/**
 * LRU (Least Recently Used) 缓存类
 * 用于限制缓存大小,自动删除最久未使用的数据
 */
export default class LRUCache {
  constructor(capacity) {
    this.capacity = capacity
    this.cache = new Map()
  }

  /**
   * 获取缓存值,同时将该值移到最新使用位置
   * @param {string|number} key 缓存键
   * @returns {any} 缓存值
   */
  get(key) {
    if (!this.cache.has(key)) return undefined

    // 获取值的同时更新位置(删除后重新插入到最后)
    const value = this.cache.get(key)
    this.cache.delete(key)
    this.cache.set(key, value)
    return value
  }

  /**
   * 设置缓存值
   * @param {string|number} key 缓存键
   * @param {any} value 缓存值
   */
  set(key, value) {
    if (this.cache.has(key)) {
      // 已存在的键值更新到最后
      this.cache.delete(key)
    } else if (this.cache.size >= this.capacity) {
      // 超出容量，删除最久未使用的(第一个)
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, value)
  }

  /**
   * 检查是否存在缓存值
   * @param {string|number} key 缓存键
   * @returns {boolean}
   */
  has(key) {
    return this.cache.has(key)
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
  }
}
