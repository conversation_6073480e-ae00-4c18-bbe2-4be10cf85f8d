/* eslint-disable no-restricted-syntax */
/*
 * @Author: guowy
 * @Date: 2020-03-30 11:43:03
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-07-27 17:55:12
 */
/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/** 将对象转换为url参数形式
* @property {Object} param 将要转换为URL参数的字符串对象
* @property {String} key URL 参数字符串的前缀
* @property {Boolean} encode 是否进行URL编码，默认为true
* @return {String} URL参数字符串
*/
export function obj2Param(param, key, encode) {
  if (param == null) return ''
  let paramStr = ''
  const t = typeof (param)
  if (t === 'string' || t === 'number' || t === 'boolean') {
    paramStr += `&${key}=${(encode == null || encode) ? encodeURIComponent(param) : param}`
  } else {
    // eslint-disable-next-line guard-for-in
    for (const i in param) {
      const k = key == null ? i : key + (param instanceof Array ? `[${i}]` : `.${i}`)
      paramStr += obj2Param(param[i], k, encode)
    }
  }
  return paramStr
}

export function exportFile(url, fileName) {
  const link = document.createElement('a')
  link.style.display = 'none'
  const num = Math.floor(Math.random() * (100 - 1 + 1)) + 1
  link.setAttribute('id', `file-${num}`)
  // link.src = `${process.env.VUE_APP_BASE_API}/${url}`
  // link.setAttribute('href', `${process.env.VUE_APP_BASE_API}/${url}`)
  if (fileName) {
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
  } else {
    link.setAttribute('href', `${process.env.VUE_APP_BASE_API}/${url}`)
  }

  // link.setAttribute('target', '_blank')
  // link.setAttribute('class', `fileBox`)
  // document.body.appendChild(link)
  link.click()
  link.remove()
}

export async function exportPic(imageUrl, fileName) {
  const response = await fetch(imageUrl)
  const blob = await response.blob()
  // 创建下载链接
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName || '原图.jpg'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  // 释放 Blob URL
  window.URL.revokeObjectURL(url)
}

export function exportXml(xml) {
  const url = window.URL.createObjectURL(
    new Blob([xml], { type: 'text/xml' }),
  )
  // 然后就可以创建a标签 最后下载下来了
  const link = document.createElement('a')
  // 不显示链接
  link.style.display = 'none'
  link.href = url
  // 设置链接属性
  link.setAttribute('download', '')
  // 点击链接
  document.body.appendChild(link)
  link.click()
}

export function imageToBase64(img) {
  const canvas = document.createElement('canvas')
  canvas.width = img.width
  canvas.height = img.height
  const ctx = canvas.getContext('2d')
  ctx.drawImage(img, 0, 0, img.width, img.height)
  const ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()
  const dataURL = canvas.toDataURL(`image/jpeg${ext}`)
  return dataURL
}

/**
 * 从Content-Disposition头中解析文件名
 * @param {string} disposition Content-Disposition头的值，例如："attachment;filename=%E5%B7%A1%E6%9F%A5%E7%97%85%E5%AE%B3.xlsx"
 * @returns {string} 解码后的文件名
 */
export function parseContentDisposition(disposition) {
  if (!disposition) return ''
  
  // 提取filename部分
  const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
  const matches = filenameRegex.exec(disposition)
  if (matches && matches[1]) {
    // 移除可能存在的引号
    let filename = matches[1].replace(/['"]/g, '')
    
    // 解码URL编码的文件名
    try {
      // 如果文件名是URL编码的，先解码
      return decodeURIComponent(filename)
    } catch (e) {
      // 如果解码失败，尝试使用escape/unescape方法处理中文编码
      try {
        return unescape(filename)
      } catch (e2) {
        // 如果仍然失败，返回原始文件名
        console.error('解析文件名失败', e2)
        return filename
      }
    }
  }
  return ''
}

/**
 * 从Content-Disposition头中获取文件扩展名
 * @param {string} disposition Content-Disposition头的值
 * @param {string} defaultExt 默认扩展名（不含点号）
 * @returns {string} 文件扩展名（不含点号）
 */
export function getFileExtension(disposition, defaultExt = 'docx') {
  if (!disposition) return defaultExt
  
  const filename = parseContentDisposition(disposition)
  if (!filename) return defaultExt
  
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return defaultExt
  }
  
  return filename.substring(lastDotIndex + 1)
}

export function base64ToFile(urlData, fileName) {
  const arr = urlData.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bytes = atob(arr[1]) // 解码base64
  let n = bytes.length
  const ia = new Uint8Array(n)
  while (n--) {
    ia[n] = bytes.charCodeAt(n)
  }
  return new File([ia], fileName, { type: mime })
}

export const GLOBAL_VARIATE = {
  colorA: '#8CD52C',
  colorB: '#40D3EA',
  colorC: '#FFE60D',
  colorD: '#FF982C',
  colorE: '#FF1C1C',
  colorA_GLR: '#03901B',
  colorB_GLR: '#69D10E',
  colorC_GLR: '#CFEB22',
  colorD_GLR: '#FF982C',
  colorE_GLR: '#FF1C1C',
}
