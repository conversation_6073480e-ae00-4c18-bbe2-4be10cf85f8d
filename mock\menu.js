/*
 * @Author: guowy
 * @Date: 2020-06-11 17:35:18
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-10 23:08:19
 */
const { userMenus } = require('./router.js')
const rootMenu = {
  id: 1,
  name: 'main',
  icon: '',
  title: '主菜单',
  path: '',
  component: '',
  hidden: false
}

const temp = Object.assign({}, rootMenu)
temp.children = userMenus
const rootMenuArr = []
rootMenuArr.push(rootMenu)
module.exports = [
  // 获取用户菜单
  {
    url: '/menus/[a-z]*/users/[0-9]',
    type: 'get',
    response: config => {
      return {
        status: 200,
        payload: temp
      }
    }
  }
]

