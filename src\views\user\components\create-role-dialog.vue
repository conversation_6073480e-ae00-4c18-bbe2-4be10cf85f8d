<template>
  <el-dialog
    :title="form.id ? '修改角色' : '创建角色'"
    :visible.sync="dialogVisible"
    width="40%"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入角色名称" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">{{ form.id ? '保存' : '创建' }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createRole, updateRole } from '@/api/auth'

export default {
  name: 'CreateRoleDialog',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        id: undefined,
        name: '',
        anonRole: false,
        authcRole: false,
      },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入角色描述', trigger: 'blur' }],
      },
    }
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      if (row) {
        this.form = { ...row }
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
      this.form = {
        id: undefined,
        name: '',
        anonRole: false,
        authcRole: false,
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true
        if (this.form.id) {
          await updateRole(this.form)
          this.$message.success('修改成功')
        } else {
          await createRole(this.form)
          this.$message.success('创建成功')
        }
        this.$emit('refresh')
        this.handleClose()
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
  },
}
</script>

<style scoped>
.el-form {
  padding: 20px;
}
</style>
