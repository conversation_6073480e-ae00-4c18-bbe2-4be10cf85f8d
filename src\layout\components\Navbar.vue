<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <template v-if="avatar">
            <img :src="avatar" class="user-avatar">
          </template>
          <template v-else>
            <img :src="defaultAvatar" class="user-avatar">
          </template>
          <!-- <i class="el-icon-caret-bottom" /> -->
          <span class="user-name">{{ admin ? username : workUnit }}</span>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/password/change">
            <el-dropdown-item>
              修改密码
            </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="handleLogout">
            <span style="display:block;">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import defaultAvatar from '@/assets/default-avatar.jpg'
import { logout } from '@/utils/auth'
export default {
  components: {
    Breadcrumb,
    Hamburger,
  },
  data() {
    return {
      defaultAvatar: `${defaultAvatar}?${+new Date()}`,
      isFullscreen: false,
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'username',
    ]),
    ...mapState({
      admin: (state) => state.account.admin,
      workUnit: (state) => state.account.workUnit,
    }),
 
  },
  mounted() {
  },
  beforeDestroy() {
    
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    handleLogout() {
      logout()
    },
  },
}
</script>
<style lang="scss" scoped>
.navbar {
  height: 64px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  transition: all .2s ease-in-out;
  .hamburger-container {
    line-height: 62px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }
  .breadcrumb-container {
    float: left;
  }
  .right-menu {
    float: right;
    height: 100%;
    line-height: 64px;
    &:focus {
      outline: none;
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      &.hover-effect {
        cursor: pointer;
        transition: background .3s;
        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
    .fullscreen-icon {
      font-size: 22px;
      position: relative;
      top: 3px;
    }
    .avatar-container {
      margin-right: 12px;
      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        .user-avatar {
          cursor: pointer;
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }
        .user-name {
          float: right;
          font-size: 14px;
          padding-left: 12px;
          margin-top: -5px;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
