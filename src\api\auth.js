import request from '@/utils/request'

/**
 * 获取角色列表及其权限
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回角色及权限数据
 */
export function getRoles(params) {
  return request({
    url: 'authz/roles/permissions',
    method: 'get',
    params,
  })
}

/**
 * 获取所有权限列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回权限列表数据
 */
export function getPermissions(params) {
  return request({
    url: 'authz/permissions',
    method: 'get',
    params,
  })
}

/**
 * 复制角色
 * @param {Object} data - 请求数据
 * @param {number} data.copiedRoleId - 被复制的角色ID
 * @param {string} data.name - 新角色名称
 * @returns {Promise} 返回复制结果
 */
export function copyRole(data) {
  return request({
    url: '/authz/roles/copy',
    method: 'post',
    data,
  })
}

/**
 * 为角色分配权限
 * @param {Object} data - 请求数据
 * @param {number} data.roleId - 角色ID
 * @param {string[]} data.permNames - 权限名称列表
 * @returns {Promise} 返回分配结果
 */
export function assignRolePermission(data) {
  return request({
    url: `/authz/roles/${data.roleId}/permissions/assign`,
    method: 'patch',
    data: data.permNames,
  })
}

/**
 * 移除角色的权限
 * @param {Object} data - 请求数据
 * @param {number} data.roleId - 角色ID
 * @param {string[]} data.permNames - 权限名称列表
 * @returns {Promise} 返回移除结果
 */
export function unassignRolePermission(data) {
  return request({
    url: `/authz/roles/${data.roleId}/permissions/unassign`,
    method: 'patch',
    data: data.permNames,
  })
}

/**
 * 创建角色
 * @param {Object} data - 请求数据
 * @param {string} data.name - 角色名称
 * @param {boolean} data.anonRole - 是否为匿名角色
 * @param {boolean} data.authcRole - 是否为认证角色
 * @returns {Promise} 返回创建结果
 */
export function createRole(data) {
  return request({
    url: '/authz/roles',
    method: 'post',
    data,
  })
}

/**
 * 修改角色
 * @param {Object} data - 请求数据
 * @param {number} data.id - 角色ID
 * @param {string} data.name - 角色名称
 * @param {boolean} data.anonRole - 是否为匿名角色
 * @param {boolean} data.authcRole - 是否为认证角色
 * @returns {Promise} 返回修改结果
 */
export function updateRole(data) {
  return request({
    url: `/authz/roles/${data.id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除角色
 * @param {number} id - 角色ID
 * @returns {Promise} 返回删除结果
 */
export function deleteRole(id) {
  return request({
    url: `/authz/roles/secure/${id}`,
    method: 'delete',
  })
}
