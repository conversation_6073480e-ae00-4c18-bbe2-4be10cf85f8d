/*
 * @Author: wangyj
 * @Date: 2022-08-30 17:45:39
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-02-22 13:56:30
 */

import request from '@/utils/request'

export function getDataList(query) {
  return request({
    url: '/inspect-tasks',
    method: 'get',
    params: query,
  })
}

export function getDevicesDetail(query) {
  return request({
    url: '/devices/detail',
    method: 'get',
    params: query,
  })
}
export function getDataDetail(id) {
  return request({
    url: `/inspect-tasks/${id}`,
    method: 'get',
  })
}
export function deleteData(id) {
  return request({
    url: `/inspect-tasks/${id}`,
    method: 'delete',
  })
}

export function getOriginalRoadImages(params) {
  return request({
    url: '/inspect-tasks/road_image',
    method: 'get',
    params,
  })
}

// 病害一览
export function getDiseaseList(query) {
  return request({
    url: '/damages',
    method: 'get',
    params: query,
  })
}

export function correctDisease(data) {
  return request({
    url: `/damages/${data.id}`,
    method: 'put',
    data,
  })
}

export function deleteDisease(id) {
  return request({
    url: `/damages/${id}`,
    method: 'delete',
  })
}

export function getRoadStatises(query) {
  return request({
    url: '/road-statises',
    method: 'get',
    params: query,
  })
}
export function getRoadStatisesRoute(query) {
  return request({
    url: '/road-statises/route',
    method: 'get',
    params: query,
  })
}

export function getGLRStatises(query) {
  return request({
    url: '/road-green-rate-statises',
    method: 'get',
    params: query,
  })
}

// 获取单位及关联设备选项列表
export function getTaskWorkUnits() {
  return request({
    url: '/devices/queryWorkUnits',
    method: 'get',
  })
}

// 获取所有设备选项列表
export function getTaskDevices() {
  return request({
    url: '/inspect-tasks/queryInspectTaskDevices',
    method: 'get',
  })
}

// 数据刷新
export function refreshData(id) {
  return request({
    url: `/inspect-tasks/${id}/refresh`,
    method: 'post',
    timeout: undefined,
  })
}

/**
  *
  *@param {
  * "taskId": 10262,  //任务id
  * "accuracyList": [ //调用NECAS病害识别模型时不同病害的 置信度设置
  * 沥青模型 7个 : [ ~,横向裂缝,龟裂,坑槽,井盖,块状裂缝,条状修补]
  * 水泥模型 4个 : [ 裂缝,坑洞,破碎板,露骨]
  * 0.1,
  * 0.1,
  * 0.1,
  * 0.1,
  * 0.1,
  * 0.1,
  * 0.1
  *  ],
  *  "modelType": 1 //模型类型 1:沥青路模型  2、水泥路模型
  * } data
  * @returns
*/

// 病害刷新
export function refreshDisease(data) {
  return request({
    url: `/task-photo-remove-dups/applyRoadModel`,
    method: 'post',
    timeout: undefined,
    data,
  })
}

export function getDiseaseQueryProgresss(query) {
  return request({
    url: `/damage-model-identify-records/queryProgress`,
    method: 'get',
    params: query,
  })
}

// 回放
export function getRoadPictures(query) {
  const { cancelToken, ...params } = query || {}
  return request({
    url: `/image-infers/pictures`,
    method: 'get',
    params,
    timeout: undefined,
    cancelToken,
  })
}

export function exportDoc(data) {
  return request({
    url: `/road-statises/${data.taskId}/exportDoc`,
    method: 'post',
    timeout: undefined,
    data,
    responseType: 'blob',
  })
}
export function exportSummaryDoc(data) {
  return request({
    url: `/road-statises/${data.taskId}/exportSummaryDoc`,
    method: 'post',
    timeout: undefined,
    data,
    responseType: 'blob',
  })
}

// 病害一览（按距离）
export function getDiseaseListByDistance(query) {
  return request({
    url: '/damages/mergeByDistance',
    method: 'get',
    params: query,
  })
}

// 获取数据图集列表--去重后
export function getPicturesList(query) {
  const { cancelToken, ...params } = query || {}
  return request({
    url: '/task-photo-remove-dups',
    method: 'get',
    params,
    timeout: undefined,
    cancelToken,
  })
}

// 获取数据图集列表--抓拍图集
export function getPicturesListByCapture(query) {
  const { cancelToken, ...params } = query || {}
  console.log('抓拍图集- cancelToken', cancelToken)
  return request({
    url: '/capture-imageses',
    method: 'get',
    params,
    cancelToken,
  })
}
// 获取数据图集列表--所有图片
export function getAllPicturesList(query) {
  return request({
    url: '/task-photos',
    method: 'get',
    params: query,
    timeout: undefined,
  })
}

// 景观图集
export function getLandscapeList(query) {
  const { cancelToken, ...params } = query || {}
  return request({
    url: '/task-landscape-photos',
    method: 'get',
    params,
    cancelToken
  })
}

// 修改巡检信息
export function updateDetection(data) {
  return request({
    url: `/inspect-tasks/${data.id}`,
    method: 'put',
    data,
  })
}

// 绿视率图集
export function getGLR(query) {
  return request({
    url: '/task-photo-green-rates',
    method: 'get',
    params: query,
  })
}

// 绿视率图集--模型调用
export function applyGreenRateModal(data) {
  return request({
    url: `/task-photo-remove-dups/applyGreenRateModel`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

// 模型识别类型列表 分类（classification）：1 病害 2 道路资产
export function getIdentifyType(query) {
  return request({
    url: '/model-identify-type-dicts',
    method: 'get',
    params: query,
  })
}

// 人工标注
export function annotateDamages(data) {
  return request({
    url: `/task-photo-remove-dups/updateDamages`,
    method: 'post',
    data,
  })
}

// 模型面积计算标定
export function calibration(data) {
  return request({
    url: `/recog/calibration`,
    method: 'post',
    data,
  })
}

// 图像病害识别
export function recogRoadDamage(data) {
  return request({
    // baseURL: 'http://114.115.246.148:5002',
    // url: `/recog/road_damage?imageThres=${data.imageThres}`,
    url: `/recog/recog/road_damage?imageThres=${data.imageThres}`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    timeout: undefined,
    method: 'post',
    data: data.formData,
  })
}

// 查询图像标定参数配置
export function getPhotoMarkConfig(query) {
  return request({
    url: '/device-photo-mark-configs/queryPhotoMarkConfig',
    method: 'get',
    params: query,
  })
}

// 配置标定参数
export function setPhotoMarkConfig(data) {
  return request({
    url: `/device-photo-mark-configs/setPhotoMarkConfig`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

// 刷新病害面积
export function refreshArea(data) {
  return request({
    url: `/inspect-tasks/${data.id}/recountArea`,
    method: 'post',
    timeout: undefined,
  })
}

// 查询病害面积刷新进度
export function getAreaRefreshProgress(query) {
  return request({
    url: '/damage-area-recount-records/queryProgress',
    method: 'get',
    params: query,
  })
}

// nec图像病害识别 沥青路
export function recogRoadDamageByNec(data) {
  return request({
    // baseURL: 'http://39.105.177.38:3389',
    // url: `http://39.105.177.38:3389/roadcheck/v1/detectCrack?token=22922e003b8ac3fe67bd9ff0efdec541`,
    url: `/necas/roadcheck/v1/detectCrack?token=22922e003b8ac3fe67bd9ff0efdec541`,
    // url: `/models/detectCrack`,
    timeout: undefined,
    method: 'post',
    data,
  })
}

// nec图像病害识别 水泥路
export function recogRoadDamageByNecCement(data) {
  return request({
    // url: `http://111.198.21.194:18021/roadcheck/v1/detectCrackCement?token=22922e003b8ac3fe67bd9ff0efdec541`,
    url: `/necas/roadcheck/v1/detectCrackCement?token=22922e003b8ac3fe67bd9ff0efdec541`,
    timeout: undefined,
    method: 'post',
    data,
  })
}

// 定位病害一览图片在图集中的位置
export function locatePictures(query) {
  return request({
    url: `/task-photo-remove-dups/queryPhotoLocation`,
    method: 'get',
    params: query,
  })
}

// 任务裁切
export function cutTask(data) {
  return request({
    url: `/inspect-tasks/${data.id}/split`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

// 图集单个标注导出
export function exportSinglePic(query) {
  return request({
    url: `/task-photo-remove-dups/downloadSingleXml`,
    method: 'get',
    params: query,
  })
}

// 模型识别标注导出
export function exportModelPic(query) {
  return request({
    url: `/task-photo-remove-dups/downloadModelXml`,
    method: 'get',
    params: query,
  })
}

// 数据推送
export function pushData(id) {
  return request({
    url: `/inspect-tasks/${id}/pushTrackData`,
    method: 'post',
    timeout: undefined,
  })
}

// 推送病害至永峰
export function pushDamageToYongFeng(id) {
  return request({
    url: `/damages/${id}/pushDamageToYongFeng`,
    method: 'post',
  })
}
