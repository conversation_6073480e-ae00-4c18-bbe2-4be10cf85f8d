<!--
 * @Author: guowy
 * @Date: 2020-11-10 18:49:23
 * @LastEditors: guowy
 * @LastEditTime: 2021-02-20 10:50:03
-->
<template>
  <div :class="['robu-filter', multiline&&isExpend?'':'single', className]">
    <template v-if="showSearchBtn">
      <el-row :gutter="16">
        <slot />
      </el-row>
      <el-col :span="8" class="btns" align="right">
        <el-button type="primary" size="small" @click="search">查询</el-button>
        <el-button size="small" @click="reset">重置</el-button>
        <template v-if="multiline">
          <el-button v-if="isExpend" type="text" @click="toggle">收起<i class="el-icon-arrow-up" /></el-button>
          <el-button v-else type="text" @click="toggle">展开<i class="el-icon-arrow-down" /></el-button>
          <template v-if="!isExpend">
            <slot name="line">
              <span class="line">|</span>
            </slot>
            <slot name="operationBtn" />
          </template>
        </template>
        <template v-else>
          <slot name="line">
            <span class="line">|</span>
          </slot>
          <slot name="operationBtn" />
        </template>
      </el-col>
    </template>

    <div
      v-if="multiline&&isExpend || !multiline || !showSearchBtn"
      :align="showSearchBtn?'left':'right'"
      style="clear:both"
    >
      <slot name="operationBtn" />
    </div>
  </div>
</template>

<script>

export default {
  name: 'TableFilter',
  props: {
    // 检索条件是否是多行
    multiline: {
      type: Boolean,
      default: true,
    },
    showSearchBtn: {
      type: Boolean,
      default: true,
    },
    className: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isExpend: false,
    }
  },
  methods: {
    toggle() {
      this.isExpend = !this.isExpend
    },
    reset() {
      this.$emit('reset')
    },
    search() {
      this.$emit('search')
    },
  },
}
</script>

<style lang="scss">
  .robu-filter{
    overflow: hidden;
    .btns{
      float: right;
      margin-bottom: 0;
    }
    &.single{
      height: 32px;

      .btns{
        background: #ffffff;
        position: absolute;
        top: -4px;
        right: 0;
      }
    }
    .el-col{
      margin-bottom: 16px;
    }
  }
</style>
