/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:55
 * @LastEditors: guowy
 * @LastEditTime: 2021-04-21 17:38:05
 */
import store from '@/store'
import router from '@/router'
import axios from 'axios'
import Message from 'element-ui/packages/message'
import RequestCancelManager from './RequestCancelManager'

const options = {
  toLogin: () => {
    store.dispatch('user/resetToken').then(() => {
      localStorage.removeItem('refresh_token')
      router.push({ path: '/login' })
    })
  },
}

export default function request(_option) {
  return new Promise((resolve, reject) => {
    $axios(options)(_option).then((res) => {
      resolve(res)
    }).catch((err) => {
      // 检查是否是取消请求的错误
      if (RequestCancelManager.isCancel(err)) {
        console.log('Request canceled in main function:', err.message)
        // 对于取消的请求，返回一个特定结构的空结果而不是抛出错误
        resolve({
          payload: {
            content: [],
            totalElements: 0,
          },
          status: 200, // 确保status字段存在且为成功状态
          requestCancelled: true, // 标记为请求被取消
        })
      } else {
        reject(err)
      }
    })
  })
}

const showError = (message) => {
  Message({
    message,
    type: 'error',
    duration: 5 * 1000,
  })
}
const defaultOption = {
  baseURL: process.env.VUE_APP_BASE_API ? process.env.VUE_APP_BASE_API : '',
  timeout: 30 * 1000, // 请求超时设置
  beforeSend: undefined,
  toLogin: undefined,
}

function $axios(options) {
  const _option = Object.assign(defaultOption, options)

  const errorHandle = (status, errorMsg) => {
    switch (status) {
    case 400:
      showError('输入数据无效')
      break
    case 401:
      if (_option.toLogin) {
        _option.toLogin()
      }
      showError(errorMsg || '登录信息失效，请重新登陆')
      break
    case 403:
      if (_option.toLogin) {
        _option.toLogin()
      }
      showError('当前用户无权访问，请使用其他用户登录')
      break
    case 404:
      showError('请求的数据不存在')
      break
    case 408:
      showError('请求超时')
      break
    case 500:
      showError(errorMsg || '服务器异常')
      break
    case 502:
      showError(errorMsg || '网关出错，')
      break
    case 503:
      showError('服务不可用')
      break
    case 505:
      showError('http版本不支持该请求')
      break
    default:
      showError(errorMsg)
      break
    }
  }

  // 创建一个axios实例
  const service = axios.create({
    baseURL: _option.baseURL, // url = base url + request url
    timeout: _option.timeout,
    headers: { 'content-type': 'application/json' },
  })

  // 请求拦截器
  service.interceptors.request.use(
    (config) => {
      // 在发送请求之前做一些事情
      if (_option.beforeSend) {
        _option.beforeSend(config)
      }

      // 如果传入的配置中有cancelId，则注册取消令牌
      if (config.cancelId) {
        config.cancelToken = RequestCancelManager.create(config.cancelId, config.cancelGroup)
      }

      return config
    },
    (error) => {
      // 请求错误
      console.log(error)
      return Promise.reject(error)
    },
  )

  // 响应拦截器
  service.interceptors.response.use(
    /**
     * 如果要获取http信息（例如标题或状态）
     * 请返回  response => response
    */

    /**
     * 通过自定义代码确定请求状态
     * 这只是一个例子
     * 您还可以通过HTTP状态代码来判断状态
     */
    (response) => {
      let res
      // 如果是文件下载请求（responseType为blob），直接返回完整响应对象
      if (response.config.responseType === 'blob') {
        return {
          payload: response.data,
          headers: response.headers,
          status: response.status,
          statusText: response.statusText
        }
      }
      // IE9时response.data是undefined，因此需要使用response.request.responseText(Stringify后的字符串)
      if (response.config.url.includes('/authc/login')) {
        // 检查响应中是否包含refreshToken
        if (response.data && response.data.payload && response.data.payload.refreshToken) {
          // 保存refreshToken到localStorage
          localStorage.setItem('refresh_token', response.data.payload.refreshToken)
          console.log('RefreshToken已保存:', response.data.payload.refreshToken)
        }
      }

      if (response.data === undefined) {
        res = response.request.responseText
      } else {
        res = response.data
      }
      if (res.status && res.status === 200) {
        if (res.errorCode) {
          const msg = res.msg ? res.msg : '服务器异常'
          showError(msg)
          return Promise.reject(res)
        }
        return res
      } if (res.status === 401) {
        if (_option.toLogin) {
          _option.toLogin()
        }
        showError('登录信息失效，请重新登陆')
      } else if (res.status === 403) {
        if (_option.toLogin) {
          _option.toLogin()
        }
        showError('当前用户无权访问，请使用其他用户登录')
      } else {
        if (res.msg) {
          showError(res.msg)
        }
        return res
      }
    },
    (error) => {
      const { response } = error
      if (response) {
        errorHandle(response.status, response.data.msg)
        if (response.status !== 401) {
          return Promise.reject(response)
        }
      } else {
        if (!RequestCancelManager.isCancel(error)) showError('请检查网络连接')
        return Promise.reject(error)
      }
    },
  )

  return service
}

// 导出取消请求管理器，方便直接使用
export { RequestCancelManager }
