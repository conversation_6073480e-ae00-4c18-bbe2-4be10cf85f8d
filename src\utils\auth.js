import store from '@/store'
import router from '@/router' // 导入router实例

// 定义登录路径常量
const loginPath = '/login'

/**
 * 退出登录方法
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 退出成功的回调
 * @param {Function} options.onError - 退出失败的回调
 */
export function logout(options = {}) {
  console.log('退出登录')
  const { onSuccess, onError } = options

  // 清除refresh_token
  localStorage.removeItem('refresh_token')

  return store.dispatch('user/logout')
    .then(() => {
      // 重定向到登录页
      router.push(loginPath) // 使用导入的router实例

      // 使用导入的store实例
      store.commit('account/SET_PLYS', [])
      store.commit('account/SET_PLYPATHS', null)
      store.commit('detail/DEL_TASK_DATA_OBJ', 'all')
      store.dispatch('unit/resetState')

      // 如果提供了成功回调，则执行
      if (typeof onSuccess === 'function') {
        onSuccess()
      }
    })
    .catch((error) => {
      console.error('退出登录失败:', error)

      // 如果提供了失败回调，则执行
      if (typeof onError === 'function') {
        onError(error)
      }
    })
}
