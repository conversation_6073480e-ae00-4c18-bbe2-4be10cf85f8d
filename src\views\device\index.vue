/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:00:57
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-02-14 16:27:49
 */
<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <UnitSelect  v-model="listQuery.workUnitId" useNodeId placeholder="设备单位" clearable filterable @input="handleWorkUnitChange"/>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-select v-model="listQuery.deviceName" placeholder="设备名称" clearable filterable>
            <el-option v-for="device in deviceOptions" :key="device.id" :label="device.deviceName" :value="device.deviceName" />
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input v-model="listQuery.deviceKey" placeholder="设备ID" clearable />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">
          添加设备
        </el-button>
      </el-row>  
      
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="设备单位" prop="workUnit" />
      <el-table-column align="center" label="设备名称" prop="deviceName" />
      <el-table-column align="center" label="设备ID" prop="deviceKey" />
      <el-table-column align="center" label="设备BSSID" prop="deviceBssid" />
      <el-table-column align="center" label="设备类型">
        <template slot-scope="{row}">
          {{ row.deviceModel === '1' ? '慢行设备' : '车载设备' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="车牌号" prop="plateNum" width="110"/>
      <el-table-column align="center" label="流量卡号" prop="cardIccid" class-name="card-iccid-column" />
      <el-table-column align="center" label="设备最后运行时间">
        <template slot-scope="{row}">
          <el-button v-if="row.latestRunTime" type="text" @click="handleLookTransmitInfo(row)">{{ new Date(row.latestRunTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="运行策略" prop="deviceKey" width="100">
        <template slot-scope="{row}">
          <el-button type="text" @click="handleConfig(row)">配置运行策略</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row,$index}">
          <el-button v-if="row.videoUrl" type="text" @click="handleVideo(row, $index)">实时视频</el-button>
          <el-button type="text" @click="handleUpdate(row, $index)">修改</el-button>
          <el-button type="text" @click="handleDelete(row, $index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <create-dialog ref="createDialog" :work-unit="listQuery.workUnitId" @refreshTable="refreshTable" />
    <ConfigDialog ref="configDialog" />
    <DataDialog ref="dataDialog" />
    <VideoDialog ref="videoDialog" />
  </div>
</template>

<script>
import {
  getDevices, deleteDevice, getDeviceWorkUnits, getDeviceNames,
} from '@/api/device'
import Pagination from '@/components/Pagination/index.vue'
import { mapState } from 'vuex'
import CreateDialog from './components/create-dialog.vue'
import ConfigDialog from './components/config-dialog.vue'
import DataDialog from './components/data-dialog.vue'
import VideoDialog from './components/video-dialog.vue'
export default {
  name: 'Device',
  components: {
    Pagination, 
    CreateDialog, 
    ConfigDialog, 
    DataDialog, 
    VideoDialog,
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnitId: null,
        deviceName: '',
        deviceKey: '',
      },
      temp: {},
      configData: {},
      unitOptions: [], // 单位下拉选择
      unitDisabled: false,
      deviceOptions: [], // 设备名称下拉选择
      allDeviceName: [], // 所有设备名称
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      workUnitId: (state) => state.account.workUnitId,
    }),
  },
  watch: {
    $route: {
      handler(val, oldVal) {
        const { workUnitId } = this.$route.query
        if (this.$route.path === '/device' && workUnitId) {
          if (workUnitId !== this.listQuery.workUnitId) {
            this.listQuery.workUnitId = workUnitId
            const arr = this.unitOptions.filter((item) => item.workUnitId === workUnitId)
            this.deviceOptions = arr[0].devices
            this.getList()
          }
        }
      },
      // 深度观察监听
      deep: true,
    },
  },

  async created() {
    const { workUnitId } = this.$route.query
    await this.getWorkUnit()
    if (!this.admin) {
      console.log('this.$route.query', this.$route.query);
      this.listQuery.workUnitId = this.workUnitId
      this.unitDisabled = true
      this.deviceOptions = this.allDeviceName
    } else {
      if (workUnitId) {
        this.listQuery.workUnitId = workUnitId
        const arr = this.unitOptions.filter((item) => item.workUnitId === workUnitId)
        this.deviceOptions = arr[0].devices
      } else {
        this.deviceOptions = this.allDeviceName
      }
    }

    this.getList()
  },
  methods: {
    async getWorkUnit() {
      const { status, payload } = await getDeviceWorkUnits()
      if (status === 200) {
        this.unitOptions = payload
        const devices = this.admin 
        ? this.unitOptions.flatMap(unit => unit.devices || [])
        : (this.unitOptions.find(unit => unit.workUnitId === this.workUnitId)?.devices || [])
      
        this.deviceOptions = devices;
        this.allDeviceName = [...devices];
      }

    },
    handleWorkUnitChange(value) {
      this.listQuery.deviceName = ''
      if (value) {
        const unitArr = this.unitOptions.filter((item) => item.workUnitId === value)
        this.deviceOptions = unitArr[0].devices
      } else {
        this.deviceOptions = this.allDeviceName
      }
    },
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, deviceName, workUnitId, deviceKey,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        workUnitId: workUnitId || null,
        deviceName: deviceName.trim() === '' ? null : deviceName,
        deviceKey: deviceKey.trim() === '' ? null : deviceKey,
      }
      getDevices(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleCreate() {
      this.$refs.createDialog.show()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该设备吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteDevice(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getList()
      })
    },
    handleVideo(row) {
      row.videoUrl && this.$refs.videoDialog.show(row.videoUrl)
    },
    handleUpdate(row) {
      this.$refs.createDialog.show(row)
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleConfig(row) {
      this.$refs.configDialog.show(row.id)
    },
    handleLookTransmitInfo(row) {
      this.$refs.dataDialog.show(row.deviceKey)
    },
  },
}
</script>
<style lang="scss" scoped>
  ::v-deep.el-table {
    .card-iccid-column{
      -webkit-touch-callout: text;
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
      .cell {
        -webkit-touch-callout: auto;
        -webkit-user-select: auto;
        -khtml-user-select: auto;
        -moz-user-select: auto;
        -ms-user-select: auto;
        user-select: auto;
      }
    }
  }

</style>
