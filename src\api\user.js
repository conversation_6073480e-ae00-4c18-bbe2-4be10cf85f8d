/*
 * @Author: guowy
 * @Date: 2020-05-27 11:03:57
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-14 17:02:08
 */

import request from '@/utils/request'

export function getUsers(query) {
  return request({
    url: '/users',
    method: 'get',
    params: query,
  })
}

export function getUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'get',
  })
}

export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data,
  })
}

export function updateUser(data) {
  return request({
    url: `/users/${data.id}`,
    method: 'put',
    data,
  })
}

export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'delete',
  })
}

// 查询当前登录用户信息
export function getCurrentUserInfo() {
  return request({
    url: '/users/currentUserInfo',
    method: 'get',
  })
}
