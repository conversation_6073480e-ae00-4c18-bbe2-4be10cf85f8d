<template>
  <el-dialog
    :title="dialog.title"
    :visible.sync="dialog.isShowDialog"
    width="60%"
    align-center
    top="10vh"
    class="assign-dialog"
  >
    <div class="assign-dialog-container">
      <ul class="assigned-list">
        <li>
          <p>已分配权限</p>
          <p class="short">删除</p>
        </li>
        <li v-for="item in assignedPermissions" :key="item.id">
          <p>{{ item.title }}</p>
          <p class="short">
            <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="onRemovePermission(item)" />
          </p>
        </li>
      </ul>
      <ul class="unassigned-list">
        <li>
          <p class="short">分配</p>
          <p>待分配权限</p>
        </li>
        <li v-for="item in unassignedList" :key="item.id">
          <p class="short">
            <el-button size="mini" type="primary" icon="el-icon-back" circle @click="onAssignPermission(item)" />
          </p>
          <p>{{ item.title }}</p>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
import { assignRolePermission, unassignRolePermission } from '@/api/auth'

export default {
  name: 'AssignDialog',
  data() {
    return {
      dialog: {
        isShowDialog: false,
        title: '分配权限',
      },
      role: {},
      assignedPermissions: [],
      permissions: [],
    }
  },
  computed: {
    unassignedList() {
      const ids = this.assignedPermissions.map((item) => item.name)
      return this.permissions.filter((item) => !ids.includes(item.name))
    },
  },

  methods: {
    show({ role, assignedPermissions, permissions }) {
      this.role = role
      this.assignedPermissions = assignedPermissions
      this.permissions = permissions
      this.dialog.isShowDialog = true
    },
    async onAssignPermission(perm) {
      const data = {
        roleId: this.role.id,
        permNames: [perm.name],
      }
      await assignRolePermission(data)
      this.assignedPermissions.push(perm)
      this.$message.success('权限分配成功')
      this.$emit('refresh')
    },
    async onRemovePermission(perm) {
      const data = {
        roleId: this.role.id,
        permNames: [perm.name],
      }
      await unassignRolePermission(data)
      this.$message.success('权限移除成功')
      const index = this.assignedPermissions.findIndex((p) => p.name === perm.name)
      this.assignedPermissions.splice(index, 1)
      this.$emit('refresh')
    },
  },
}
</script>

<style lang="scss" scoped>
.assign-dialog .assign-dialog-container {
	width: 100% !important;
	display: flex;
	.assigned-list,
	.unassigned-list {
		flex: 1;
		border: 1px solid #eee;
		border-radius: 4px;
		list-style: none;
    overflow-y: auto;
    max-height: 65vh;
		li {
			display: flex;
			line-height: 40px;
			border-bottom: 1px solid #eee;
			p:first-child {
				border-right: 1px solid #eee;
			}
			p {
				width: 70%;
				text-align: center;
				&.short {
					width: 30%;
				}
			}
		}
		li:first-child {
			font-weight: 600;
		}
	}
	.assigned-list {
		margin-right: 20px;
	}
}
</style>
