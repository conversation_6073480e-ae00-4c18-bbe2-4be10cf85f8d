<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" style="width: 100%;" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-input
            v-model="listQuery.title"
            placeholder="标题"
            clearable
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-input
            v-model="listQuery.detail"
            placeholder="详情"
            clearable
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-input
            v-model="listQuery.remark"
            placeholder="备注"
            clearable
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >  
          <el-date-picker
            v-model.trim="listQuery.createTime"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            clearable
            placeholder="请选择创建时间"
          />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" plain icon="el-icon-refresh" @click="resetQuery">
          重置
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="ID" prop="id" width="110"/>
      <el-table-column align="center" label="类型" prop="type" width="130"/>
      <el-table-column align="center" label="标题" prop="title" width="160"/>
      <el-table-column align="center" label="详情" >
        <template slot-scope="{row}">
          <div class="ellipsis" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"> {{ row.detail }} </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark" width="300"/>
      <el-table-column align="center" label="创建时间" width="150">
        <template slot-scope="{row}">
          {{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="80">
        <template slot-scope="{row}">
          <el-button type="text" @click="handleDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 添加详情弹窗 -->
    <el-dialog
      title="日志详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      :append-to-body="true"
      style="top: -5vh;"
    >
      <div v-loading="detailLoading">
        <div class="detail-info">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ detailData.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">类型:</span>
            <span class="detail-value">{{ detailData.type }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">标题:</span>
            <span class="detail-value">{{ detailData.title }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">备注:</span>
            <span class="detail-value">{{ detailData.remark }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建时间:</span>
            <span class="detail-value">{{ new Date(detailData.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">详细内容:</span>
            <div class="detail-content">
              <pre class="detail-text">{{ detailData.detail }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getSystemLogs, getSystemLogDetail } from '@/api/log'

export default {
  name: 'SystemLog',
  components: { Pagination },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        createTime: null,
        type: null,
        title: null,
        detail: null,
        remark: null
      },
      // 添加详情弹窗相关的数据
      detailDialogVisible: false,
      detailLoading: false,
      detailData: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, createTime, type, 
        title, detail, remark
      } = this.listQuery
      
      const query = {
        page: currentPage - 1,
        size: pageSize,
        createTime: createTime || null,
        type: type || null,
        title: title || null,
        detail: detail || null,
        remark: remark || null,
        sort: 'createTime,desc'
      }
      getSystemLogs(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        currentPage: 1,
        pageSize: 10,
        createTime: null,
        type: null,
        title: null,
        detail: null,
        remark: null
      }
      this.getList()
    },
    // 添加处理详情的方法
    handleDetail(row) {
      this.detailDialogVisible = true
      this.detailLoading = true
      this.detailData = {} // 清空之前的数据
      
      getSystemLogDetail(row.id).then(response => {
        if (response.status === 200) {
          this.detailData = response.payload || {}
        } else {
          this.$message.error('获取详情失败')
        }
      }).catch(error => {
        console.error('获取日志详情出错:', error)
        this.$message.error('获取详情失败')
      }).finally(() => {
        this.detailLoading = false
      })
    }
  },
}
</script>

<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
}

th {
  background-color: #f2f2f2;
}


.detail-item {
  padding: 6px 0px;
  display: flex;
}

.detail-label {
  font-weight: bold;
  width: 90px;
  text-align: right;
  padding-right: 20px;
  color: #606266;
  flex-shrink: 0; /* 防止label缩小 */
}

.detail-value {
  color: #303133;
  word-break: break-all; /* 确保长文本可以换行 */
}

.detail-content {
  flex: 1;
}

.detail-text {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  width: 100%;
}

::v-deep .el-dialog__body {
  padding: 0px 20px;
}
</style>
