- hosts: "{{lookup('env','TARGET_SERVERS')}}"
  tasks:
      - name: Say hello
        debug:
            msg: "Severs: {{ lookup('env','TARGET_SERVERS') }}"

      - name: Clean directory
        file: "path={{remote_dir}} state=absent"

      - name: Create directory
        file: "path={{remote_dir}} state=directory"

      - name: Archive dist files
        archive:
          path: "{{app_src}}"
          dest: "{{app_src}}.zip"
          format: "zip"
        delegate_to: "localhost"
        become: "false"

      - name: Extract archive file to target
        unarchive:
          src: "{{app_src}}.zip"
          dest: "{{remote_dir}}"

