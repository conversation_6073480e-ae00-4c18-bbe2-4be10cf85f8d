const DevicesLatest = {
  data() {
    return {
      tableHeight: 600,
    }
  },
  mounted() {
    this.initTableHeight()
    window.addEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  deactivated() {
    window.removeEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  destroyed() {
    this.map.removeOverlay()
    window.removeEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  methods: {
    initTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = document.getElementsByClassName('app-main')[0].clientHeight - 600
      })
    },
  },
}

export default DevicesLatest
