<template>
  <el-dialog
    :title="modelType === 1 ? '沥青路面置信度设置' : '水泥路面置信度设置'"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
    :destroy-on-close="true"
    top="10vh"
  >
    <div class="dialog-box">
      <div class="cont-tool-scroll">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-if="modelType === 9" title="慢行病害" name="9">
            <el-row :gutter="30">
              <el-col v-for="(sPType) in slowPatrolType" :key="sPType.id" :span="sPType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: sPType.roadType != modelType ? 'not-allowed' : 'pointer'}"
                  :type="sPType.roadType != modelType ? 'info' : ''"
                  :effect="sPType.check ? 'dark' : 'plain'"
                  @click="modelType === 9 && handleTypeChange(sPType)"
                >
                  {{ sPType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType === 1 || modelType === 10" title="沥青路面病害" name="1">
            <el-row :gutter="30">
              <el-col v-for="(pType) in pitchDiseaseType" :key="pType.id" :span="pType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: pType.roadType == modelType || modelType == 10 ? 'pointer' : 'not-allowed'}"
                  :type="pType.roadType == modelType || modelType == 10 ? '' : 'info'"
                  :effect="pType.check ? 'dark' : 'plain'"
                  @click="(modelType === 1 || modelType === 10) && handleTypeChange(pType)"
                >
                  {{ pType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType === 2" title="水泥路面病害" name="2">
            <el-row :gutter="30">
              <el-col v-for="(cType) in cementDisesaeType" :key="cType.id" :span="cType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: cType.roadType != modelType ? 'not-allowed' : 'pointer'}"
                  :type="cType.roadType != modelType ? 'info' : ''"
                  :effect="cType.check ? 'dark' : 'plain'"
                  @click="modelType === 2 && handleTypeChange(cType)"
                >
                  {{ cType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType === 6" title="砂石路面病害" name="6">
            <el-row :gutter="30">
              <el-col v-for="(ssType) in sandstoneDisesaeType" :key="ssType.id" :span="ssType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: ssType.roadType != modelType ? 'not-allowed' : 'pointer'}"
                  :type="ssType.roadType != modelType ? 'info' : ''"
                  :effect="ssType.check ? 'dark' : 'plain'"
                  @click="modelType === 6 && handleTypeChange(ssType)"
                >
                  {{ ssType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType !== 9 && modelType !== 10" title="道路资产" name="3">
            <el-row :gutter="30">
              <el-col v-for="(rType) in allRoadAssetsType" :key="rType.engName" :span="rType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: modelType === 9 ? 'not-allowed' : 'pointer'}"
                  :type="modelType == 9 ? 'info' : ''"
                  :effect="rType.check ? 'dark' : 'plain'"
                  @click="modelType !== 9 && handleTypeChange(rType)"
                >
                  {{ rType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType !== 9 && modelType !== 10" title="路面风险" name="4">
            <el-row :gutter="30">
              <el-col v-for="(mType) in allRoadForeignMatter" :key="mType.engName" :span="mType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: modelType === 9 ? 'not-allowed' : 'pointer'}"
                  :type="modelType === 9 ? 'info' : ''"
                  :effect="mType.check ? 'dark' : 'plain'"
                  @click="modelType !== 9 && handleTypeChange(mType)"
                >
                  {{ mType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="modelType !== 9 && modelType !== 10" title="沿线设施损坏" name="5">
            <el-row :gutter="30">
              <el-col v-for="(mType) in allAlongLine" :key="mType.engName" :span="mType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: modelType === 9 ? 'not-allowed' : 'pointer'}"
                  :type="modelType === 9 ? 'info' : ''"
                  :effect="mType.check ? 'dark' : 'plain'"
                  @click="modelType !== 9 && handleTypeChange(mType)"
                >
                  {{ mType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>

          <el-collapse-item v-if="modelType === 1" title="慢行病害" name="9">
            <el-row :gutter="30">
              <el-col v-for="(sPType) in slowPatrolType" :key="sPType.id" :span="sPType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: modelType === 1 ? 'pointer' : 'not-allowed'}"
                  :type="modelType === 1 ? '' : 'info'"
                  :effect="sPType.check ? 'dark' : 'plain'"
                  @click="modelType === 1 && handleTypeChange(sPType)"
                >
                  {{ sPType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>

          <el-collapse-item v-if="modelType === 10 || modelType === 1 || modelType === 9" title="城市管理问题" name="10">
            <el-row :gutter="30">
              <el-col v-for="(cType) in cityManagementType" :key="cType.engName" :span="cType.chineseName.length > 8 ? 24 : 12">
                <el-tag
                  :style="{cursor: modelType === 10 || modelType === 1 || modelType === 9 ? 'pointer' : 'not-allowed'}"
                  :type="modelType === 10 || modelType === 1 || modelType === 9 ? '' : 'info'"
                  :effect="cType.check ? 'dark' : 'plain'"
                  @click="modelType === 10 || modelType === 1 || modelType === 9 && handleTypeChange(cType)"
                >
                  {{ cType.chineseName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="collapse-box">
        <el-row class="box-card">
          <el-col :span="3">公共置信度</el-col>
          <el-col :span="21">
            <el-slider
              v-model.number="commonAccuracy"
              style="pointer-events: auto;"
              :min="0.01"
              :max="1"
              :step="0.01"
              show-input
            />
          </el-col>
        </el-row>
        <el-row v-for="(item, index) in collapseList" :key="item.label" class="box-card">
          <el-col :span="3">{{ item.chineseName }}</el-col>
          <el-col :span="21">
            <el-slider
              v-model.number="item.accuracy"
              style="pointer-events: auto;"
              :min="0.01"
              :max="1"
              :step="0.01"
              show-input
            />
          </el-col>
        </el-row>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import CanvasMixin from '@/mixin/canvas'
import { getModelDetail, modelSettings } from '@/api/model'

export default {
  mixins: [CanvasMixin],
  data() {
    return {
      dialogVisible: false,
      activeNames: [],
      roadType: 300,
      pitchDiseaseType: [], // 沥青路面病害
      cementDisesaeType: [], // 水泥路面病害
      sandstoneDisesaeType: [], // 砂石路面病害
      collapseList: [],
      modelType: null, // 当前的路面模型
      modelId: null,
      id: null,
      commonAccuracy: 0.3,
      deleteCollapseList: [],
    }
  },
  methods: {
    async show(row) {
      const that = this
      this.modelType = row.modelType
      this.modelId = row.id
      const res = await getModelDetail({ modelId: this.modelId })

      this.id = res.payload.id
      this.commonAccuracy = res.payload.detail.commonAccuracy || 0.3

      await that.getModelIdentifyTypes()
      if (this.cementDisesaeType.length === 0) {
        this.cementDisesaeType = that.filterDiseaseType(1, 2)
      }
      if (this.pitchDiseaseType.length === 0) {
        this.pitchDiseaseType = that.filterDiseaseType(1, 1)
      }
      if (this.sandstoneDisesaeType.length === 0) {
        this.sandstoneDisesaeType = that.filterDiseaseType(1, 13)
      }

      this.isCheck(res.payload.detail.labelList || [])

      switch (row.modelType) {
      case 1: // 沥青路面
        that.activeNames = ['1', '3', '4', '5', '9', '10']
        break
      case 2: // 水泥路面
        that.activeNames = ['2', '3', '4', '5']
        break
      case 6: // 砂石路面
        that.activeNames = ['6', '3', '4', '5']
        break
      case 9: // 慢巡
        that.activeNames = ['9', '10']
        break
      case 10: // 城市管理问题
        that.activeNames = ['1', '10']
        break

      default:
        break
      }

      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    isCheck(labelList = [], isReset = false) {
      const updateCheckAndPush = (items, labelList, isReset) => {
        items.forEach((item) => {
          if (isReset) {
            item.check = false
          } else {
            const data = labelList.find((d) => d.label === item.label)
            item.check = !!data // 使用 !! 来简化逻辑
            if (data) {
              this.collapseList.push({
                chineseName: item.chineseName,
                label: data.label,
                accuracy: data.accuracy,
              })
            }
          }
        })
      }

      switch (this.modelType) {
      case 1:
        updateCheckAndPush(this.pitchDiseaseType, labelList, isReset)
        break
      case 2:
        updateCheckAndPush(this.cementDisesaeType, labelList, isReset)
        break
      case 6:
        updateCheckAndPush(this.sandstoneDisesaeType, labelList, isReset)
        break
      case 9:
        updateCheckAndPush(this.slowPatrolType, labelList, isReset)
        updateCheckAndPush(this.cityManagementType, labelList, isReset)
        break
      case 10:
        updateCheckAndPush(this.pitchDiseaseType, labelList, isReset)
        updateCheckAndPush(this.cityManagementType, labelList, isReset)
        break

      default:
        break
      }

      if (this.modelType !== 9) {
        updateCheckAndPush(this.allRoadForeignMatter, labelList, isReset)
        updateCheckAndPush(this.allRoadAssetsType, labelList, isReset)
        updateCheckAndPush(this.allAlongLine, labelList, isReset)
      }
    },
    async handleTypeChange(type) {
      type.check = !type.check

      const findIndex = this.collapseList.findIndex((item) => type.label === item.label)
      const findDelIndex = this.deleteCollapseList.findIndex((item) => type.label === item.label)

      if (type.check) {
        if (findDelIndex !== -1) {
          this.collapseList.push(this.deleteCollapseList[findDelIndex])
        } else if (findIndex === -1) {
          this.collapseList.push({
            chineseName: type.chineseName,
            label: type.label,
            accuracy: this.commonAccuracy,
          })
        }
      } else if (findIndex !== -1) {
        if (findDelIndex !== -1) {
          this.deleteCollapseList[findDelIndex] = this.collapseList[findIndex]
        } else {
          this.deleteCollapseList.push(this.collapseList[findIndex])
        }
        this.collapseList.splice(findIndex, 1)
      }
    },
    handleSubmit() {
      const query = {
        id: this.id,
        modelId: this.modelId, // 模型id
        detail: {
          commonAccuracy: this.commonAccuracy, // 公共置信度
          labelList: this.collapseList,
        },
      }
      modelSettings(query).then((res) => {
        this.$message({
          type: 'success',
          message: '设置成功',
        })
        this.handleClose()
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.isCheck([], true)
      this.activeNames = []
      this.modelType = null
      this.collapseList = []
      this.deleteCollapseList = []
    },
  },
}

</script>

<style lang="scss" scoped>

::v-deep .el-dialog__body {
  padding: 10px 20px 20px;
}
.dialog-box {
  display: flex;
  max-height: 65vh;
  .collapse-box {
    flex: 1;
    padding-left: 25px;
    overflow-y: auto;
    .box-card {
      margin-bottom: 10px;
      ::v-deep.el-card__body {
        padding: 0px 20px !important;
      }
      .el-col {
        line-height: 2.5;
        margin-bottom: 0px !important;
      }
    }
  }
}
.cont-tool-scroll {
  width: 300px;
  overflow-y: auto;
  margin-top: 8px;

  // &::-webkit-scrollbar-track-piece {
  //   background: #fff;
  // }

  // &::-webkit-scrollbar {
  //   width: 0px;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: #F5F6F7;
  //   border-radius: 20px;
  // }
}
.cont-tool-title {
    font-size: 16px;
    margin-top: 0px;
    // margin-bottom: 10px;
    // background: rgb(247, 247, 247);
    padding: 10px 0;
    background: #F5F6F7;
}

.el-row {
  margin: 0;
  padding: 0
}
.el-col {
  margin-bottom: 6px;
  padding-left: 10px !important;
  padding-right: 10px !important;
}
.el-tag {
  width: 100%;
  cursor: pointer;
  text-align: center;
  // padding: 0 5px;

}
.el-radio {
    padding: 15px 0;
}

.submit-btn {
    width: 100%;
    margin-top: 20px;
}

.area-div {
  font-size: 14px;
  padding-left: 10px;
  margin-top: 10px;

  .el-input {
    width: 65%;
  }
}

::v-deep .el-collapse {
  border-color: transparent!important;

  .el-collapse-item__header {
    font-size: 16px;
    border-color: transparent!important;
    background: #F5F6F7;
    height: 30px;
    line-height: 30px;
    padding-left: 20px;
  }

  .el-collapse-item__wrap {
    border-color: transparent!important;
    padding: 10px 20px;
  }

  .el-collapse-item__arrow {
    transform: rotate(-90deg);

    &.is-active {
      transform: rotate(90deg);
    }
  }

  .el-collapse-item__content {
    padding-bottom: 0!important;
  }
}
</style>
