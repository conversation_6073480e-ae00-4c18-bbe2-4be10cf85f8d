/* eslint-disable no-lonely-if */
/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:53
 * @LastEditors: guowy
 * @LastEditTime: 2021-02-18 15:42:35
 */
import Vue from 'vue'
import NProgress from 'nprogress' // progress bar
import getPageTitle from '@/utils/get-page-title'
import router from './router'
import store from './store'
import 'nprogress/nprogress.css' // progress bar style
// import Cookies from 'js-cookie'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

const permissionFn = async (to, from, next) => {
  // 确定用户是否已登录
  const hasToken = Vue.prototype.$user.getToken()

  if (hasToken) {
    if (to.path === '/login') {
      const perRoutes = store.getters.permission_routes
      if (perRoutes && perRoutes.length > 0) {
        const path = (`${perRoutes[0].path}/${perRoutes[0].children[0].path}`).replace('//', '/')
        next({ path })
      } else if (to.query.userName && to.query.userPwd) {
        next('/home')
      } else {
        next()
      }
      NProgress.done()
    } else if (store.getters.userId) {
      next()
    } else {
      try {
        // 获取当前登录用户信息（自定义）
        await store.dispatch('account/getAccountInfo')
        // 获取用户信息
        await store.dispatch('user/getInfo')
        // 从服务端获取路由
        const { routes, accessedRoutes } = await store.dispatch('menu/generateRoutes', store.state)
        // 动态添加路由
        router.addRoutes(routes)

        if (to.path === '/') {
          if (accessedRoutes && accessedRoutes.length > 0) {
            const redirect = { ...to }
            const path = (`${accessedRoutes[0].path}/${accessedRoutes[0].children[0].path}`).replace('//', '/')
            redirect.path = path
            redirect.fullPath = path
            next({ ...redirect, replace: true })
          } else {
            Vue.prototype.$message({
              message: '没有菜单权限',
              type: 'error',
            })
            NProgress.done()
          }
        } else {
          next({ ...to, replace: true })
        }
      } catch (error) {
        // 删除 token
        // await store.dispatch('user/resetToken')
        NProgress.done()
      }
    }
  } else if ((to.query.userName && to.query.userPwd) || to.query.thirdtoken) {
  // 从其他系统跳转而来或访问本项目时，如果地址携带登录验证参数，自动完成login操作，并跳转到首页
    const params = {
      principal: '',
      password: '',
    }
    // 情况1：若地址拦携带用户名`userName`和密码`userPwd`，直接使用登录
    if (to.query.userName && to.query.userPwd) {
      params.principal = decodeURI(to.query.userName)
      params.password = decodeURI(to.query.userPwd)
    } else if (to.query.thirdtoken) {
    // 情况2：若地址拦携带`thirdtoken`（用户名和密码的一个可逆的加密字符串，如username={username}&password={passowrd}的base64编码串），需解密
    // 测试：thirdtoken=********************************************************
      const queryData = {}
      const thridtokenArr = atob(to.query.thirdtoken).split('&')
      thridtokenArr.forEach((item) => {
        if (item.includes('=')) {
          const [key, value] = item.split('=')
          queryData[key] = value
        }
      })
      params.principal = queryData.username
      params.password = queryData.password
    }
    const path = to.query.path || `/home`
    try {
      await store.dispatch('user/login', params)
      localStorage.setItem('loginPath', to.path)
      // @robu/user中登录接口返回空，即resolve()，此行会报错
      // Cookies.set('access_token', res.payload.accessToken, { sameSite: 'None', secure: true })
      next({ path, replace: true })
      NProgress.done()
    } catch (error) {
      next('/login')
      NProgress.done()
    }
  } else if (whiteList.indexOf(to.path) !== -1) {
    next()
  } else {
    next(`/login`)
    NProgress.done()
  }
}

router.beforeEach((to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // 判断网站是否需要登录
  if (store.state.settings.login) {
    permissionFn(to, from, next)
  } else {
    if (to.path === '/') {
      next({ path: '/home' })
    } else {
      next()
    }
    NProgress.done()
  }
})

router.afterEach((to, from) => {
  // finish progress bar
  if (to.path === '/login') {
    const devicewidth = document.documentElement.clientWidth// 获取当前分辨率下的可是区域宽度
    const scale = devicewidth / 1920 // 分母——设计稿的尺寸
    document.body.style.zoom = scale// 放大缩小相应倍数
  } else {
    document.body.style.zoom = 1
  }
  NProgress.done()
})
