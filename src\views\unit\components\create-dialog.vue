/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by:   wangyj
 * @Last Modified time: 2022-10-25 18:02:28
 */

<template>
  <el-dialog
    width="40%"
    :title="dialogType==='edit'?'修改单位':'添加单位'"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    @close="handelClose"
  >
    <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
      <el-form-item label="父级单位" class="parentId">
        <UnitSelect v-model="temp.parentId" showOnlyParents useNodeId/>
      </el-form-item>
      <el-form-item label="单位名称" prop="unitName">
        <el-input v-model="temp.unitName" placeholder="请输入单位名称" />
      </el-form-item>
      <el-form-item label="所属区域" prop="regionProvince">
        <el-select
          v-model="temp.regionProvince"
          class="mr_10 area"
          filterable
          placeholder="请选择省份"
          clearable
          @change="onRegionProvince"
        >
          <el-option
            v-for="item in regionProvinceOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
        <el-select
          v-model="temp.regionCity"
          class="mr_10 area"
          filterable
          clearable
          placeholder="请选择城市"
          @change="onRegionCityChange"
        >
          <el-option
            v-for="item in regionCityOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
        <el-select v-model="temp.regionDistrict" class="mr_10 area" filterable clearable placeholder="请选择区县">
          <el-option
            v-for="item in regionDistrictOptions"
            :key="item.name"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createOrUpdateUnit } from '@/api/unit'
import districtsJson from '../json/districts.json'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        unitName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
      },
      regionProvinceOptions: [],
      regionCityOptions: [],
      regionDistrictOptions: [],
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        if (this.formData.id) {
          this.dialogType = 'edit'
          console.log('edit')

          if (this.formData.regionProvince) {
            const data = this.regionProvinceOptions.find((item) => this.formData.regionProvince === item.name)
            if (data) this.regionCityOptions = data.districts

            if (this.formData.regionCity) {
              const data = this.regionCityOptions.find((item) => this.formData.regionCity === item.name)
              if (data) this.regionDistrictOptions = data.districts
            }
          }
        } else {
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  async created() {
    this.regionProvinceOptions = districtsJson
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          createOrUpdateUnit(tempData).then((res) => {
            this.$message({
              message: '新增单位成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
            // 刷新单位列表
            this.$store.dispatch('unit/refreshUnitList')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          createOrUpdateUnit(tempData).then(() => {
            this.$message({
              message: '修改单位成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
            // 刷新单位列表
            this.$store.dispatch('unit/refreshUnitList')
          })
        }
      })
    },
    onRegionProvince(value) {
      console.log('onRegionProvince')
      const data = this.regionProvinceOptions.find((item) => value === item.name)
      if (data) {
        this.regionCityOptions = data.districts
      } else {
        this.regionCityOptions = []
        this.regionDistrictOptions = []
      }

      if (this.temp.regionCity) this.temp.regionCity = ''
      if (this.temp.regionDistrict) this.temp.regionDistrict = ''
    },
    onRegionCityChange(value) {
      const data = this.regionCityOptions.find((item) => value === item.name)
      if (data) this.regionDistrictOptions = data.districts
      else this.regionDistrictOptions = []

      if (this.temp.regionDistrict) this.temp.regionDistrict = ''
    },
    show() {
      this.dialogFormVisible = true
    },
    handelClose() {
      console.log('handelclose')
      this.dialogFormVisible = false
      this.regionCityOptions = []
      this.regionDistrictOptions = []
    },

  },
}
</script>

<style scoped lang="scss">
.mr_10 {
  margin-right: 10px;
}
.area {
  width: 180px;
}

 .parentId {
  ::v-deep .el-form-item__content {
    line-height: 0 !important;
  }
}

</style>
