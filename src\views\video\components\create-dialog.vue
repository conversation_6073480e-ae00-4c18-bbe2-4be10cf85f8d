/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-08 14:48:15
 */

<template>
  <el-dialog
    :title="dialogType==='edit'?'修改视频流':'添加视频流'"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="80%"
    top="5vh"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="网址" prop="videoUrl">
        <el-input v-model="temp.videoUrl" placeholder="请输入rtsp视频流网址" />
      </el-form-item>
      <el-form-item label="预览网址" prop="previewUrl">
        <el-input v-model="temp.previewUrl" placeholder="请输入http视频流预览地址" />
      </el-form-item>
      <el-form-item label="视频里宽度" prop="videoWidth">
        <el-input v-model="temp.videoWidth" placeholder="请输入视频流宽度" />
      </el-form-item>
      <el-form-item label="视频里高度" prop="videoHeight">
        <el-input v-model="temp.videoHeight" placeholder="请输入视频流高度" />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="坐标类型" prop="coordinateType">
            <el-select v-model="temp.coordinateType" placeholder="请选择坐标类型" style="width: 100%">
              <el-option>原始坐标</el-option>
              <el-option>地图坐标</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="经度" prop="longitude">
            <el-input v-model="temp.longitude" placeholder="请输入位置的经度" />
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <el-input v-model="temp.latitude" placeholder="请输入位置的纬度" />
          </el-form-item>
          <el-form-item label="行政区域编码" prop="areaCode">
            <el-input v-model="temp.areaCode" disabled placeholder="" />
          </el-form-item>
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="temp.address" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <div id="videoMap" />
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createUser, updateUser } from '@/api/user'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: '请输入视频流名称', trigger: 'blur' }],
        videoWidth: [{ required: true, message: '请输入视频流宽度', trigger: 'blur' }],
        videoHeight: [{ required: true, message: '请输入视频流高度', trigger: 'blur' }],
        coordinateType: [{ required: true, message: '请选择坐标类型', trigger: 'blur' }],
        longitude: [{ required: true, message: '请输入位置的经度', trigger: 'blur' }],
        latitude: [{ required: true, message: '请输入位置的纬度', trigger: 'blur' }],
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        if (this.formData.id) {
          this.dialogType = 'edit'
        } else {
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          createUser(tempData).then((res) => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          updateUser(tempData).then(() => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
    handlePwdChange(value) {
      this.$refs.dataForm.clearValidate('confirmPassword')
      this.$set(this.temp, 'confirmPassword', '')
      if (value !== '') {
        this.rules.confirmPassword[0].required = true
      } else {
        this.rules.confirmPassword[0].required = false
      }
    },
  },
}
</script>
