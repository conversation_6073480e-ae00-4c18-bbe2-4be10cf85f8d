import request from '@/utils/request'
import { time } from 'echarts'

/**
 * 获取巡检记录列表
 * @param {Object} params
 * @param {number} params.page - 当前页码
 * @param {number} params.size - 每页数量
 * @param {string} [params.workUnit] - 设备单位
 * @param {string} [params.deviceKey] - 设备ID
 * @param {string} [params.startTime] - 开始时间
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.roadFilter] - 路线名称、编号
 * @param {string} [params.adCode] - 行政区划编码
 * @returns {Promise}
 */
export function getInspectionRecords(params) {
  return request({
    url: '/inspect-records',
    method: 'get',
    params,
  })
}

/**
 * 获取巡检记录详情
 * @param {number} id - 巡检记录ID
 * @returns {Promise}
 */
export function getInspectionRecordDetail(id) {
  return request({
    url: `/inspect-records/${id}`,
    method: 'get',
  })
}

/**
 * 创建巡检任务
 * @param {number} id - 巡检记录ID
 * @returns {Promise}
 */
export function createInspectionTask(id) {
  return request({
    url: `/inspect-records/create-task`,
    method: 'get',
    timeout: undefined,
    params: { id },
  })
}

/**
 * 结束巡检任务
 * @param {number} id - 巡检记录ID
 * @returns {Promise}
 */
export function finishInspectionTask(id) {
  return request({
    url: `/inspect-records/finish-task`,
    method: 'get',
    params: { id },
  })
}
