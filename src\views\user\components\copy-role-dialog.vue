<template>
  <el-dialog
    title="复制角色"
    :visible.sync="visible"
    width="40%"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="选择角色" prop="copiedRoleId">
        <el-select
          v-model="form.copiedRoleId"
          filterable
          placeholder="请选择要复制的角色"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="新角色名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入新角色名称" />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { copyRole } from '@/api/auth'

export default {
  name: 'CopyRoleDialog',
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        copiedRoleId: undefined,
        name: '',
        anonRole: false,
        authcRole: false,
      },
      roleList: [],
      rules: {
        copiedRoleId: [{ required: true, message: '请选择要复制的角色' }],
        name: [{ required: true, message: '请输入新角色名称' }],
      },
    }
  },
  methods: {
    show(roles) {
      this.visible = true
      this.roleList = roles
      this.form = {
        copiedRoleId: undefined,
        name: '',
      }
    },
    handleClose() {
      this.visible = false
      this.$refs.form?.resetFields()
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true
        await copyRole(this.form)
        this.$message.success('复制成功')
        this.$emit('refresh')
        this.handleClose()
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
  },
}
</script>
