<!--  -->
<template>
  <div class="container">
    <map-search :devices="devices" @select-location="handleLocationSelect" />
    <!-- <div class="tool-container">
      <el-button type="primary" :plain="curTab === 2" @click="handleSwitch(1)">设备</el-button>
      <el-button type="primary" :plain="curTab === 1" @click="handleSwitch(2)">路线</el-button>
    </div>-->

    <baidu-map v-if="admin" class="bm-view" :center="center" :zoom="zoom" :scroll-wheel-zoom="true" @ready="handler">
      <!-- 所有设备最新位置 -->
      <bml-marker-clusterer v-if="devices.length > 0" :average-center="true" :styles="styles">
        <bm-marker
          v-for="device in devices"
          :key="device.deviceKey"
          :position="{lat: device.gpsLatitude, lng: device.gpsLongitude}"
          :rotation="device.rotation"
          :icon="getDeviceIcon(device)"
          :top="true"
          @click="handleShow(device)"
        >
          <bm-label
            v-if="device.deviceKey === curDevice.deviceKey"
            :content="device.deviceName"
            :label-style="{borderRadius: '10px', color: '#57a3f3', fontSize : '16px', fontWeight: 'bold', border: 'none', padding: '2px 10px', transform: 'translateX(-50%)'}"
            :offset="{height: -20, width: 32}"
          />
        </bm-marker>
      </bml-marker-clusterer>
      <!-- 所有设备最新路线 -->
      <template v-if="lines.length > 0">
        <bm-polyline
          v-for="(item,id) in lines"
          :key="id+item.color"
          :path="item.line"
          :stroke-color="item.color"
          :stroke-opacity="1"
          :stroke-weight="5"
        />
      </template>
    </baidu-map>
    <div v-show="isShow" class="inspection-panel">
      <el-card class="card">
        <i class="el-icon-close" @click="handleHide" />
        <div class="title active-tab">
          <i class="el-icon-discount active-tab" />
          <span class="active-tab">设备信息</span>
        </div>
        <div class="info">
          <div class="info-item">
            <p>设备单位：{{ curDevice.workUnit }}</p>
            <p @dblclick="handleCopyText(curDevice.deviceName)">
              设备名称：
              {{ curDevice.deviceName }}
            </p>
            <p>
              设备状态：{{ curDevice.using ? '工作中' : '未工作' }}
              <span v-if="curDevice.using" class="using" />
              <span v-else class="no-using" />
            </p>
          </div>
          <!-- <div class="info-item">
            <Jessibuca v-if="videoUrl" style="width: 201px;height: 114px;" :play-url="videoUrl" />
          </div>-->
        </div>
        <div class="title">
          <span class="cursor-p" :class="{'active-tab': activeTab === 1}" @click="activeTab = 1">
            <i class="el-icon-time" />
            <span>设备巡检历史</span>
          </span>
          <span v-if="videoUrl" class="boundary" />
          <span v-if="videoUrl" class="cursor-p" :class="{'active-tab': activeTab === 2}" @click="activeTab = 2">
            <i class="el-icon-video-camera" />
            <span>实时视频</span>
          </span>
        </div>
        <el-table
          v-show="activeTab === 1"
          v-loading="loading"
          size="mini"
          :data="inspectionListData"
          border
          :max-height="tableHeight"
          style="width: 100%"
        >
          <el-table-column align="center" prop="roadName" label="路线名称" width="90" />
          <el-table-column align="center" prop="detectDirection" label="检测方向" width="70">
            <template slot-scope="{ row }">
              {{
              row.detectDirection === 1 ? "上行" : "下行"
              }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="巡检开始时间" width="120">
            <template slot-scope="{ row }">
              {{
              new Date(row.startTime)
              | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
              }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="巡检结束时间" width="120">
            <template slot-scope="{ row }">
              <template v-if="row.hasEnd">
                {{
                new Date(row.endTime)
                | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
                }}
              </template>
              <template v-else>巡检中</template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="巡检里程(km)" prop="inspectMileage" width="95" />
          <el-table-column align="center" label="操作" width="60">
            <template slot-scope="{ row, $index }">
              <el-button type="text" @click="handleDetail(row, $index)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="activeTab === 2" class="live-box">
          <Jessibuca v-if="isShow && activeTab === 2 && videoUrl" :play-url="videoUrl" />
        </div>
        <Pagination
          v-show="activeTab === 1 && total > 0"
          :total="total"
          :page.sync="listQuery.currentPage"
          :limit.sync="listQuery.pageSize"
          :pager-count="5"
          layout="total, prev, pager, next"
          @pagination="getInspectionListAjax"
        />
      </el-card>
    </div>
  </div>
</template>

<script>
import { simpleStyleJson } from '@/utils/map-style'
import { BmlMarkerClusterer } from 'vue-baidu-map-v3'
import Pagination from '@/components/Pagination/index.vue'
import Jessibuca from '@/components/Jessibuca.vue'
import MapSearch from './map-search.vue'
import DevicesMixin from '../mixin/devices'
import { copyText } from '@/utils/clipboard'

const icons = {
  patrol: {
    on: {
      left: require('@/assets/patrol_on_left.png'),
      right: require('@/assets/patrol_on.png')
    },
    off: {
      left: require('@/assets/patrol_off_left.png'),
      right: require('@/assets/patrol_off.png')
    }
  },
  car: {
    on: require('@/assets/car_on.png'),
    off: require('@/assets/car_off.png'),
  },
}

export default {
  name: 'Home',
  components: { BmlMarkerClusterer, Pagination, Jessibuca, MapSearch },
  mixins: [DevicesMixin],
  watch: {
    homeNum(newVal, oldVal) {
      console.log(this.$route.path)
      if (this.$route.path === '/home') {
        this.handleReFresh()
      }
    },
  },
  methods: {
    handler({ BMap, map }) {
      const that = this
      that.map = map
      that.BMap = BMap
      that.center.lng = 116.404
      that.center.lat = 39.915
      that.zoom = 6

      // map.setMapStyle({
      //   style: 'grayscale',
      // })
      // map.setMapStyleV2({
      //   styleId: '84641241ef00321945d05b9cb13fab3b',
      // })
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
    },

    handleSwitch(val) {
      const that = this
      that.curTab = val
      if (val === 1) {
        that.lines = []
        clearTimeout(that.timer)
        that.timer = null
        that.getDevicesLatestAjax(true)
      } else {
        that.infoWindowClose(that.curDevice)
        that.devices = []
        clearTimeout(that.timer)
        that.timer = null
        that.getRouteLatestAjax()
      }
    },

    infoWindowOpen(device) {
      this.$set(device, 'show', true)
      this.isShow = true
    },
    infoWindowClose(device) {
      this.$set(device, 'show', false)
      this.isShow = false
    },

    handleReFresh() {
      // this.$router.go(0)
      // if (this.curDevice) {
      //   this.infoWindowClose(this.curDevice)
      // }
      const that = this
      that.curDevice = {}
      that.isShow = false
      if (that.timer) {
        clearInterval(that.timer)
        that.timer = null
      }
      that.inspectionListData = []
      that.listQuery.currentPage = 1
      that.total = 0
      that.getDevicesLatestAjax(true)
      that.timer = setInterval(() => {
        that.getDevicesLatestAjax(false)
      }, 1000)
    },
    getDeviceIcon(device) {
      const isPatrol = device.deviceModel == 1
      const iconType = isPatrol ? 'patrol' : 'car'
      const status = device.using ? 'on' : 'off'
      
      let url;
      if (isPatrol) {
        const direction = device.direction === 'left' ? 'left' : 'right';
        url = icons[iconType][status][direction];
      } else {
        url = icons[iconType][status];
      }
      
      const icon = {
        url: url,
        size: { width: isPatrol ? 62 : 69, height: isPatrol ? 46 : 51 },
        opts: {
          imageSize: { width: isPatrol ? 62 : 69, height: isPatrol ? 46 : 51 },
        },
      }
      return icon
    },
    // 处理定位选择的方法
    handleLocationSelect(item) {
      if (item.type === 'coordinate') {
        // 直接定位到经纬度
        this.map.centerAndZoom(
          new this.BMap.Point(item.lng, item.lat),
          this.maxZoom || 19
        )
      } else if (item.type === 'device') {
        // 定位到设备
        this.map.centerAndZoom(
          new this.BMap.Point(item.lng, item.lat),
          this.maxZoom || 19
        )

        // 找到对应设备并显示详情
        const device = this.devices.find((d) => d.deviceKey === item.deviceKey)
        if (device) {
          this.handleShow(device)
        }
      }
    },
    handleCopyText(text) {
      copyText(text);
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 94px);
  position: relative;

  .tool-container {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 1;
  }

  .bm-view {
    width: 100%;
    height: 100%;

    // ::v-deep.BMap_pop {
    //   & > div:nth-of-type(1),
    //   & > div:nth-of-type(2),
    //   & > div:nth-of-type(3),
    //   & > div:nth-of-type(5),
    //   & > div:nth-of-type(7),
    //   & > div:nth-of-type(8),
    //   .BMap_top,
    //   .BMap_center,
    //   .BMap_bottom{
    //     display: none;
    //   }

    //   & > div:nth-of-type(9){
    //     background: #fff;
    //     padding: 15px 20px;
    //     // width: 260px!important;
    //     height: auto!important;
    //     border-radius: 6px;

    //     .BMap_bubble_title {
    //       color: #2d8cf0!important;
    //       font-weight: bold;
    //       font-size: 18px;
    //     }
    //     .c-info {
    //       color: #2d8cf0!important;
    //       line-height: 30px;
    //     }
    //   }

    //   & > img:nth-of-type(1) {
    //     left: 290px!important;
    //     top: 30px!important;
    //   }
    // }

    // ::v-deep .BMap_shadow {
    //   display: none !important;
    // }
  }
  .bm-view_72 {
    width: 72%;
    max-width: calc(100% - 512px);
  }

  .inspection-panel {
    width: var(rightAsideWidth);
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    background: #f6f6f6;
    overflow: auto;
    padding: 10px;

    .card {
      position: relative;
      border-radius: 10px;
      height: calc(100vh - 120px);
      border: none;
      box-shadow: none;
      .info {
        font-size: 14px;
        color: #606266;
        padding-bottom: 12px;
        display: flex;

        .info-item {
          flex: 1;
          &:nth-child(2) {
            display: flex;
            justify-content: flex-end;
          }
        }
        p {
          display: flex;
          align-items: center;
        }

        span {
          width: 12px;
          height: 12px;
          display: inline-block;
          border-radius: 50%;
          margin-left: 10px;
        }
        .using {
          background: rgba(147, 229, 59);
        }
        .no-using {
          background: #909399;
        }
      }
      .el-icon-close {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #394380;
        cursor: pointer;
      }

      .pagination-container {
        padding-bottom: 0;
        padding-top: 20px;
      }
    }
  }
}
</style>
