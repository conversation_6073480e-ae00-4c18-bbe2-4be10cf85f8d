/**
 * 日期选择器选项mixin
 * 提供日期选择器相关方法
 */

import dayjs from 'dayjs'

export default {
  data() {
    return {
      // 默认日期时间格式
      dateFormat: 'yyyy-MM-dd HH:mm:ss'
    }
  },
  methods: {
    /**
     * 开始时间日期选择器配置
     * 限制开始时间不能晚于结束时间
     * @param {String|Date} endTime 结束时间
     * @returns {Object} 选择器配置
     */
    startPickerOptions(endTime) {
      return {
        disabledDate(time) {
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime()
          }
          return false
        }
      }
    },
    
    /**
     * 结束时间日期选择器配置
     * 限制结束时间不能早于开始时间
     * @param {String|Date} startTime 开始时间
     * @returns {Object} 选择器配置
     */
    endPickerOptions(startTime) {
      return {
        disabledDate(time) {
          if (startTime) {
            return time.getTime() < new Date(startTime).getTime()
          }
          return false
        }
      }
    },
    
    /**
     * 处理结束时间变化，将时间设置为当天的23:59:59
     * @param {String|Date} date 日期时间
     * @param {String} format 日期格式，默认使用this.dateFormat
     * @returns {String} 处理后的结束时间
     */
    getEndOfDay(date, format = null) {
      if (!date) return null
      
      // 使用指定格式或默认格式
      const dateFormat = format || this.dateFormat
      
      // 将时间设置为当天的23:59:59
      return dayjs(date).endOf('day').format(dateFormat)
    },
    
    /**
     * 处理结束时间变化，将时间设置为当天的23:59:59
     * 此方法用于兼容旧版本，新版本建议使用getEndOfDay
     * @param {String|Date} date 日期时间
     * @returns {String} 处理后的结束时间
     */
    handleEndTimeChange(date) {
      const endOfDay = this.getEndOfDay(date)
      console.log('处理后的结束时间：', endOfDay)
      return endOfDay
    }
  }
} 