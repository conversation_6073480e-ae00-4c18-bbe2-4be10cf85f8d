<!--
 * @Author: guowy
 * @Date: 2020-11-11 10:43:03
 * @LastEditors: guowy
 * @LastEditTime: 2021-02-20 10:51:03
-->
<template>
  <div class="table-container">
    <div class="filter-container">
      <slot name="tableFilter" />
    </div>
    <div class="robu-table">
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="list"
        highlight-current-row
        height="100%"
        style="width: 100%;"
        @row-click="rowClick"
        @selection-change="handleSelectionChange"
      >
        <slot name="tableColumns" />
      </el-table>
    </div>
    <template v-if="paging">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.currentPage"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </template>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'

export default {
  components: { Pagination },
  props: {
    paging: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 20,
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getListWithNoPage() {
      this.listLoading = true
      this.$emit('getTableData', (payload) => {
        this.list = payload
        this.listLoading = false
      })
    },
    async getListWithPage() {
      this.listLoading = true
      const { pageSize, currentPage } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
      }
      this.$emit('getTableData', query, (payload) => {
        this.list = payload.content
        this.total = payload.totalElements
        this.listLoading = false
      })
    },
    async getList() {
      if (this.paging) {
        this.getListWithPage()
      } else {
        this.getListWithNoPage()
      }
    },
    refreshTable() {
      this.listQuery.currentPage = 1
      this.list = []
      this.getList()
    },
    rowClick(row) {
      this.$emit('row-click', row)
    },
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },
    deleteRow(index) {
      this.list.splice(index, 1)
    },
    clearSelection() {
      this.$refs.table.clearSelection()
    },
  },
}
</script>

<style lang="scss">
.table-container{
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .robu-table{
    width: 100%;
    flex:1;
    overflow: auto;
  }
  .filter-container{
    flex: 0;
    margin-bottom: 16px;
  }
  .pagination-container{
    flex: 0;
  }
}
</style>
