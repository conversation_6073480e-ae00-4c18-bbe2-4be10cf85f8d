<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <UnitSelect v-model="listQuery.workUnitId" placeholder="单位名称" useNodeId />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-date-picker
            v-model.trim="listQuery.analysisDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy/MM/dd"
            clearable
            placeholder="请选择巡检日期"
          />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="巡检日期">
        <template slot-scope="{row}">
          {{ new Date(row.analysisDate) | parseTime('{yyyy}-{mm}-{dd}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="单位名称" prop="workUnit" />
      <el-table-column align="center" label="巡检车辆" prop="carNum" />
      <el-table-column align="center" label="巡检总里程(km)" prop="inspectMileage" />
      <el-table-column align="center" label="巡检总时长(h)" prop="inspectHour" />
      <el-table-column align="center" label="发现重大病害(处)" prop="damageCount" />
      <el-table-column align="center" label="发现道路资产(处)" prop="roadAssetCount" />
      <el-table-column align="center" label="发现道路风险点(处)" prop="roadRiskCount" />
      <el-table-column align="center" label="发现沿线设施损坏(处)" prop="roadsideFacilitiesDamageCount" />
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row}">
          <!-- <el-button type="text">详情</el-button> -->
          <el-button type="text" @click="handleExport(row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getInspectionReports } from '@/api/inspection-report'
import Pagination from '@/components/Pagination/index.vue'
import { exportFile } from '@/utils/index'
export default {
  name: 'InspectionReport',
  components: { Pagination },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnitId: null,
        analysisDate: null,
      },
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, analysisDate, workUnitId,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        workUnitId: workUnitId || null,
        analysisDate: analysisDate || null,
        sort: 'analysisDate,desc',
      }
      getInspectionReports(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleExport({ workUnitId, analysisDate }) {
      analysisDate = this.$time.parseTime(new Date(analysisDate), '{yyyy}/{mm}/{dd}')
      exportFile(`inspect-data-daily-analyses/excel?workUnitId=${workUnitId}&analysisDate=${analysisDate}`)
    },
  },
}
</script>
