<template>
  <div class="app-container">
    <div class="detail-info">
      <!-- 两列布局的信息区域 -->
      <div class="info-grid">
        <div class="detail-item">
          <span class="detail-label">巡查单位:</span>
          <span class="detail-value">{{ detailData.workUnitName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">病害归属:</span>
          <span class="detail-value">{{ detailData.belongToStr }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">巡查人员:</span>
          <span class="detail-value">{{ detailData.realName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">巡查时间:</span>
          <span class="detail-value">{{ formatTimeRange }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">巡查里程:</span>
          <span class="detail-value">{{ detailData.inspectMileage ? detailData.inspectMileage + 'km' : '' }}</span>
        </div>
        <!-- 下载按钮 -->
        <div class="button-container">
          <el-button type="primary" @click="handleExport(detailData.id)">下载</el-button>
        </div>
      </div>

      <el-table :data="diseaseList" border style="width: 100%">
        <el-table-column align="center" label="病害ID" prop="id" width="80">
          <template slot-scope="{ row }">
            {{ row.id }}
          </template>
        </el-table-column>
        <el-table-column prop="damageName" label="病害名称" align="center" />
        <el-table-column label="病害图片" align="center" width="200">
          <template slot-scope="{ row }">
            <div class="image-container">
              <el-carousel
                v-if="row.images && row.images.length > 0"
                height="100px"
                trigger="click"
                :autoplay="false"
                @change="handleCarouselChange($event, row)"
              >
                <el-carousel-item v-for="(image, index) in row.images" :key="index">
                  <el-image
                    :src="image.objectStorageUrlPrefix + image.originalImagePath"
                    fit="contain"
                    @click="handleImageClick(index, row)"
                  >
                    <template #error>
                      <div class="item-image-error"></div>
                    </template>
                    <template #placeholder>
                      <i class="el-icon-loading" style="font-size: 14px;"></i>
                    </template>
                  </el-image>
                </el-carousel-item>
              </el-carousel>
              <div
                class="count"
                v-if="row.images && row.images.length > 0"
              >{{ row.currentImageIndex + 1 }}/{{ row.images.length }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="addressDescription" label="位置描述" align="center" />
        <el-table-column prop="damageDescription" label="病害描述" align="center" />
        <el-table-column prop="roadName" label="道路名称" align="center" />
        <el-table-column prop="roadCode" label="道路编号" align="center"/>
        <el-table-column prop="startPileStr" label="道路桩号" align="center" />
        <el-table-column prop="directionStr" label="巡查方向" align="center" />
        <el-table-column prop="createTime" label="巡查时间" align="center" width="150">
          <template slot-scope="{row}">{{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="100">
          <template slot-scope="{row}">
            <el-button type="text" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="handleGetOrderDamagesList"
    />
    <damage-detail ref="detailDialog" />
  </div>
</template>

<script>
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import { getOrderDamagesList } from '@/api/order-records'
import Pagination from '@/components/Pagination/index.vue'
import DamageDetail from '@/views/order-damages/detail.vue'
// 引入下载mixin
import downloadMixin from './mixins/downloadMixin'

export default {
  name: 'InspectionReportDetail',
  components: { Pagination, DamageDetail },
  // 添加下载mixin
  mixins: [downloadMixin],
  data() {
    return {
      detailData: [],
      activeTab: 'route', // 默认选中巡查路线
      diseaseList: [], // 病害列表数据
      listQuery: {
        page: 1,
        size: 10,
      },
      total: 0,
      previewSrcList: [],
      currentImageIndex: 0,
    }
  },
  computed: {
    // 计算巡查时间范围的格式化显示
    formatTimeRange() {
      if (!this.detailData.startTime) return ''

      const startTime = this.$options.filters.parseTime(
        new Date(this.detailData.startTime),
        '{yyyy}-{mm}-{dd} {hh}:{ii}'
      )

      if (!this.detailData.endTime) return startTime

      const endTime = this.$options.filters.parseTime(
        new Date(this.detailData.endTime),
        '{yyyy}-{mm}-{dd} {hh}:{ii}'
      )

      return `${startTime} ~ ${endTime}`
    },
  },
  created() {
    const { taskId } = this.$route.params
    this.detailData = JSON.parse(
      sessionStorage.getItem(`order-records/detail/${taskId}`)
    )
  },
  mounted() {
    this.handleGetOrderDamagesList()
  },
  methods: {
    handleGetOrderDamagesList() {
      if (!this.detailData) return
      const { startTime, endTime, userId, belongTo, roadName} = this.detailData
      let params = {
        startTime,
        endTime,
        userId,
        belongTo,
        roadName,
        ...this.listQuery,
      }
      params.page -= 1
      getOrderDamagesList(params).then((res) => {
        this.diseaseList = res.payload.content.map((item) => ({
          ...item,
          currentImageIndex: 0, // 初始化图片索引
        }))
        this.total = res.payload.totalElements
      })
    },
    handleImageClick(index, row) {
      console.log('handleImageClick', row)
      this.previewSrcList = row.images.map(
        (item) => item.objectStorageUrlPrefix + item.originalImagePath
      )
      this.currentImageIndex = index
      this.initViewer()
    },
    initViewer() {
      const that = this
      if (that.viewer) {
        that.viewer.destroy()
      }
      that.viewer = viewerApi({
        images: this.previewSrcList,
        options: {
          initialViewIndex: this.currentImageIndex,
          inline: true, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: true, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: true, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: false, // 使用 CSS3 过度
          fullscreen: false, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
          zIndex: 9999,
          hidden(e) {
            console.log('hidden')
            that.viewer = null
          },
          ready(e) {
            console.log('ready')
          },
          viewed(e) {
            console.log('viewed', e)
          },
        },
      })
    },
    handleCarouselChange($index, row) {
      console.log('row', $index, row)
      const index = this.diseaseList.findIndex((item) => item.id === row.id)
      console.log('index', index)
      this.$set(this.diseaseList[index], 'currentImageIndex', $index)
    },
    handleDetail(row) {
      this.$refs.detailDialog.open(row)
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-tag {
  color: #fff;
}
// 修改dialog内容区域高度和滚动
::v-deep .el-dialog__body {
  max-height: 75vh !important;
  padding: 20px 20px 10px;
  overflow-y: auto;
}

/* 详情信息区域 */
.detail-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 单行网格布局 */
.info-grid {
  position: relative;
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 55px;
  .button-container {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  .count {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    z-index: 100;
    padding: 0 10px;
    font-size: 14px;
    line-height: 20px;
    border-radius: 13px;
  }
  ::v-deep .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
/* 自适应轮播图指示器样式 */
::v-deep .el-carousel__indicators {
  /* 指示器按钮样式 */
  .el-carousel__button {
    /* 调整指示器大小 */
    width: 8px !important;
    height: 8px !important;
    /* 圆形指示器 */
    border-radius: 50%;
    /* 确保最小尺寸 */
    min-width: 8px;
    min-height: 8px;
  }
}

/* 如果需要指示器水平排列 */
::v-deep .el-carousel__indicators--horizontal {
  display: flex;
  justify-content: center;
  /* 调整指示器间距 */
  .el-carousel__indicator {
    margin: 0 3px !important;
  }
}

/* 详情项样式 */
.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

/* 标签样式 */
.detail-label {
  font-weight: bold;
  width: 85px; /* 调整宽度以适应单行布局 */
  text-align: left;
  padding-right: 15px;
  color: #606266;
  flex-shrink: 0;
}

/* 值样式 */
.detail-value {
  color: #303133;
  word-break: break-all;
}

/* 表格标题容器 */
.table-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
  width: 100%;
}
</style>