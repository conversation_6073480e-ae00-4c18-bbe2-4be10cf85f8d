/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:01:58
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-03 17:11:12
 */

<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" style="width: 100%;" >        
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-select v-model="listQuery.deviceId" placeholder="deviceId" filterable>
            <el-option v-for="device in deviceOptions" :key="device.deviceKey" :value="device.deviceKey" :label="device.deviceKey" />
          </el-select>
         </el-col>
         <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="startTime"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="endTime"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col>
        
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" icon="el-icon-download" @click="handleExport">
          导出
        </el-button>
        <el-radio-group v-model="dataType" style="margin: -2px 0 0 10px" @change="handleChangeRadio">
          <el-radio-button :label="1">所有数据</el-radio-button>
          <el-radio-button :label="2">问题数据</el-radio-button>
        </el-radio-group>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
      :row-style="TableRowStyle"
      :cell-style="TableCellStyle"
    >
      <el-table-column align="center" label="device_id" prop="deviceId" />
      <el-table-column align="center" label="photo_time" prop="photoTime" />
      <el-table-column align="center" label="boot_start_time" prop="bootStartTime" />
      <el-table-column align="center" label="create_time" prop="createTime" />
      <el-table-column align="center" label="gps_longitude" prop="gpsLongitude" />
      <el-table-column align="center" label="gps_latitude" prop="gpsLatitude" />
      <el-table-column align="center" label="z_acclspeed" prop="zAcclspeed" />
      <el-table-column align="center" label="object_storage_url_prefix" prop="objectStorageUrlPrefix" />
      <el-table-column align="center" label="操作" width="80">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleDetail(row, $index)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0 && dataType === 1"
      :page-sizes="[500, 1000, 2000, 5000]"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getImageInfers, getImageInfersProblem } from '@/api/system'
import { getDevices } from '@/api/device'
import { obj2Param, exportFile } from '@/utils/index'
import Pagination from '@/components/Pagination/index.vue'
import datePickerOptions from '@/mixin/datePickerOptions'
import DateTimePicker from '@/components/DateTimePicker/index.vue'
export default {
  name: 'DataView',
  components: { Pagination, DateTimePicker },
  mixins: [datePickerOptions],
  data() {
    return {
      origList: null,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 500,
        deviceId: '',
        startTime: null,
        endTime: null,
      },
      temp: {},
      dateFormat: 'yyyy-MM-dd HH:mm:ss',
      deviceOptions: [],
      dataType: 1, // 1为所有数据，2为问题数据
    }
  },
  async created() {
    this.listQuery.startTime = `${this.$time.parseTime(new Date(), '{yyyy}-{mm}-{dd}')} 00:00:00`
    await this.getDevicesSelect()
    this.getList()
  },
  methods: {
    async getDevicesSelect() {
      const { payload } = await getDevices()
      this.deviceOptions = payload
      if (payload.length > 0) {
        this.listQuery.deviceId = payload[0].deviceKey
      }
    },
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, deviceId, startTime, endTime,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        deviceId: deviceId === '' ? null : deviceId,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
      }
      getImageInfers(query).then((response) => {
        if (response.payload) {
          this.origList = response.payload.content
          if (this.dataType === 1) {
            this.list = response.payload.content
          } else if (this.dataType === 2) {
            this.list = this.origList.filter((item) => item.dataNotEnough || item.dataMissLarge || item.bootStartTimeChanged)
          }
          this.total = response.payload.totalElements
        }
        this.listLoading = false
      })
    },
    // getProblemList() {
    //   this.listLoading = true
    //   const { deviceId, startTime, endTime } = this.listQuery
    //   const query = {
    //     deviceId: deviceId === '' ? null : deviceId,
    //     startTime: startTime === '' ? null : startTime,
    //     endTime: endTime === '' ? null : endTime,
    //   }
    //   getImageInfersProblem(query).then((res) => {
    //     this.list = res.payload
    //     this.total = 0
    //     this.listLoading = false
    //   })
    // },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleDetail(row, index) {
      sessionStorage.setItem('detailData', JSON.stringify(row))
      this.$router.push({
        name: 'SystemDetail',
        params: {
          detailData: JSON.stringify(row),
        },
      })
    },
    handleExport() {
      const {
        deviceId, startTime, endTime,
      } = this.listQuery
      const params = {
        deviceId: deviceId === '' ? null : deviceId,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
      }
      const paramsStr = obj2Param(params).slice(1)

      exportFile(`image-infers/export?${paramsStr}`)
    },
    TableRowStyle({ row, rowIndex }) {
      const rowStyle = {}
      if (row.dataNotEnough) {
        rowStyle.background = 'rgba(255, 188, 0, 0.15)'
      }

      if (row.dataMissLarge) {
        rowStyle.background = 'rgba(255, 0, 0, 0.15)'
      }
      return rowStyle
    },
    TableCellStyle({
      row, column, rowIndex, columnIndex,
    }) {
      if (row.bootStartTimeChanged && columnIndex === 2) {
        return 'background: rgba(103, 194, 58, 0.4)'
      }
      return ''
    },
    handleChangeRadio(val) {
      if (val === 1) {
        this.list = this.origList
      } else if (val === 2) {
        this.list = this.origList.filter((item) => item.dataNotEnough || item.dataMissLarge || item.bootStartTimeChanged)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
  ::v-deep .el-table__row {
    &:hover {
      td {
        background: rgb(246, 247, 251)!important;
      }
    }
  }
</style>
