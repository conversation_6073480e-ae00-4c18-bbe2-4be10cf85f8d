// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  color: #515a6e;
  font-weight: 400 !important;
}

.el-breadcrumb__separator {
  margin: 0 8px;
  color: #dcdee2;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-message {
  z-index: 9999!important;
}

.el-radio.hide-label .el-radio__label {
  display: none;
}

// el-tree
// 根目录不展示多选框
.el-tree > .el-tree-node > .el-tree-node__content .el-checkbox {
  display: none;
}

// 级联选择器 不显示radio
.el-cascader-panel .el-radio {
  z-index: 10;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
  opacity: 0.000001;
  margin: 0;
}
.el-cascader-panel .el-cascader-node {
  position: relative;
}