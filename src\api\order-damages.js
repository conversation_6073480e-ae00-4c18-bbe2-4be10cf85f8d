import request from '@/utils/request'

/**
 * 获取平台人工巡检病害列表
 * @param {Object} params 请求参数
 * @param {number} [params.unitId] 单位ID
 * @param {string} [params.startTime] 起始时间，格式：2025-05-13T10:00:00
 * @param {string} [params.endTime] 截止时间，格式：2025-05-15T19:00:00
 * @param {string} [params.filter] 路线编码、描述、行政编码等组合过滤
 * @returns {Promise<Object>} 响应结果
 */
export function getOrderDamageList(params) {
  return request({
    url: '/order-damages',
    method: 'get',
    params,
  })
}

/**
   * 删除人工巡检病害
   * @param {string} id 病害ID
   */
export function deleteOrderDamage(id) {
  return request({
    url: `/order-damages/${id}`,
    method: 'delete',
  })
}

/**
 * 批量导出人工巡检病害
 * 如果选全部了，就把现在的列表的查询参数带上+allSelected true 除去分页
 * 如果选择几个 传 selectedIds 查询参数不用传
 * @param {Object} data 请求参数
 * @param {Array} data.selectedIds 病害ID列表
 * @param {boolean} data.allSelected 是否选择全部
 * @param {Array} data.ignoredIds 忽略的ID列表
 * @param {string} data.startTime 起始时间，格式：2025-05-13T10:00:00
 * @param {string} data.endTime 截止时间，格式：2025-05-15T19:00:00
 * @param {string} data.filter 路线编码、描述、行政编码等组合过滤
 * @param {string} data.unitId 单位ID
 * @param {string} data.mainRoad 干道类型
 * @returns {Promise<Object>} 响应结果
 */
export function exportOrderDamage(data) {
  return request({
    url: `/order-damages/export`,
    method: 'post',
    responseType: 'blob',
    transformResponse: [data => data],
    timeout: 1200 * 1000, // 20分钟超时 
    data,
  })
}
