const UndoMixin = {
  data() {
    return {
      undo: [], // 撤回操作
      redo: [], // 重做操作
    }
  },
  methods: {
    addMemberUndo({ type, frame, index }) {
      if (this.undo.length >= 10) this.undo.shift()
      this.undo.push({
        type,
        index,
        frame,
      })
      console.log('setUndo', this.undo)
    },
    addMemberRedo(data) {
      if (this.redo.length >= 10) this.redo.shift()
      this.redo.push(data)
    },
    setMemberUndo() {
      const { length } = this.undo
      if (length === 0) return

      const data = this.undo[length - 1]
      const { frame, index, type } = data

      console.log(type)
      switch (type) {
      case 'addFrame':
        this.allStrokeRect.splice(index, 1)
        this.reDraw()
        break
      case 'changeType': // 左侧点击改变类型

        console.log(frame)
        this.typeValue = frame.type
        this.reDraw(index)
        break
      default:
        break
      }
      this.addMemberRedo(data)
      this.undo.pop()

      console.log('setMemberUndo', frame)
    },
    setMemberRedo() {
      const { length } = this.redo
      if (length === 0) return
      const data = this.redo[length - 1]
      const { frame, index, type } = data

      console.log('setMemberRedo', frame)
      switch (type) {
      case 'addFrame':
        this.allStrokeRect.push(frame)
        const index = this.allStrokeRect.length - 1
        // 重新绘制
        this.reDraw(index)
        // 初始化
        this.draw(index)
        this.delDraw(index)
        break

      default:
        break
      }

      this.addMemberUndo(data)
      this.redo.pop()
    },
  },
}

export default UndoMixin
