/*
 * @Author: wangyj
 * @Date: 2022-08-29 16:33:41
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-03-09 13:18:18
 */

import { getIdentifyType } from '@/api/data'
// import { getInstanceByDom } from 'echarts'

const CanvasMixin = {
  data() {
    return {
      // type2color: {
      //   longitudinal: {
      //     color: '#0000FF',
      //     txt: '纵向裂纹',
      //   },
      //   transverse: {
      //     color: '#00FF00',
      //     txt: '横向裂纹',
      //   },
      //   aligator: {
      //     color: '#FF0000',
      //     txt: '龟裂',
      //   },
      //   pothole: {
      //     color: '#FFCC00',
      //     txt: '坑洞',
      //   },
      //   other: {
      //     color: '#ff00cc',
      //     txt: '其他',
      //   },
      // },
      // typeArr: ['longitudinal', 'transverse', 'aligator', 'pothole'],
      allDiseaseType: [
        // { label: '纵向裂纹', type: 'longitudinal' },
        // { label: '横向裂纹', type: 'transverse' },
        // { label: '龟裂', type: 'aligator' },
        // { label: '坑洞', type: 'pothole' },
        // { label: '网裂', type: 'reticular_fiss' },
        // { label: '唧浆', type: 'wash_erosion' },
        // { label: '波浪拥包', type: 'embrace_bag' },
        // { label: '车辙', type: 'rut' },
        // { label: '沉陷', type: 'sink' },
        // { label: '剥落', type: 'exfoliation' },
        // { label: '坑边', type: 'pit_edge' },
        // { label: '路框差', type: 'road_frame_difference' },
        // { label: '泛油', type: 'oily' },
        // { label: '补丁', type: 'patch' },
        // { label: '杂物积水', type: 'bequeath' },
      ],
      cityManagementType: [], // 城管类型
      slowPatrolType: [], // 慢行病害
      allRoadAssetsType: [], // 道路资产类型
      allRoadForeignMatter: [], // 路面风险类型
      allAlongLine: [], // 沿线设施损坏
      allAsphaltRoad: [], // 沥青路面
      subgradeDisease: [], // 路基病害
      bridgeDisease: [], // 桥梁病害
      tunnelDisease: [], // 隧道病害
      otherDisease: [], // 其他病害

      allIdentifyType: [], // 所有识别出的类型
      requested: false, // 防止多次请求
      requested1: false, // 防止多次请求
      markingPavementType: null, // 当前的标注路面类型
    }
  },
  // async created() {
  //   await this.getModelIdentifyTypes()
  // },
  methods: {
    loadImg(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = src // 异步执行，需加载才显示
        img.setAttribute('crossOrigin', 'anonymous')
        img.onload = () => resolve(img) // 后面所有操作均需在图片加载成功后执行，否则图片将处理无效
        img.onerror = () => resolve('')
      })
    },
    // 获取canvas，ctx
    getCtx(image, id, width, height) {
      this.canvasWidth = width
      this.canvasHeight = height
      this.canvas = document.getElementById(id)
      if (this.canvas) {
        this.canvas.width = width
        this.canvas.height = height
        this.ctx = this.canvas.getContext('2d')
      }
    },
    // canvas 绘制矩形和文字
    ctxDraw(type, coordinate, accuracy = null, typeIsName = false) {
      const that = this
      that.ctx.lineWidth = 3
      if (that.canvas.width > 640) {
        that.ctx.font = 'bold 22px Arial'
      } else {
        that.ctx.font = 'bold 22px Arial'
      }

      let txt = ''

      let typeArr = this.allIdentifyType.filter((item) => item.engName === type)
      if (typeIsName) {
        typeArr = this.allIdentifyType.filter((item) => item.chineseName === type)
      }
      if (typeArr.length > 0) {
        that.ctx.strokeStyle = typeArr[0].color
        that.ctx.fillStyle = typeArr[0].color
        txt = typeArr[0].chineseName
      } else if (type.includes('prop-road-width')) {
      // 当type为`prop-road-width:{size_m2}`时，表示为路宽识别；文字显示`路宽 {size_m2}m`
        that.ctx.strokeStyle = '#0000FF'
        that.ctx.fillStyle = '#0000FF'
        txt = `路宽 ${type.split(':')[1]}m`
      } else {
        that.ctx.strokeStyle = '#9C9C9C'
        that.ctx.fillStyle = '#9C9C9C'
        txt = '其他'
      }

      if (accuracy) {
        console.log('accuracy', accuracy)
        txt = `${txt}(${(accuracy * 100).toFixed(1)}%)`
      }
      const coordinateArr = coordinate.split(' ')
      that.ctx.strokeRect(coordinateArr[0] * 1, coordinateArr[1] * 1, coordinateArr[2] - coordinateArr[0], coordinateArr[3] - coordinateArr[1])
      // if (that.canvas.width > 640) {
      //   that.ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[1] * 1 - 4, coordinateArr[2] - coordinateArr[0] - 4)
      // } else {
      //   that.ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[1] * 1 - 4, coordinateArr[2] - coordinateArr[0] - 4)
      // }

      // 文字位置：框上或下，对齐：左对齐或右对齐
      const txtW = that.ctx.measureText(txt).width
      if (coordinateArr[0] * 1 + txtW > that.canvas.width) {
        that.ctx.textAlign = 'right'
        if (coordinateArr[1] * 1 < 25) {
          // const oldFillStyle = that.ctx.fillStyle
          // that.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          // that.ctx.fillRect(coordinateArr[2] * 1 - txtW, coordinateArr[3] * 1, txtW, 25)
          // that.ctx.fillStyle = oldFillStyle

          if (that.canvas.width > 640) {
            that.ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[3] * 1 + 25)
          } else {
            that.ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[3] * 1 + 25)
          }
        } else {
          // const oldFillStyle = that.ctx.fillStyle
          // that.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          // that.ctx.fillRect(coordinateArr[2] * 1 - txtW, coordinateArr[1] * 1 - 25, txtW, 25)
          // that.ctx.fillStyle = oldFillStyle

          that.ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[1] * 1 - 6)
        }
      } else {
        that.ctx.textAlign = 'left'
        if (coordinateArr[1] * 1 < 25) {
          // const oldFillStyle = that.ctx.fillStyle
          // that.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          // that.ctx.fillRect(coordinateArr[0] * 1, coordinateArr[3] * 1, txtW, 25)
          // that.ctx.fillStyle = oldFillStyle

          if (that.canvas.width > 640) {
            that.ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[3] * 1 + 25)
          } else {
            that.ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[3] * 1 + 25)
          }
        } else {
          // const oldFillStyle = that.ctx.fillStyle
          // that.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          // that.ctx.fillRect(coordinateArr[0] * 1, coordinateArr[1] * 1 - 25, txtW, 25)
          // that.ctx.fillStyle = oldFillStyle

          that.ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[1] * 1 - 6)
        }
      }
    },
    // 慢巡
    // async getSlowPatrolIdentifyTypes(roadType) {
    //   // this.allRoadAssetsType = []
    //   // this.allRoadForeignMatter = []
    //   // this.allAlongLine = []
    //   const res = await getIdentifyType({ roadType: 9, classification: 9 })
    //   this.slowPatrolType = res.payload

    //   const resAll = await getIdentifyType()
    //   this.allIdentifyType = resAll.payload
    //   this.markingPavementType = roadType || this.taskData.roadType
    // },
    // roadType：1沥青路面 2水泥路面 13砂石路面 9慢行 6城市道路
    filterDiseaseType(classification, roadType) {
      return this.allIdentifyType.filter((item) => item.classification === classification && item.roadType === roadType)
    },
    // roadType：1沥青路面 2水泥路面 13砂石路面 9慢行 6城市道路
    async getModelIdentifyTypes(roadType) {
      if (this.markingPavementType === roadType) {
        return
      }
      if (this.requested) return

      const resAll = await getIdentifyType()

      // 所有道路标注类型
      this.allIdentifyType = resAll.payload
      
      if (roadType === 9) {
        // 2025/6/26 慢行病害 展示 沥青路面病害
        this.allDiseaseType = this.allIdentifyType.filter((item) => (item.classification === 1 && (item.roadType == 1 || item.roadType === 3)))
      } else {
        // 沥青路面和水泥混凝土需要判断 roadType === 3
        if (roadType === 1 || roadType === 2) {
          this.allDiseaseType = this.allIdentifyType.filter((item) => (item.classification === 1 && (item.roadType == roadType || item.roadType === 3)))
        } else {
          this.allDiseaseType = this.allIdentifyType.filter((item) => (item.classification === 1 && item.roadType == roadType))
        }
      } 

      const classificationMap = {
        // 1: [], 
        2: [], // 道路资产
        3: [], // 路面风险
        5: [], // 沿线设施损坏
        9: [], // 慢行病害
        10: [], // 城管
        20: [], // 路基病害
        40: [], // 桥梁病害
        50: [], // 隧道病害
        90: [], // 其他病害
      }

      this.allIdentifyType.forEach((item) => {
        if (classificationMap[item.classification]) {
          classificationMap[item.classification].push(item)
        }
      })
      // 道路资产
      this.allRoadAssetsType = classificationMap[2]
      // 路面风险
      this.allRoadForeignMatter = classificationMap[3]
      // 沿线设施损坏
      this.allAlongLine = classificationMap[5]
      // 慢行病害
      this.slowPatrolType = classificationMap[9]
      // 城管
      this.cityManagementType = classificationMap[10]
      // 路基病害
      this.subgradeDisease = classificationMap[20]
      // 桥梁病害
      this.bridgeDisease = classificationMap[40]
      // 隧道病害
      this.tunnelDisease = classificationMap[50]
      // 其他病害
      this.otherDisease = classificationMap[90]
      this.requested = true
      this.markingPavementType = roadType
    },
    async getModelIdentifyTypesAll() {
      const resAll = await getIdentifyType()
      this.allIdentifyType = resAll.payload
    },
    async getModelIdentifyTypesByRoadType(roadType) {
      if (this.requested1) {
        return []
      }
      const { payload } = await getIdentifyType({ classification: 1, roadType })
      this.requested1 = true
      return payload
    },
  },
}

export default CanvasMixin
