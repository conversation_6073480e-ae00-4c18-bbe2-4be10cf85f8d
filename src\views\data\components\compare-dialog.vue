<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="98%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    title="模型识别对比"
    :modal="false"
    :modal-append-to-body="false"
    :append-to-body="false"
    :destroy-on-close="true"
    custom-class="comparison-models"
  >
    <div ref="dialogContainer" class="dialog-container" tabindex="0">
      <div class="modal">
        <div v-show="leftModelVisible" class="v-model" />
        <div class="model-title">
          <AccuracySetDialog
            v-if="dialogVisible"
            ref="accuracySetDialogRef1"
            :title="leftTitle"
            :model-type="roadType"
            custom-class="left-dialog"
            @nextDiseaseRefresh="handelChangeBeforeThreshold"
            @handleDigClose="handleDigClose"
          />
          <div><i class="el-icon-takeaway-box" /> 模型选择</div>
          <div style="margin-left: 15px;">
            <el-select
              v-model="leftModelVersionValue"
              placeholder="请选择版本"
              size="mini"
              style="width: 200px"
              @change="handleChangeModelVersion($event, 2)"
            >
              <el-option
                v-for="item in leftModelVersion"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div v-loading="leftLoading" class="model-img">
          <canvas id="leftCanvas" :width="cW" :height="cH" />
        </div>
        <!-- <div class="threshold-value" style="height: 54px;display: flex;align-items: center;">
          <span style="margin-right: 20px;">置信度 :&nbsp;</span>
          <template v-for="item in roadType === 1 ? pitch : cement">
            <span :key="item.label" style="padding-right: 28px;">
              <span>{{item.label}}:</span>{{item.value}}
            </span>
          </template>
        </div> -->
        <div class="download-btns">
          <el-button type="primary" size="small" @click="handleDonfidence(2)">置信度设置</el-button>
          <el-button type="primary" size="small" @click="handleDownloadModelPic(2)">下载标注</el-button>
          <el-button type="primary" size="small" @click="handleDownloadOrigPic(2)">下载原图</el-button>
        </div>
      </div>
      <div class="modal">
        <div v-show="rightModelVisible" class="v-model" />
        <AccuracySetDialog
          v-if="dialogVisible"
          ref="accuracySetDialogRef2"
          :title="rightTitle"
          :model-type="roadType"
          custom-class="right-dialog"
          @nextDiseaseRefresh="handelChangeBeforeThreshold"
          @handleDigClose="handleDigClose"
        />
        <div class="model-title">
          <div><i class="el-icon-takeaway-box" /> 模型选择</div>
          <div style="margin-left: 15px;">
            <el-select
              v-model="rightModelVersionValue"
              placeholder="请选择R模型版本"
              size="mini"
              style="width: 200px"
              @change="handleChangeModelVersion($event, 1)"
            >
              <el-option
                v-for="item in rightModelVersion"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div v-loading="rightLoading" class="model-img">
          <canvas id="rightCanvas" :width="cW" :height="cH" />
        </div>
        <!-- <div class="threshold-value">
          <div>当前阈值：{{ rightThreshold }}%</div>
          <el-slider v-model="rightThreshold" :format-tooltip="formatTooltip" :disabled="!robuConfig" @change="handelChangeAfterThreshold" />
        </div> -->
        <div class="download-btns">
          <el-button type="primary" size="small" @click="handleDonfidence(1)">置信度设置</el-button>
          <el-button type="primary" style="pointer-events: auto;" size="small" @click="handleDownloadModelPic(1)">下载标注</el-button>
          <el-button type="primary" style="pointer-events: auto;" size="small" @click="handleDownloadOrigPic(1)">下载原图</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>

import ConstantsMixin from '@/mixin/constants'
import { recogRoadDamage } from '@/api/data'
import { getModels, modelIdentify, downloadModelXml } from '@/api/model'
import { exportPic } from '@/utils/index'
import CanvasMixin from '@/mixin/canvas'
import _ from 'lodash'
import AccuracySetDialog from './accuracy-set-dialog.vue'

export default {
  name: 'CompareDialog',
  components: {
    AccuracySetDialog,
  },
  mixins: [CanvasMixin, ConstantsMixin],
  props: {
    deviceKey: {
      type: String,
    },
  },
  data() {
    return {
      roadType: 1,
      accuracy_list: [],
      leftLoading: true,
      rightLoading: true,
      dialogVisible: false,
      dialogPic: null,
      cW: 640,
      cH: 360,
      leftCanvas: null,
      leftCtx: null,
      leftThreshold: 10,
      rightCanvas: null,
      rightCtx: null,
      afterScale: 1,
      rightThreshold: 10, // 默认10, 及0.1
      formData: null, // 传robu模型识别的formData数据
      image: null,
      rightModelVersion: [],
      rightModelVersionValue: '',
      rightModelDetil: '',
      rightModelVisible: false,
      leftModelVersion: [],
      leftModelVersionValue: '',
      leftModelDetil: '',
      leftModelVisible: false,
      leftTitle: '',
      rightTitle: '',
    }
  },
  methods: {
    async show(picture) {
      // console.log('picture', picture);
      const that = this
      this.roadType = picture.roadType
      this.dialogVisible = true
      this.dialogPic = { ...picture }
      await this.getModelIdentifyTypesAll()
      await this.getAllModels()

      this.$nextTick(() => {
        const { clientWidth, clientHeight } = document.getElementsByClassName('model-img')[0]

        that.cW = clientWidth
        that.cH = (that.cW * 9) / 16

        that.leftCanvas = document.getElementById('leftCanvas')
        that.leftCtx = that.leftCanvas.getContext('2d')

        that.rightCanvas = document.getElementById('rightCanvas')
        that.rightCtx = that.rightCanvas.getContext('2d')

        that.loadOriginalImg()
      })
    },
    async loadOriginalImg() {
      const that = this

      that.loadImg(that.dialogPic.pictureUrl).then(async (image) => {
        if (image) {
          that.image = image
          that.initRecogRoadDamageByRobu(1)
          that.initRecogRoadDamageByRobu(2)
        }
      })
    },
    initRecogRoadDamageByRobu(type) {
      const that = this
      const params = {
        id: that.dialogPic.id,
        taskId: this.$route.query.id,
        apiUrl: type === 1 ? this.rightModelVersion.find((item) => item.id === this.rightModelVersionValue).apiUrl : this.leftModelVersion.find((item) => item.id === this.leftModelVersionValue)?.apiUrl,
        modelDetail: type === 1 ? this.rightModelDetil : this.leftModelDetil,
      }

      modelIdentify(params).then((res) => {
        that.afterScale = that.cW / that.image.width
        that.drawImageAndDamages(that.image, type === 1 ? that.rightCtx : that.leftCtx, res.result, that.afterScale)
        type === 1 ? that.rightLoading = false : that.leftLoading = false
      })
    },
    drawImageAndDamages(image, ctx, damages, scale) {
      const that = this
      ctx.drawImage(image, 0, 0, that.cW, that.cH)
      if (!damages?.length) return
      damages.forEach((damage) => {
        let txt = ''
        let color = ''
        let typeArr = []
        console.log('damage', damage)
        typeArr = that.allIdentifyType.filter((item) => item.label === damage.label)

        if (typeArr.length > 0) {
          color = typeArr[0].color
          txt = typeArr[0].chineseName
        } else {
          color = '#9C9C9C'
          txt = '其他'
        }

        const dataText = `(${damage.size_m2.toFixed(2)}㎡, ${(damage.accuracy).toFixed(2)})`

        const x = damage.x1
        const y = damage.y1
        const w = damage.x2 - damage.x1
        const h = damage.y2 - damage.y1

        let tX = damage.x1
        let tY = damage.y1
        let tOffset = -4

        ctx.lineWidth = 3
        ctx.strokeStyle = color
        ctx.setLineDash([])
        ctx.strokeRect(
          x * scale,
          y * scale,
          w * scale,
          h * scale,
        )

        ctx.font = 'bold 22px Arial'
        ctx.fillStyle = color
        const mainTxtW = ctx.measureText(txt).width
        const dataTxtW = ctx.measureText(dataText).width
        const totalWidth = mainTxtW + dataTxtW

        if (x + totalWidth > image.width) {
          ctx.textAlign = 'right'
          tX = damage.x2 * 1
          if (y < 25) {
            tY = damage.y2 * 1
            tOffset = 25
          } else {
            tY = damage.y1 * 1
            tOffset = -6
          }
        } else {
          ctx.textAlign = 'left'
          tX = damage.x1 * 1
          if (y < 25) {
            tY = damage.y2 * 1
            tOffset = 25
          } else {
            tY = damage.y1 * 1
            tOffset = -6
          }
        }

        ctx.font = 'bold 22px Arial'
        ctx.fillText(
          txt,
          tX * scale,
          tY * scale + tOffset
        )

        ctx.font = '18px Arial'
        if (ctx.textAlign === 'right') {
          ctx.fillText(
            dataText,
            (tX * scale) - mainTxtW,
            tY * scale + tOffset
          )
        } else {
          ctx.fillText(
            dataText,
            (tX * scale) + mainTxtW,
            tY * scale + tOffset
          )
        }
      })
    },

    initRecogRoadDamage() {
      const that = this

      recogRoadDamage({ formData: that.formData, imageThres: that.rightThreshold / 100 }).then((res) => {
        const { list } = res.payload
        that.afterScale = that.cW / that.image.width
        that.drawImageAndDamages(that.image, that.rightCtx, list, that.afterScale, 1)
        that.rightLoading = false
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.rightModelVisible = false
      this.leftModelVisible = false
      this.leftCtx.clearRect(0, 0, this.cW, this.cH)
      this.rightCtx.clearRect(0, 0, this.cW, this.cH)
      this.leftThreshold = 10
      this.rightThreshold = 10
      this.formData = null
      this.image = null

      sessionStorage.setItem('ModelVersionValue', JSON.stringify({
        leftModelVersionValue: this.leftModelVersionValue,
        rightModelVersionValue: this.rightModelVersionValue,
      }))
    },
    formatTooltip(val) {
      return `${val}%`
    },
    handelChangeBeforeThreshold(params) {
      // console.log('params', params);
      const { type } = params
      delete params.type
      if (type === 'left-dialog') {
        const index = this.leftModelVersion.findIndex((item) => item.id === this.leftModelVersionValue)
        if (index !== -1) this.leftModelVersion[index].modelDetail = params.modelDetail
        this.leftModelDetil = params.modelDetail
        this.leftLoading = true
        this.leftModelVisible = false
      } else {
        const index = this.rightModelVersion.findIndex((item) => item.id === this.rightModelVersionValue)
        if (index !== -1) this.rightModelVersion[index].modelDetail = params.modelDetail
        this.rightModelDetil = params.modelDetail
        this.rightLoading = true
        this.rightModelVisible = false
      }

      params.taskId = this.$route.query.id
      params.id = this.dialogPic.id

      modelIdentify(params).then((res) => {
        this.afterScale = this.cW / this.image.width
        this.drawImageAndDamages(this.image, type === 'left-dialog' ? this.leftCtx : this.rightCtx, res.result, this.afterScale)
        type === 'left-dialog' ? this.leftLoading = false : this.rightLoading = false
      })
    },
    handleDigClose(className) {
      if (className === 'left-dialog') {
        this.leftModelVisible = false
      } else {
        this.rightModelVisible = false
      }
    },
    handelChangeAfterThreshold(val) {
      this.rightLoading = true
      this.initRecogRoadDamageByRobu()
    },
    handleDownloadOrigPic(type) {
      // type 1 => before, 2 => after
      exportPic(this.dialogPic.pictureUrl, this.dialogPic.picture)
    },
    handleDonfidence(index) {
      // index 2 => left, 1 => right
      if (!(this.leftModelVersionValue || this.rightModelVersionValue)) return
      if (index === 1) {
        const temp = this.rightModelVersion.find((item) => item.id === this.rightModelVersionValue)
        this.$refs.accuracySetDialogRef2.show(temp)
        this.rightModelVisible = true
      } else {
        const temp = this.leftModelVersion.find((item) => item.id === this.leftModelVersionValue)
        this.$refs.accuracySetDialogRef1.show(temp)
        this.leftModelVisible = true
      }
    },
    // 下载标注
    handleDownloadModelPic(type) {
      // type 2 => left, 1 => right
      const params = {
        id: this.dialogPic.id,
        taskId: this.$route.query.id,
        apiUrl: type === 1 ? this.rightModelVersion.find((item) => item.id === this.rightModelVersionValue).apiUrl : this.leftModelVersion.find((item) => item.id === this.leftModelVersionValue)?.apiUrl,
        modelDetail: type === 1 ? this.rightModelDetil : this.leftModelDetil,
      }

      downloadModelXml(params).then((data) => {
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(data, 'application/xml')
        // 获取filename的文本内容
        const filenameElement = xmlDoc.getElementsByTagName('filename')[0]

        const blob = new Blob([data], { type: 'application/xml' })

        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = filenameElement?.textContent || 'annotation.xml'
        document.body.appendChild(a)
        a.click() // 触发下载
        window.URL.revokeObjectURL(url) // 释放URL对象
        a.remove() // 移除创建的<a>标签
      })
    },
    async getAllModels() {
      const { payload } = await getModels({modelType: this.roadType })
      const that = this
      const tempPayload = payload.map((item) => {
        item.label = `${that.Model_Cn_Name[item.modelType]}  ${item.versionName} (${item.status === '1' ? '启用中' : '暂未启用'})`
        return item
      }).filter((item) => item !== undefined)

      this.leftModelVersion = _.cloneDeep(tempPayload)
      this.rightModelVersion = _.cloneDeep(tempPayload)

      if (tempPayload?.length) {
        const modelVersionValue = sessionStorage.getItem('ModelVersionValue')
        if (modelVersionValue) {
          const { leftModelVersionValue, rightModelVersionValue } = JSON.parse(modelVersionValue)

          const setModelVersion = (modelVersionList, modelVersionValue, tempPayload) => {
            const isExist = modelVersionList.find((item) => item.id === modelVersionValue)
            return isExist || tempPayload[0]
          }

          // 如果ModelVersionValue存在，根据其值设置左右模型版本
          const leftModel = setModelVersion(this.leftModelVersion, leftModelVersionValue, tempPayload)
          this.leftModelVersionValue = leftModel.id
          this.leftModelDetil = leftModel.modelDetail

          const rightModel = setModelVersion(this.rightModelVersion, rightModelVersionValue, tempPayload)
          this.rightModelVersionValue = rightModel.id
          this.rightModelDetil = rightModel.modelDetail
        } else {
          // 如果没有ModelVersionValue，则根据tempPayload的长度设置默认模型版本
          if (tempPayload.length === 1) {
            const defaultModel = tempPayload[0]
            this.leftModelVersionValue = defaultModel.id
            this.rightModelVersionValue = defaultModel.id
            this.leftModelDetil = defaultModel.modelDetail
            this.rightModelDetil = defaultModel.modelDetail
          } else if (tempPayload.length > 1) {
            const defaultLeftModel = tempPayload[0]
            const defaultRightModel = tempPayload[1]
            this.leftModelVersionValue = defaultLeftModel.id
            this.rightModelVersionValue = defaultRightModel.id
            this.leftModelDetil = defaultLeftModel.modelDetail
            this.rightModelDetil = defaultRightModel.modelDetail
          }
        }
      }
    },
    handleChangeModelVersion(id, type) {
      if (type === 1) {
        this.rightLoading = true
        this.rightModelDetil = this.rightModelVersion.find((item) => item.id === id).modelDetail
      } else {
        this.leftLoading = true
        this.leftModelDetil = this.leftModelVersion.find((item) => item.id === id).modelDetail
      }
      this.initRecogRoadDamageByRobu(type)
    },
  },

}
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog {
    margin-top: 10vh!important;
  }
  .dialog-container {
    display: flex;
    justify-content: space-between;

    &>div {
      width: 49.6%;
      .model-title {
        min-height: 34px;
        font-size: 16px;
        margin-bottom: 10px;
        border-bottom: 1px solid #2d8cf0;
        padding-bottom: 5px;
        display: flex;
        align-items: center;
        // div {
        //   margin-right: 20px;
        // }
      }

      .model-img{
        width: 100%;
        aspect-ratio: 16/9;
        height: auto;
        margin-bottom: 7px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .threshold-value {
        margin-top: 15px;
      }

      .download-btns {
        text-align: right;
      }
    }
  }

  .modal {
    position: relative;
    .v-model {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      opacity: 0.5;
      background: #000000;
      z-index: 2001;
    }
    .left-dialog {
      position: absolute;
      left: 0px !important;
      pointer-events: auto;
      ::v-deep .el-dialog {
        left: 0;
        top: 0;
        margin-top: 10vh !important;
        width: 85% !important;
      }
      ::v-deep .el-dialog__body {
        max-height: 30vh;
        overflow: hidden;
        overflow-y: auto;
      }
    }
    .right-dialog {
      @extend .left-dialog;
    }
    ::v-deep .v-modal {
      position: absolute;
    }
  }

  ::v-deep .comparison-models {
    .el-dialog__body {
      padding: 5px 20px 20px;
    }
  }
</style>
