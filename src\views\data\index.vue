/* * @Author: wangyj * @Date: 2022-10-25 17:59:31 * @Last Modified by: wangyj *
@Last Modified time: 2023-02-22 15:38:13 */
<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom: 30px">
      <el-row type="flex" :gutter="10">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <UnitSelect
            v-model="listQuery.workUnitId"
            use-node-id
            placeholder="设备单位"
            clearable
            filterable
            @input="handleWorkUnitChange"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-select
            v-model="listQuery.deviceKey"
            clearable
            filterable
            placeholder="设备名称"
            @change="handleChangeKey"
          >
            <el-option
              v-for="device in deviceOptions"
              :key="device.id"
              :value="device.deviceKey"
              :label="device.deviceName"
            />
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.deviceKeyInput"
            :disabled="deviceKeyInputDisabled"
            placeholder="设备ID"
            clearable
          />
        </el-col> 

        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="开始时间"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col> 
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="结束时间"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col> 
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.roadFilter"
            clearable
            placeholder="路线名称、编号"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.adCode"
            clearable
            placeholder="行政区划编码"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-select
            v-model="listQuery.roadType"
            clearable
            placeholder="路面类型"
          >
            <el-option :value="1" label="沥青路面" />
            <el-option :value="2" label="水泥混凝土路面" />
            <el-option :value="9" label="慢行" />
            <el-option :value="10" label="城市管理" />
          </el-select>
        </el-col>
        <div class="button-group">
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            查询
          </el-button>
          <el-radio-group
            v-model="radioType"
            :disabled="!radioShow"
            class="ml-10 mr-5"
            @input="handelRadioType"
          >
            <el-radio-button label="表" />
            <el-radio-button label="图" />
          </el-radio-group>
          <el-button
            v-auth="
              'inspect-tasks-select-batch-export:view:inspect-tasks-select-batch-export'
            "
            class="filter-item"
            icon="el-icon-download"
            @click="handleBatchDownload"
          >
            批量下载
          </el-button>
        </div>
      </el-row>
    </div>
    <template v-if="radioType === '表'">
      <el-table
        ref="multipleTable"
        v-loading="listLoading"
        :data="list"
        border
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="
            $auth(
              'inspect-tasks-select-batch-export:view:inspect-tasks-select-batch-export'
            )
          "
          align="center"
          type="selection"
          width="40"
        />
        <el-table-column align="center" label="任务ID" prop="id">
          <template slot-scope="{ row }">
            {{ row.id }}
            <el-tag v-if="row.roadType === 9" size="mini">慢行</el-tag>
            <el-tag
              v-if="row.roadType === 10"
              type="success"
              size="mini"
            >
              城管
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="设备单位"
          prop="workUnit"
          width="190"
        />
        <el-table-column
          align="center"
          label="设备名称"
          prop="deviceName"
          width="180"
        >
          <template slot-scope="{ row }">
            <span @dblclick="handleCopyText(row.deviceName)" style="cursor: pointer;" :title="'双击复制'">{{ row.deviceName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="路线名称" prop="roadName" />
        <el-table-column align="center" label="路线编号" prop="roadCode" />
        <el-table-column
          align="center"
          label="行政区划编码"
          prop="adCode"
          width="140"
        />
        <el-table-column align="center" label="巡检开始时间" width="140">
          <template slot-scope="{ row }">
            {{
              new Date(row.startTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
            }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="巡检结束时间" width="140">
          <template slot-scope="{ row }">
            <template v-if="row.hasEnd">
              {{
                new Date(row.endTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
              }}
            </template>
            <template v-else>巡检中</template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="起点桩号" prop="startPileNum" />
        <el-table-column align="center" label="止点桩号" prop="endPileNum" />
        <el-table-column
          align="center"
          label="巡检里程(km)"
          prop="inspectMileage"
          width="107"
        />
        <el-table-column
          v-if="$auth('inspect-tasks-push:view:inspect-tasks-push')"
          align="center"
          label="推送状态"
          width="80"
        >
          <template slot-scope="{ row }">
            {{ row.patrolDataSyncResult ? "已推送" : "未推送" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="400">
          <template slot-scope="{ row, $index }">
            <el-button
              type="text"
              @click="handleDetail(row, $index)"
            >
              详情
            </el-button>
            <el-button
              type="text"
              @click="handleEdit(row, $index)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              @click="handlePlayback(row, $index, 1)"
            >
              道路回放
            </el-button>
            <el-button
              type="text"
              @click="handlePictures(row, $index)"
            >
              道路图集
            </el-button>
            <el-button
              type="text"
              @click="handleDataRefresh(row, $index)"
            >
              数据刷新
            </el-button>
            <el-button
              type="text"
              @click="handleDiseaseRefresh(row, $index)"
            >
              病害刷新
            </el-button>
            <el-button
              v-auth="'inspect-tasks-push:view:inspect-tasks-push'"
              type="text"
              @click="handleDataPush(row, $index)"
            >
              数据推送
            </el-button>
            <el-button
              v-auth="
                'inspect-tasks-area-refresh:view:inspect-tasks-area-refresh'
              "
              type="text"
              @click="handleAreaRefresh(row, $index)"
            >
              面积刷新
            </el-button>
            <el-button
              type="text"
              @click="handleCommand('huifang', row, $index)"
            >
              景观回放
            </el-button>
            <el-button
              type="text"
              @click="handleCommand('tuji', row, $index)"
            >
              景观图集
            </el-button>
            <el-button
              type="text"
              @click="handleDelete(row, $index)"
            >
              删除
            </el-button>

            <!-- <el-dropdown style="margin-left: 10px" @command="handleCommand($event, row, $index)">
              <span class="el-dropdown-link">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="huifang">景观回放</el-dropdown-item>
                <el-dropdown-item command="tuji">景观图集</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
            <!-- <el-button type="text" @click="handleExport(row, $index)">生成报告</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.currentPage"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </template>
    <template v-if="radioType === '图'">
      <baidu-map
        v-loading="mapLoading"
        class="bm-view"
        :center="center"
        :zoom="zoom"
        :scroll-wheel-zoom="true"
        @ready="handler"
      >
        <!-- 原始配色 '#7EBE2A', '#28CAE3', '#705FFF', '#F4D539', '#FF1C1C' -->
        <!-- 所有绿点 -->
        <bm-point-collection
          :points="greenPointData"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="GLOBAL_VARIATE.colorA"
          size="BMAP_POINT_SIZE_NORMAL"
        />
        <!-- 所有蓝点 -->
        <bm-point-collection
          :points="bluePointData"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="GLOBAL_VARIATE.colorB"
          size="BMAP_POINT_SIZE_NORMAL"
        />
        <!-- 所有紫点 -->
        <bm-point-collection
          :points="purplePointData"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="GLOBAL_VARIATE.colorC"
          size="BMAP_POINT_SIZE_NORMAL"
        />
        <!-- 所有黄点 -->
        <bm-point-collection
          :points="yellowPointData"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="GLOBAL_VARIATE.colorD"
          size="BMAP_POINT_SIZE_NORMAL"
        />
        <!-- 所有红点 -->
        <bm-point-collection
          :points="redPointData"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="GLOBAL_VARIATE.colorE"
          size="BMAP_POINT_SIZE_NORMAL"
        />
        <!-- 无数条线段集合，每一节颜色都不一样 -->
        <bm-polyline
          v-for="(item, id) in mapLineData"
          :key="id + item.color"
          :path="item.line"
          :stroke-color="item.color"
          :stroke-opacity="1"
          :stroke-weight="5"
        />
      </baidu-map>
    </template>
    <CreateDialog
      ref="createDialog"
      :form-data="temp"
      @refreshTable="refreshTable"
      @refreshData="onRefreshData"
    />
    <PlayDialog
      ref="playDialog"
      :data-obj="dataObj"
      :disease-arr="diseaseArr"
      :type="playbackType"
    />
    <LoadingDialog :text="dialogText" :dialog-visible="dialogVisible" />
    <CircleProgressDialog ref="circleProgress" :text="ProgressText" />
    <BatchDownloadDialog ref="BatchDownloadDialog" :selection="selection" />
    <RefreshDiseaseDialog
      ref="RefreshDiseaseDialog"
      @nextDiseaseRefresh="onRefreshDisease"
    />
  </div>
</template>

<script>
import {
  getDataList,
  getTaskDevices,
  refreshData,
  refreshDisease,
  getDiseaseQueryProgresss,
  deleteData,
  refreshArea,
  getAreaRefreshProgress,
  pushData,
} from "@/api/data";
import { getDeviceWorkUnits } from "@/api/device";
import { getAnalysis } from "@/api/analysis";
import { exportFile } from "@/utils/index";
import { simpleStyleJson } from "@/utils/map-style";
import { mapState } from "vuex";
import Pagination from "@/components/Pagination/index.vue";
import LoadingDialog from "@/components/LoadingDialog/index.vue";
import CircleProgressDialog from "@/components/ProgressDialog/circle.vue";
import CreateDialog from "./components/create-dialog.vue";
import PlayDialog from "./components/play-dialog.vue";
import BatchDownloadDialog from "./components/batch-download-dialog.vue";
import RefreshDiseaseDialog from "./components/refresh-disease-dialog.vue";
import { copyText } from "@/utils/clipboard";

// 引入日期选择器选项mixin
import datePickerOptions from '@/mixin/datePickerOptions'
import DateTimePicker from '@/components/DateTimePicker/index.vue'

export default {
  name: "Data",
  mixins: [datePickerOptions],
  components: {
    Pagination,
    CreateDialog,
    PlayDialog,
    LoadingDialog,
    CircleProgressDialog,
    BatchDownloadDialog,
    RefreshDiseaseDialog,
    DateTimePicker,
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      currentDiseaseModelType: 1, // 当前病害刷新的路面模型
      currentDiseaseTaskId: 1,
      currentDiseaseRow: {},
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnitId: null,
        deviceKey: null,
        startTime: null,
        endTime: null,
        deviceKeyInput: '',
        roadFilter: '',
        adCode: '',
        roadType: null,
      },
      temp: {},
      deviceOptions: [], // 设备下拉选择项数据
      allDevices: [],
      dateFormat: 'yyyy-MM-dd HH:mm:ss',
      dataObj: {}, // 传给playDialog的数据
      diseaseArr: [], // 传给playDialog的数据
      playbackType: 1, // 传给playDialog的数据, 1 是道路回放，2是景观回放
      unitOptions: [], // 设备单位options
      unitDisabled: false,
      dialogVisible: false,
      dialogText: '',
      radioType: '表',
      radioShow: false,
      map: null,
      BMap: null,
      center: { lng: 116.404, lat: 39.915 },
      zoom: 3,
      mapLineData: [],
      greenPointData: [],
      bluePointData: [],
      purplePointData: [],
      yellowPointData: [],
      redPointData: [],
      mapLoading: true,
      deviceKeyInputDisabled: false,
      selection: [],
      ProgressText: '病害面积刷新进度',
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      userType: (state) => state.account.userType,
      workUnitId: (state) => state.account.workUnitId,
    }),
  },
  async created() {
    await this.getUnitsOptions()

    if (!this.admin) {
      this.listQuery.workUnitId = this.workUnitId
      this.radioType = '表'
      this.radioShow = true
    }
    this.getList()
  },
  methods: {
    async getUnitsOptions() {
      const { payload } = await getDeviceWorkUnits()
      this.unitOptions = payload

      // 初始化设备列表
      const devices = this.admin
        ? this.unitOptions.flatMap((unit) => unit.devices || [])
        : (this.unitOptions.find((unit) => unit.workUnitId === this.workUnitId)?.devices || [])

      this.deviceOptions = devices
      this.allDevices = [...devices]
    },
    handleCopyText(text) {
      copyText(text);
    },
    handleWorkUnitChange(value) {
      this.listQuery.deviceKey = ''

      if (value) {
        const unit = this.unitOptions.find((unit) => unit.workUnitId === value)
        this.deviceOptions = unit?.devices || []
      } else {
        this.deviceOptions = [...this.allDevices]
      }
    },
    handleSelectionChange(val) {
      this.selection = val
      console.log(this.selection)
      // this.selection = val.map(({id}) => id)
    },
    handelRadioType(value) {
      if (value === '表') {
        this.$nextTick(() => {
          this.selection.forEach((row) => {
            console.log(row)
            this.$refs.multipleTable.toggleRowSelection(row)
          })
        })
      }
    },
    getList() {
      this.listLoading = true
      const {
        pageSize,
        currentPage,
        workUnitId,
        deviceKey,
        startTime,
        endTime,
        deviceKeyInput,
        roadFilter,
        adCode,
        roadType,
      } = this.listQuery

      const query = {
        page: currentPage - 1,
        size: pageSize,
        workUnitId,
        deviceKey: null,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
        roadFilter: roadFilter === '' ? null : roadFilter,
        adCode: adCode === '' ? null : adCode,
        roadType,
      }

      if (deviceKey === null || deviceKey === '') {
        if (deviceKeyInput !== '') {
          query.deviceKey = deviceKeyInput
        }
      } else {
        query.deviceKey = deviceKey
      }

      getDataList(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    onRefreshData(row) {
      this.handleDataRefresh(row)
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()

      if (this.listQuery.workUnitId || this.listQuery.deviceKey) {
        this.radioShow = true
        if (this.radioType === '图') {
          this.getMapData()
        }
      } else {
        this.radioShow = false
        this.radioType = '表'
      }
    },
    // 批量下载
    handleBatchDownload() {
      if (this.selection.length === 0) {
        this.$message({
          message: '至少选中一个',
          type: 'warning',
        })
      } else {
        this.$refs.BatchDownloadDialog.dialogVisible = true
      }
    },
    async handleDetail(row, index) {
      await this.$store.dispatch('detail/setTaskData', row.id)
      this.$router.push(`/data/detail/${row.id}`)
    },
    handlePlayback(row, index, type) {
      // getDiseaseList({ taskId: row.id }).then((res) => {
      //   const diseaseArr = res.payload
      //   if (diseaseArr.length === 0) {
      //     this.$message({
      //       message: '该段巡检数据暂无病害数据',
      //       type: 'info',
      //     })
      //   } else {
      //     this.dataObj = row
      //     this.diseaseArr = diseaseArr
      //     this.$refs.playDialog.show()
      //   }
      // })

      this.dataObj = row
      this.playbackType = type

      console.log(this.playbackType)
      // this.diseaseArr = diseaseArr
      this.$refs.playDialog.show()
    },
    async handleDataRefresh(row, index) {
      this.dialogText = '数据刷新中，请稍等。。。'
      this.dialogVisible = true
      const { status } = await refreshData(row.id)
      if (status === 200) {
        this.dialogVisible = false
        this.$message({
          message: '数据刷新成功',
          type: 'success',
        })
        this.getList()
      }
    },

    handleDataPush(row, index) {
      this.dialogText = '数据推送中，请稍等。。。'
      this.dialogVisible = true
      pushData(row.id)
        .then((res) => {
          if (res.status === 200) {
            this.dialogVisible = false
            this.$message({
              message: '数据推送成功',
              type: 'success',
            })
            this.getList()
          }
        })
        .catch((error) => {
          this.dialogVisible = false
        })
    },
    handleExport(row, index) {
      if (row.inspectMileage < 0.1) {
        this.$message({
          message: '巡检里程不足100米，不能生成报告',
          type: 'error',
        })
        return
      }
      exportFile(`road-statises/${row.id}/exportDoc`)
    },
    handler({ BMap, map }) {
      // console.log(BMap, map)
      this.map = map
      this.BMap = BMap
      this.center.lng = 116.404
      this.center.lat = 39.915
      this.zoom = 6

      // map.setMapStyle({
      //   style: 'grayscale',
      // })
      // map.setMapStyleV2({
      //   styleId: '84641241ef00321945d05b9cb13fab3b',
      // })
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
      this.getMapData()
    },
    async getMapData() {
      const that = this
      that.greenPointData = []
      that.yellowPointData = []
      that.redPointData = []
      that.purplePointData = []
      that.bluePointData = []
      that.mapLineData = []

      that.mapLoading = true

      const mapGLineData = []
      const mapBLineData = []
      const mapPLineData = []
      const mapYLineData = []
      const mapRLineData = []

      const {
        workUnitId, deviceKey, startTime, endTime, deviceKeyInput,
      } = this.listQuery
      const params = {
        workUnitId,
        deviceKey: null,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
      }
      if (deviceKey === null || deviceKey === '') {
        if (deviceKeyInput !== '') {
          params.deviceKey = deviceKeyInput
        }
      } else {
        params.deviceKey = deviceKey
      }
      const { payload } = await getAnalysis(params)

      const allPoints = []
      payload.routes.forEach((route) => {
        route.forEach((point) => {
          if (point.pqi >= 90) {
            that.greenPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
              },
            )

            mapBLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              color: this.GLOBAL_VARIATE.colorA,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 80 && point.pqi < 90) {
            // 蓝
            that.bluePointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
              },
            )

            mapBLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              color: this.GLOBAL_VARIATE.colorB,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 70 && point.pqi < 80) {
            // 紫
            that.purplePointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
              },
            )

            mapPLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              color: this.GLOBAL_VARIATE.colorC,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 60 && point.pqi < 70) {
            // 黄
            that.yellowPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
              },
            )

            mapYLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              color: this.GLOBAL_VARIATE.colorD,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else {
            // 红
            that.redPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
              },
            )

            mapRLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              color: this.GLOBAL_VARIATE.colorE,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          }
        })
      })

      that.mapLineData = [
        ...mapGLineData,
        ...mapBLineData,
        ...mapPLineData,
        ...mapYLineData,
        ...mapRLineData,
      ]
      // 地图视野包含所有点
      that.map.setViewport(allPoints)
      that.zoom = that.map.getZoom()

      that.mapLoading = false
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该条数据吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteData(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getList()
      })
    },
    handlePictures(row, index) {
      this.$router.push({
        path: '/atlas/pictures',
        query: {
          id: row.id,
          key: row.deviceKey,
          roadType: row.roadType,
          roadLevel: row.roadLevel,
          startTime: row.startTime,
          endTime: row.endTime,
        },
      })
    },
    handleLandscape(row, index) {
      this.$router.push({
        path: '/atlas/landscape',
        query: {
          id: row.id,
          key: row.deviceKey,
          roadType: row.roadType,
          startTime: row.startTime,
          endTime: row.endTime,
        },
      })
    },
    handleCommand(command, row, index) {
      console.log(command, row, index)
      if (command === 'huifang') this.handlePlayback(row, index, 2)
      else if (command === 'tuji') this.handleLandscape(row, index)
    },
    async handleAreaRefresh(row, index, type = 'area') {
      const that = this
      that.ProgressText = '病害面积刷新进度'
      const { payload } = await getAreaRefreshProgress({ taskId: row.id })
      if (payload) {
        if (payload.result) {
          const progress = payload.progress * 100
          if (progress < 100) {
            that.onNextRefresh({ id: row.id, type })
          } else {
            this.$confirm('上次病害面积刷新已完成，是否重新刷新?', '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning',
            })
              .then(() => {
                refreshArea({ id: row.id }).then(() => {
                  that.onNextRefresh({ id: row.id, type })
                })
              })
              .catch(() => {})
          }
        } else {
          this.handleRefreshFail(row.id, type)
        }
      } else {
        refreshArea({ id: row.id }).then((res) => {
          that.onNextRefresh({ id: row.id, type })
        })
      }
    },

    onNextRefresh({ id, type }) {
      this.$refs.circleProgress.onShow()
      this.getAreaOrDiseaseProgress(id, type)
    },
    async onRefreshDisease(params) {
      const { status } = await refreshDisease(params)
      if (status === 200) {
        this.onNextRefresh({
          id: this.currentDiseaseTaskId,
          type: this.currentDiseaseModelType,
        })
      }
    },
    async handleDiseaseRefresh(row, index, type = 'disease') {
      console.log(row)
      this.ProgressText = '病害刷新进度'
      this.currentDiseaseRow = row
      const { payload } = await getDiseaseQueryProgresss({ taskId: row.id })
      if (payload) {
        if (payload.result) {
          // result 存在就是 上次次已经刷新过
          const progress = payload.progress * 100
          if (progress < 100) {
            this.onNextRefresh({ id: row.id, type })
          } else {
            this.$confirm('上次病害刷新已完成，是否重新刷新?', '提示', {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning',
            })
              .then(() => {
                this.currentDiseaseModelType = row.roadType
                this.currentDiseaseTaskId = row.id
                this.$refs.RefreshDiseaseDialog.show(row)
              })
              .catch(() => {})
          }
        } else {
          this.handleRefreshFail(row.id, type)
        }
      } else {
        // 没有就是要刷新
        this.currentDiseaseModelType = row.roadType
        this.currentDiseaseTaskId = row.id
        this.$refs.RefreshDiseaseDialog.show(row)
      }
    },

    async getAreaOrDiseaseProgress(taskId, type) {
      console.log('getAreaOrDiseaseProgress', taskId, type)
      const that = this
      const { status, payload } = type === 'area'
        ? await getAreaRefreshProgress({ taskId })
        : await getDiseaseQueryProgresss({ taskId })

      console.log(status, payload)
      if (status === 200) {
        if (payload.result) {
          console.log('---')
          const progress = parseInt(payload.progress * 100, 10)
          that.$refs.circleProgress.onUpdatePercentage(progress)
          if (progress < 100) {
            setTimeout(() => {
              if (that.$refs.circleProgress.visible) {
                that.getAreaOrDiseaseProgress(taskId, type)
              }
            }, 1000)
          } else {
            setTimeout(() => {
              that.$refs.circleProgress.onClose()
            }, 1500)
          }
        } else {
          this.handleRefreshFail(taskId, type, () => {
            that.$refs.circleProgress.onClose()
          })
        }
      }
    },
    handleRefreshFail(id, type, cancelCallFun) {
      const that = this
      this.$confirm('刷新失败是否重新刷新?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
      })
        .then(() => {
          refreshArea({ id }).then(() => {
            that.$refs.circleProgress.onShow()
            that.getAreaOrDiseaseProgress(id, type)
          })
        })
        .catch(() => {
          if (cancelCallFun) {
            cancelCallFun()
          }
        })
    },
    handleChangeKey(value) {
      if (value) {
        this.deviceKeyInputDisabled = true
        this.listQuery.deviceKeyInput = ''
      } else {
        this.deviceKeyInputDisabled = false
      }
    },
    handleEdit(row, index) {
      console.log('handleEdit', row)
      this.temp = row
      this.$refs.createDialog.show()
    },
  },
}
</script>
<style>
.bm-view {
  width: 100%;
  height: 720px;
}
.el-dropdown-link {
  cursor: pointer;
  color: #2d8cf0;
  font-weight: normal !important;
  font-family: "Microsoft Yahei", tahoma, arial, "Hiragin Sans GB";
}
.el-icon-arrow-down {
  font-size: 12px;
}

.el-table__row .el-button + .el-button {
  margin-left: 0;
}
.el-table__row .el-button {
  margin-right: 10px;
}
</style>
