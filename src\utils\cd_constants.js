/**
 * 道路类型和病害归属相关常量
 * 
 * 本文件包含以下内容：
 * 1. 基础配置：ROAD_CONFIG - 包含所有道路类型的完整信息
 * 2. 导出常量：各种映射对象，用于不同场景
 * 3. 工具函数：用于获取道路类型相关信息的辅助函数
 */

// =====================================================================
// 导入资源
// =====================================================================

// 导入地图marker-病害类型图标
import mainMarker from '@/assets/cheng-de-screen/main-marker.png'
import subMarker from '@/assets/cheng-de-screen/sub-marker.png'
import slMarker from '@/assets/cheng-de-screen/sl-marker.png'
import gxMarker from '@/assets/cheng-de-screen/gx-marker.png'
import dsMarker from '@/assets/cheng-de-screen/ds-marker.png'
import bridgeMarker from '@/assets/cheng-de-screen/bridge-marker.png'
import tunnelMarker from '@/assets/cheng-de-screen/tunnel-marker.png'
// =====================================================================
// 基础配置
// =====================================================================

/**
 * 统一的道路配置
 * 包含所有道路类型的完整信息：类型、标签、颜色、图标等
 */
export const ROAD_CONFIG = {
  // 主干道
  main_road: {
    type: 'main_road',
    label: '主干道',
    color: '#D8B4FE', 
    icon: mainMarker,
    // activeIcon: activeMarker
  },
  // 次干道
  sec_road: {
    type: 'sec_road',
    label: '次干道',
    color: '#8CC8FF',
    icon: subMarker,
    // activeIcon: activeMarker
  },
  // 双滦区
  ad_code_130803: {
    type: 'ad_code_130803',
    label: '双滦区',
    color: '#84FFB1',
    icon: slMarker,
    // activeIcon: activeMarker
  },
  ad_code_130871: {
    type: 'ad_code_130871',
    label: '高新区',
    color: '#FFA184',
    icon: gxMarker,
    // activeIcon: activeMarker
  },
  // 东山ppp项目
  dongshan_ppp: {
    type: 'dongshan_ppp',
    label: '东山ppp项目',
    color: '#FFDA00',
    icon: dsMarker,
    // activeIcon: activeMarker
  },
  // 桥梁
  bridge: {
    type: 'bridge',
    label: '桥梁',
    color: '#ccfe77',
    icon: bridgeMarker,
    // activeIcon: activeMarker
  },
  // 隧道
  tunnel: {
    type: 'tunnel',
    label: '隧道',
    color: '#FFA18A',
    icon: tunnelMarker,
    // activeIcon: activeMarker
  }
}

// =====================================================================
// 导出常量
// =====================================================================

/**
 * 病害归属类型映射 (为保持向后兼容)
 * @example { main_road: 'main_road', sec_road: 'sec_road', ... }
 */
export const BELONG_TO_TYPE_MAP = Object.keys(ROAD_CONFIG).reduce((map, key) => {
  map[key] = ROAD_CONFIG[key].type;
  return map;
}, {});

/**
 * 承德归属选项，用于下拉选择框
 * @example [{ value: 'main_road', label: '主干道' }, ...]
 */
export const ROAD_TYPE_OPTIONS = Object.values(ROAD_CONFIG).map(config => ({
  value: config.type,
  label: config.label
}));

/**
 * 病害归属类型对应的图标映射
 * @example { main_road: mainMarker, sec_road: subMarker, ... }
 */
export const BELONG_TO_ICON_MAP = Object.keys(ROAD_CONFIG).reduce((map, key) => {
  map[key] = ROAD_CONFIG[key].icon;
  return map;
}, {});

/**
 * 病害归属类型对应的激活图标
 * @example { main_road: activeMarker, sec_road: activeMarker, ... }
 */
export const BELONG_TO_ACTIVE_ICON_MAP = Object.keys(ROAD_CONFIG).reduce((map, key) => {
  map[key] = ROAD_CONFIG[key].activeIcon;
  return map;
}, {});

/**
 * statisType与belongTo的映射关系
 * @example { main_road: 'main_road', sec_road: 'sec_road', ... }
 */
export const STATIS_TYPE_MAP = Object.keys(ROAD_CONFIG).reduce((map, key) => {
  map[key] = ROAD_CONFIG[key].type;
  return map;
}, {});

/**
 * belongTo到statisType的反向映射
 * @example { 'main_road': 'main_road', 'sec_road': 'sec_road', ... }
 */
export const BELONG_TO_STATIS_TYPE_MAP = Object.entries(STATIS_TYPE_MAP).reduce((acc, [key, value]) => {
  acc[value] = key;
  return acc;
}, {});

/**
 * 病害归属类型对应的颜色和标签映射
 * @example { main_road: { color: '#9013fe', label: '主干道' }, ... }
 */
export const BELONG_TO_COLOR_MAP = Object.keys(ROAD_CONFIG).reduce((map, key) => {
  map[key] = {
    color: ROAD_CONFIG[key].color,
    label: ROAD_CONFIG[key].label
  };
  return map;
}, {});

// =====================================================================
// 工具函数
// =====================================================================

/**
 * 获取病害图标函数
 * @param {string} belongTo - 病害归属类型
 * @param {boolean} isActive - 是否激活状态
 * @returns {string} 对应的图标路径
 */
export const getMarkerIcon = (belongTo, isActive = false) => {
  if (!ROAD_CONFIG[belongTo]) {
    return isActive ? activeMarker : mainMarker;
  }
  return isActive ? ROAD_CONFIG[belongTo].activeIcon : ROAD_CONFIG[belongTo].icon;
}

/**
 * 获取病害CSS类名函数
 * @param {string} belongTo - 病害归属类型
 * @returns {string} 对应的CSS类名
 */
export const getBelongToClass = (belongTo) => {
  return BELONG_TO_TYPE_MAP[belongTo] || '';
}

/**
 * 根据belongTo获取对应的statisType
 * @param {string} belongTo - 病害归属类型
 * @returns {string} 对应的statisType值
 */
export const getStatisTypeByBelongTo = (belongTo) => {
  return BELONG_TO_STATIS_TYPE_MAP[belongTo] || belongTo;
}

/**
 * 根据statisType获取对应的belongTo
 * @param {string} statisType - 统计类型
 * @returns {string} 对应的belongTo值
 */
export const getBelongToByStatisType = (statisType) => {
  return STATIS_TYPE_MAP[statisType] || statisType;
}

/**
 * 获取病害归属类型对应的颜色
 * @param {string} belongTo - 病害归属类型
 * @param {string} defaultColor - 默认颜色，如果没有找到对应的颜色则返回此值
 * @returns {string} 对应的颜色值
 */
export const getBelongToColor = (belongTo, defaultColor = '#1990ff') => {
  if (!ROAD_CONFIG[belongTo]) {
    return defaultColor;
  }
  return ROAD_CONFIG[belongTo].color;
}

/**
 * 获取病害归属类型对应的标签
 * @param {string} belongTo - 病害归属类型
 * @param {string} defaultLabel - 默认标签，如果没有找到对应的标签则返回此值
 * @returns {string} 对应的标签值
 */
export const getBelongToLabel = (belongTo, defaultLabel = '未知类型') => {
  if (!ROAD_CONFIG[belongTo]) {
    return defaultLabel;
  }
  return ROAD_CONFIG[belongTo].label;
}

// =====================================================================
// 默认导出（为保持向后兼容）
// =====================================================================

/**
 * 路面类型与英文名称和数值的映射
 * 路基病害: 20, 桥梁病害: 40, 隧道病害: 50, 其他: 90
 */
export const ROAD_TYPE_MAPPING = {
  SUBGRADE: {    // 路基病害
    code: 20,
    name: 'subgrade_disease',
    label: '路基病害'
  },
  BRIDGE: {      // 桥梁病害
    code: 40,
    name: 'bridge_disease',
    label: '桥梁病害'
  },
  TUNNEL: {      // 隧道病害
    code: 50,
    name: 'tunnel_disease',
    label: '隧道病害'
  },
  OTHER: {       // 其他
    code: 90,
    name: 'other_disease',
    label: '其他'
  }
};

/**
 * 根据roadType值获取对应的英文名称和数值
 * @param {number|string} roadType - 路面类型值或名称
 * @returns {Object} 包含code、name和label的对象，如果未找到则返回null
 */
export const getRoadTypeInfo = (roadType) => {
  // 如果输入的是数字，则通过code查找
  if (typeof roadType === 'number') {
    for (const key in ROAD_TYPE_MAPPING) {
      if (ROAD_TYPE_MAPPING[key].code === roadType) {
        return ROAD_TYPE_MAPPING[key];
      }
    }
    return null;
  }
  
  // 如果输入的是字符串，则可能是key或name
  if (typeof roadType === 'string') {
    // 先检查是否是key
    if (ROAD_TYPE_MAPPING[roadType]) {
      return ROAD_TYPE_MAPPING[roadType];
    }
    
    // 再检查是否是name
    for (const key in ROAD_TYPE_MAPPING) {
      if (ROAD_TYPE_MAPPING[key].name === roadType) {
        return ROAD_TYPE_MAPPING[key];
      }
    }
  }
  
  return null;
};

export default {
  // 基础配置
  ROAD_CONFIG,
  
  // 导出常量
  BELONG_TO_TYPE_MAP,
  BELONG_TO_ICON_MAP,
  BELONG_TO_ACTIVE_ICON_MAP,
  STATIS_TYPE_MAP,
  BELONG_TO_STATIS_TYPE_MAP,
  BELONG_TO_COLOR_MAP,
  ROAD_TYPE_OPTIONS,
  ROAD_TYPE_MAPPING,
  
  // 工具函数
  getMarkerIcon,
  getBelongToClass,
  getStatisTypeByBelongTo,
  getBelongToByStatisType,
  getBelongToColor,
  getBelongToLabel,
  getRoadTypeInfo
}
