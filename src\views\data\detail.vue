/*
 * @Author: wangyj
 * @Date: 2022-10-25 17:59:57
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-03-09 13:22:29
 */
<template>
  <div class="map-container">
    <baidu-map
      :class="{'bm-view': true, 'bm-view_68': isUseClass}"
      :center="center"
      :zoom="zoom"
      :map-click="true"
      :scroll-wheel-zoom="scrollWhellZoom"
      :double-click-zoom="false"
      :auto-resize="true"
      @ready="handler"
      @zoomend="onZoomed"
      @click="handleMapClick"
      @rightclick="handleMapRightClick"
      @tilesloaded="handleMapTilesloaded"
    >
      <!-- 所有病害点 -->
      <bm-point-collection
        :points="diseasePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        color="#DC143C"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 所有绿点 -->
      <bm-point-collection
        :points="greenPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorA"
        size="8"
        @click="handlePointClick"
      />
      <!-- 所有蓝点 -->
      <bm-point-collection
        :points="bluePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorB"
        size="8"
        @click="handlePointClick"
      />
      <!-- 所有紫点 -->
      <bm-point-collection
        :points="purplePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorC"
        size="8"
        @click="handlePointClick"
      />
      <!-- 所有黄点 -->
      <bm-point-collection
        :points="yellowPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorD"
        size="8"
        @click="handlePointClick"
      />
      <!-- 所有红点 -->
      <bm-point-collection
        :points="redPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorE"
        size="8"
        @click="handlePointClick"
      />
      <!-- 小车 -->
      <bm-marker v-if="carPoint.lng && layerType === '路况'" ref="carMarker" :position="carPoint" :rotation="carRotation" :icon="{url:require('@/assets/car.png'),size:{width:60,height:28}}" :z-index="99999999999" />
      <!-- 终点marker -->
      <bm-marker v-if="endPoint.lng" ref="endMarker" :position="endPoint" :icon="{url:require('@/assets/line-end.png'),size:{width:64,height:64}, opts:{imageSize:{width:64,height:64},anchor: {width: 17, height:53}}}" />
      <!-- 起点marker -->
      <bm-marker v-if="startPoint.lng" ref="startMarker" :position="startPoint" :icon="{url:require('@/assets/line-start.png'),size:{width:64,height:64}, opts:{imageSize:{width:64,height:64}, anchor: {width: 17, height: 53}} }" />
      <!-- 无数条线段集合，每一节颜色都不一样 -->
      <!-- :icons="id % 5 === 0 ? item.icons : []"  // 箭头图标 -->
      <bm-polyline
        v-for="(item,id) in mapLineData"
        :id="item.id"
        :key="id+item.color"
        :path="item.line"
        :stroke-color="item.color"
        :stroke-opacity="1"
        :stroke-weight="7"
        :stroke-style="item.style"
        :clicking="true"
        @mouseover="handleMouseOverPolyline(id)"
        @mouseout="handleMouseOutPolyline(id)"
        @click="handlePointClick($event, id)"
      />
      <!-- 道路病害图 -->
      <canvas id="canvas" :width="canvasWidth" :height="canvasHeight" />
      <!-- @draw="draw" -->
      <bm-overlay v-show="diseaseWindow.show && diseaseWindow.urlRecheck !== ''" class="info-overlay" style="left: 0px; top: 0px;" pane="floatPane" @initialize="initialize">
        <div style="height: 100%; position: relative;">
          <img style="position: absolute; top: 0px; width: 10px; height: 10px; cursor: pointer; z-index: 10000; right: 6px;" src="https://api.map.baidu.com/images/iw_close1d3.gif" @click="handleInfoWindowClose">
          <div class="info-overlay-noprint">
            <img style="box-sizing:content-box;border:none;margin:0px;padding:0px;margin-left:-186px;margin-top:-691px;max-width:none; width:690px;height:786px;" src="https://api.map.baidu.com/images/iw3.png" @click="handleInfoWindowClose">
          </div>
          <div class="info-overlay-title">
            <span>{{ diseaseWindow.title }}</span>
            <span v-if="diseaseWindow.urlRecheck !== ''">人工复检图片</span>
          </div>

          <div
            :class="{
              'info-overlay-main-recheck' : diseaseWindow.urlRecheck !== '',
              'info-overlay-main' : diseaseWindow.urlRecheck === ''
            }"
          >
            <div class="info-overlay-img-box">
              <img
                v-if="!diseaseWindow.notUpload"
                :src="diseaseWindow.url"
                alt="鼠标滑轮滚动缩放图片"
                title="点击放大图片"
                style="object-fit:cover"
                @click="handleShowBigImg(1)"
              >
              <div v-if="diseaseWindow.notUpload" class="disease-img disease-img-error">{{ diseaseWindow.errorTxt }}</div>
            </div>
            <div v-if="diseaseWindow.urlRecheck !== ''" class="info-overlay-img-box">
              <img
                :src="diseaseWindow.urlRecheck"
                alt="鼠标滑轮滚动缩放图片"
                title="点击放大图片"
                style="object-fit:cover"
                @click="handleShowBigImg(2)"
              >
            </div>
          </div>

          <div class="info-overlay-footer">
            <div style="padding-right: 20px;">
              <div class="disease-deal-btns">
                <div style="position: relative;">
                  <div v-if="diseaseWindow.btnsShow" style="color: #606266; line-height: 32px;">选择正确的{{ diseaseWindow.btnsType === 1 ? '病害' : diseaseWindow.btnsType === 2 ? '道路资产' : '路面风险' }}类型：</div>
                </div>
                <div>
                  <el-button size="small" round @click="handleToPicture">定位图集</el-button>
                  <el-button size="small" round @click="handlePrev">上一张</el-button>
                  <el-button size="small" round @click="handleNext">下一张</el-button>
                </div>
              </div>
              <div v-if="diseaseWindow.btnsShow" class="disease-correction">
                <el-button
                  v-for="item in selectTypes"
                  :key="item.engName"
                  size="mini"
                  :type="item.engName === tagActive ? 'warning' : ''"
                  @click.stop="handleCorrect(item.engName)"
                >
                  {{ item.chineseName }}
                </el-button>
                <el-button size="mini" @click.stop="handleIgnore">
                  非{{ diseaseWindow.btnsType === 1 ? '病害' : diseaseWindow.btnsType === 2 ? '道路资产' : '路面风险' }}
                </el-button>
                <el-button
                  v-auth="'inspect-tasks-detail-check-push:view:inspect-tasks-detail-check-push'"
                  :loading="YONGFENG_info.loading"
                  style="margin-left: auto;width: 80px"
                  size="mini"
                  type="primary"
                  @click.stop="handlePushDamageToYongFeng"
                >
                  推送
                </el-button>
              </div>
            </div>

            <div v-if="diseaseWindow.urlRecheck !== ''">
              <div class="disease-deal-btns">
                <div style="color: #606266; line-height: 32px;">病害处置概要描述：</div>
              </div>
              <div class="disease-correction" style="color: #606266;">
                {{ diseaseWindow.description }}
              </div>
            </div>
          </div>
        </div>
      </bm-overlay>

      <!-- 点击核查时显示的病害图片弹窗 -->
      <bm-info-window
        ref="diseaseWindow"
        :position="diseaseWindow.position"
        :title="diseaseWindow.title"
        :show="diseaseWindow.show && diseaseWindow.urlRecheck === ''"
        :close-on-click="false"
        :auto-pan="true"
        @close="handleInfoWindowClose"
        @open="handleInfoWindowOpen"
        @clickclose="handleInfoWindowClickClose"
      >
        <div v-cloak v-if="diseaseWindow.show" class="disease-cont">
          <div class="disease-img-box">
            <!-- <viewer title="点击放大图片">
              <img
                v-if="!diseaseWindow.notUpload"
                :src="diseaseWindow.url"
                class="disease-img"
                alt="鼠标滑轮滚动缩放图片"
                style="{object-fit:cover}"
                width="700px"
                height="393px"
              >
            </viewer> -->
            <img
              v-if="!diseaseWindow.notUpload"
              :src="diseaseWindow.url"
              class="disease-img"
              alt="鼠标滑轮滚动缩放图片"
              title="点击放大图片"
              style="object-fit:cover"
              width="700px"
              height="393px"
              @click="handleShowBigImg(1)"
            >
            <div v-if="diseaseWindow.notUpload" class="disease-img disease-img-error">{{ diseaseWindow.errorTxt }}</div>
          </div>

          <div class="disease-deal-btns">
            <div style="position: relative;">
              <div v-if="diseaseWindow.btnsShow" style="color: #606266; line-height: 32px;">选择正确的{{ diseaseWindow.btnsType === 1 ? '病害' : diseaseWindow.btnsType === 2 ? '道路资产' : '路面风险' }}类型：</div>

              <!-- <el-button type="warning" size="small" round style="cursor: default;">纠错</el-button>
              <span class="triangle" /> -->
            </div>
            <div>
              <!-- <el-button type="success" size="small" round @click="handleRetain">保留</el-button>
              <el-button type="info" size="small" round @click="handleIgnore">忽略</el-button> -->
              <el-button size="small" round @click="handleToPicture">定位图集</el-button>
              <el-button size="small" round @click="handlePrev">上一张</el-button>
              <el-button size="small" round @click="handleNext">下一张</el-button>
            </div>
          </div>
          <div v-if="diseaseWindow.btnsShow" class="disease-correction">
            <el-button
              v-for="item in selectTypes"
              :key="item.engName"
              size="mini"
              :type="item.engName === tagActive ? 'warning' : ''"
              @click.stop="handleCorrect(item.engName)"
            >
              {{ item.chineseName }}
            </el-button>
            <el-button size="mini" @click.stop="handleIgnore">
              非{{ diseaseWindow.btnsType === 1 ? '病害' : diseaseWindow.btnsType === 2 ? '道路资产' : '路面风险' }}
            </el-button>
            <el-button
              v-auth="'inspect-tasks-detail-check-push:view:inspect-tasks-detail-check-push'"
              :loading="YONGFENG_info.loading"
              style="margin-left: auto;width: 80px"
              size="mini"
              type="primary"
              @click.stop="handlePushDamageToYongFeng"
            >
              推送
            </el-button>
          </div>
          <!-- <div class="disease-opn-btns">
            <el-button size="small" round @click="handlePrev">上一张</el-button>
            <el-button size="small" round @click="handleNext">下一张</el-button>
          </div> -->
        </div>
      </bm-info-window>

      <!-- # TODO: 20241103，点击道路轨迹时，后台依据该点的GPS去盒子上报的此次巡检的原始路面图中去查询最近的一张图返回，前端弹窗展示 -->
      <bm-info-window
        ref="oriRoadImageWindowRef"
        :position="oriRoadImageWindow.postition"
        :title="`${oriRoadImageWindow.title}${oriRoadImageWindow.roadName ? `（${oriRoadImageWindow.roadName}）` : ''}`"
        :show="oriRoadImageWindow.show"
        :close-on-click="false"
        :auto-pan="true"
        @clickclose="closeOriRoadImageWindow"
      >
        <div v-cloak v-if="oriRoadImageWindow.show" class="disease-cont">
          <div class="disease-img-box">
            <img
              :src="oriRoadImageWindow.urls[oriRoadImageWindow.urlIndex]"
              class="disease-img"
              alt="鼠标滑轮滚动缩放图片"
              title="点击放大图片"
              style="object-fit:cover;"
              width="700px"
              height="393px"
              @click="showOriRoadBigImage"
            >
          </div>
          <!-- <div class="disease-deal-btns">
            <div>
              <el-button size="small" round :disabled="oriRoadImageWindow.urlIndex === 0" @click="oriRoadImageWindow.urlInde -= 1">
                上一张
              </el-button>
              <el-button size="small" round :disabled="oriRoadImageWindow.urlIndex === oriRoadImageWindow.urls.length - 1" @click="oriRoadImageWindow.urlIndex += 1">下一张</el-button>
            </div>
          </div> -->
        </div>
      </bm-info-window>

      <!-- 线路剪切起点 -->
      <bm-marker v-if="cutPoints[0]" :dragging="true" :position="cutPoints[0]" :icon="{url: require('@/assets/start.png'), size:{width:60,height:53}, opts:{imageSize:{width:60,height:53}, anchor: {width: 30, height: 53}}}" @dragend="handleStartDragend" />
      <!-- 线路剪切终点 -->
      <bm-marker v-if="cutPoints[1]" :dragging="true" :position="cutPoints[1]" :icon="{url: require('@/assets/end.png'), size:{width:60,height:53}, opts:{imageSize:{width:60,height:53}, anchor: {width: 30, height: 53}}}" @dragend="handleEndDragend" />
    </baidu-map>
    <MapPanel
      ref="mapPanel"
      :mil-val.sync="milVal"
      :task-id.sync="taskId"
      :task-data.sync="taskData"
      :disease-window="diseaseWindow"
      @switchPanel="handleSwitchPanel"
      @deal="handleShowInfoWindow"
      @echartClick="handleEchartsClick"
      @milChange="handleMilChange"
      @updateCar="handleUpdataCar"
      @forbidWheelZoom="handleForbidWheelZoom"
      @layerType="handleLayerChange"
      @makeReport="onMakeReport"
      @closeWindow="handleInfoWindowClose"
    />
    <div class="legend-cont">
      <div class="legeng-btn" title="图例" @click="legendShow = !legendShow">?</div>
      <!-- <i class="el-icon-question" title="图例" @click="legendShow = !legendShow" /> -->
      <div v-show="legendShow" class="legend-img">
        <img :src="layerType === '绿视率' ? require('../../assets/GLR-sample.png') : require('../../assets/new-sample.png')" width="100%">
      </div>
    </div>
    <div class="line-cut">
      <el-button icon="el-icon-scissors" size="small" circle :type="isCutting ? 'primary': ''" @click="handleToggleCut" />
    </div>
    <div v-show="lineInfo.show" class="line-info-div">
      <div>开始时间：{{ lineInfo.startTime }}</div>
      <div>结束时间：{{ lineInfo.endTime }}</div>
      <div>起点位置：{{ lineInfo.startP }}</div>
      <div>终点位置：{{ lineInfo.endP }}</div>
      <div><span>起点桩号：{{ lineInfo.startPileNum }}</span><span>止点桩号：{{ lineInfo.endPileNum }}</span></div>
      <div>距起点距离：{{ lineInfo.distance }}m</div>
      <div>病害数量：{{ lineInfo.count }}</div>
      <div>起点设备启动时间：{{ lineInfo.startBootTime }}</div>
      <div>终点设备启动时间：{{ lineInfo.endBootTime }}</div>
    </div>

    <CutDialog ref="cutDialog" :task-data="taskData" :points="cutPoints" @cutSuccess="handleCutSuccess" />
  </div>
</template>
<script>
import {
  getRoadStatises,
  getRoadStatisesRoute,
  exportDoc,
  getGLRStatises,
  exportSummaryDoc,
  locatePictures,
  pushDamageToYongFeng,
  getOriginalRoadImages,
} from '@/api/data'
import axios from 'axios'
import { greyStyleJson, simpleStyleJson } from '@/utils/map-style'
import CanvasMixin from '@/mixin/canvas'
import html2Canvas from 'html2canvas'
import { exportFile, getFileExtension } from '@/utils/index'
import { mapState } from 'vuex'
import MapPanel from './components/map-panel.vue'
import 'viewerjs/dist/viewer.css'
import { api as viewerApi } from 'v-viewer'
import CutDialog from './components/cut-dialog.vue'

let pointClickDebounceTimer // 海量点的防抖

export default {
  name: 'DataDetail',
  components: { MapPanel, CutDialog },
  mixins: [CanvasMixin],
  data() {
    return {
      isRecheck: false,
      taskId: null,
      taskData: null,
      map: null,
      BMap: null,
      showBigImgType: 1,
      mapLoading: false,
      center: { lng: 0, lat: 0 }, // 地图中心
      zoom: 18, // 地图缩放级别
      scrollWhellZoom: true,
      legendShow: true,
      tagActive: null,
      canvas: null,
      ctx: null,
      YONGFENG_info: {
        id: 0,
        loading: false,
      },
      canvasWidth: 640,
      canvasHeight: 360,
      diseaseWindow: { // 病害信息窗体
        position: {},
        title: '',
        show: false,
        url: '',
        urlRecheck: '',
        description: '',
        notUpload: false,
        scale: 1,
        btnsShow: true,
        btnsType: 1, // 1病害，2道路资产
        errorTxt: '',
        photoTime: null,
      },
      isPointClick: false, // 数据加载完,才可以点击地图的线或是点
      roadData: [], // 道路数据
      mapLineData: [], // 地图所有折线集合
      greenPointData: [], // 绿点集合
      yellowPointData: [], // 黄点集合
      redPointData: [], // 红点集合
      bluePointData: [], // 蓝点集合
      purplePointData: [], // 紫点集合
      carPoint: {}, // 小车位置
      carRotation: 0, // 小车旋转角度
      isUseClass: true,
      milVal: 18,
      milArr: [{
        label: '50m',
        value: 18,
      },
      {
        label: '100m',
        value: 17,
      },
      {
        label: '200m',
        value: 16,
      },
      {
        label: '500m',
        value: 15,
      },
      {
        label: '1km',
        value: 14,
      }],
      layerType: '路况',
      diseasePointData: [],
      tempLayerData: {
        greenPointData: [],
        bluePointData: [],
        purplePointData: [],
        yellowPointData: [],
        redPointData: [],
        diseasePointData: [],
        mapLineData: [],
        GLR_level1PointData: [], // 图例上由上到下颜色
        GLR_level2PointData: [],
        GLR_level3PointData: [],
        GLR_level4PointData: [],
        GLR_level5PointData: [],
        GLR_mapLineData: [],
      },
      allPoints: [],
      simpleStyleJson,
      lineInfo: {
        show: false,
        startTime: null,
        endTime: null,
        startP: null,
        endP: null,
        startPileNum: null,
        endPileNum: null,
        distance: null,
        startBootTime: null,
        endBootTime: null,
        count: 0,
      },
      viewer: null,
      viewerFlag: false,
      viewerZoomDetail: null,
      viewerMoveDetail: null,
      isCutting: false, // 线路剪切中
      cutPoints: [], // 剪切点，只包含俩个点：起点和终点
      startPoint: {}, // 起点坐标
      endPoint: {}, // 终点坐标
      infOoverlayDom: null,
      // 点击道路轨迹，展示原始道路
      oriRoadImageWindow: { // 道路图片信息窗体
        show: false,
        title: '道路实景图',
        roadName: '',
        position: {},
        urls: [], // 原始道路图集
        urlIndex: 0, // 当前图下标
      },
    }
  },
  computed: {
    selectTypes() {
      switch (this.diseaseWindow.btnsType) {
      case 1:
        return this.allDiseaseType
      case 2:
        return this.allRoadAssetsType
      case 3:
        return this.allRoadForeignMatter
      default:
        return []
      }
    },
    ...mapState({
      taskDataObj: (state) => state.detail.taskDataObj,
      workUnit: (state) => state.account.workUnit,
    }),
  },
  async activated() {
    // console.log('缓存开始', this.taskDataObj)
    if (this.$route.params.taskId) {
      // eslint-disable-next-line radix
      this.taskId = parseInt(this.$route.params.taskId)
      // console.log(!this.taskDataObj[this.taskId]);
      if (!this.taskDataObj[this.taskId]) {
        await this.$store.dispatch('detail/setTaskData', this.taskId)
      }
      this.taskData = this.taskDataObj[this.taskId]
      const dailyInspect = this.taskData.inspectMileage * 1000 // 返回km，转成m

      if (dailyInspect < 3000) {
        this.milVal = 18
      } else if (dailyInspect >= 3000 && dailyInspect < 15000) {
        this.milVal = 17
      } else if (dailyInspect >= 15000 && dailyInspect < 50000) {
        this.milVal = 16
      } else if (dailyInspect >= 50000) {
        this.milVal = 15
      }
      // 城市道路病害（roadType = 1 且 roadLevel = 6）需特殊处理，其病害筛选条件为 road_type=6且classification=1
      await this.getModelIdentifyTypes(
        this.taskData.roadLevel === 6 ? 6
          : this.taskData.roadType === 10 ? 1 : this.taskData.roadType,
      )

      this.clearData()
      if (this.taskId === 603) {
        await this.getGLRStatisesAjax()
      }
      this.getRoadStatisesAjax()
    }
    // 监听键盘,
    this.keyDown()
  },
  deactivated() {
    // 取消键盘监听事件
    document.onkeydown = null
  },
  methods: {
    handler({ BMap, map }) {
      this.mapLoading = true
      this.map = map
      this.BMap = BMap
      // 初始化center
      this.center.lng = 116.404
      this.center.lat = 39.915
      // this.zoom = 6
      // console.log(this.taskData)

      if (this.taskData) {
        const { startPoint, midPoint, endPoint } = this.taskData

        if (startPoint && midPoint && endPoint) {
          this.center.lng = startPoint.lon
          this.center.lat = startPoint.lat

          const pointList = [
            {
              lng: startPoint.lon,
              lat: startPoint.lat,
            },
            {
              lng: midPoint.lon,
              lat: midPoint.lat,
            },
            {
              lng: endPoint.lon,
              lat: endPoint.lat,
            },
          ]
          map.setViewport(pointList, {
            margins: [20, 20, 20, 20],
          })
        }
      }
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
      const svgDom = map.getPanes().mapPane.getElementsByTagName('svg')[0] // 解决海量点覆盖(路线)polyline引发的鼠标事件丢失问题
      if (svgDom) svgDom.style.zIndex = 900
    },
    handleMapTilesloaded() {
      if (this.mapLoading) this.mapLoading = true
    },
    onZoomed() {
      if (this.map) this.zoom = this.map.getZoom()
    },
    handleInfoWindowOpen(e) {
      this.diseaseWindow.show = true
    },
    handleInfoWindowClose() {
      this.diseaseWindow.show = false
      // this.$refs.diseaseWindow.redraw()
    },
    handleInfoWindowClickClose() {
      this.diseaseWindow.show = false
      this.diseaseWindow.position = {}

      // this.$refs.mapPanel.removeTableRowIndex()
      this.handleAllowWheelZoom()
      // this.$refs.diseaseWindow.redraw()
    },
    initialize({ el, BMap, map }) {
      this.infOoverlayDom = el
    },
    // draw({ el, BMap, map }) {

    //   console.log('1111111111111111');

    //   console.log(el);
    //   const pixel = map.pointToOverlayPixel(new BMap.Point(116.404, 39.915))
    //   el.style.left = pixel.x - 60 + 'px'
    //   el.style.top = pixel.y - 20 + 'px'
    // },
    // 道路病害纠错
    async handleCorrect(diseaseType) {
      this.$refs.mapPanel.handleCorrect(diseaseType)
    },
    // 道路病害保留
    handleRetain() {
      this.$message({
        message: '保留成功',
        type: 'success',
      })
    },
    // 道路病害忽略
    async handleIgnore() {
      this.$refs.mapPanel.handleIgnore()
    },
    async handlePushDamageToYongFeng() {
      try {
        this.YONGFENG_info.loading = true
        const data = await pushDamageToYongFeng(this.YONGFENG_info.id)
        if (data.status === 200) {
          this.$message({
            message: '推送成功',
            type: 'success',
          })
          this.$refs.mapPanel.getDiseaseListAjax()
        } else {
          this.$message.error('推送失败')
        }
        this.YONGFENG_info.loading = false
      } catch (error) {
        this.$message.error('推送失败')
        this.YONGFENG_info.loading = false
      }
    },
    // 上一张病害图片
    async handlePrev() {
      this.$refs.mapPanel.handlePrev()
    },
    // 下一张病害图片
    async handleNext() {
      this.$refs.mapPanel.handleNext()
    },
    // 监听左右键实现上一张，下一张
    keyDown() {
      document.onkeydown = (e) => {
        if (!this.diseaseWindow.show) return

        // 事件对象兼容
        const e1 = e || window.event
        // 键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40
        if (e1 && e1.keyCode === 37) {
          // 按下左箭头
          this.handlePrev()
        } else if (e1 && e1.keyCode === 39) {
          // 按下右箭头
          this.handleNext()
        }
      }
    },

    // 定义一个函数，计算两个经纬度之间的距离（单位：米）
    getDistance(lat1, lon1, lat2, lon2) {
      // 将角度转换为弧度
      const radLat1 = lat1 * Math.PI / 180
      const radLon1 = lon1 * Math.PI / 180
      const radLat2 = lat2 * Math.PI / 180
      const radLon2 = lon2 * Math.PI / 180
      // 计算两点之间的夹角
      const angle = Math.acos(Math.sin(radLat1) * Math.sin(radLat2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.cos(radLon1 - radLon2))
      // 根据地球半径（6371千米）计算距离
      const distance = angle * 6371000
      return distance
    },

    // 找到数组对象中与给定经纬度最接近的元素的下标
    findClosestIndex(array, lat, lng) {
      if (!array.length) {
        return -1
      } if (array.length === 1) {
        return 0
      }
      // 记录当前最小的距离和对应的下标
      let minDistance = Infinity
      let minIndex = -1

      for (let i = 0; i < array.length; i++) {
        const elementLat = array[i].lat || array[i].gpsLatitude
        const elementLng = array[i].lng || array[i].gpsLongitude
        // 计算当前元素与给定经纬度之间的距离
        const distance = this.getDistance(lat, lng, elementLat, elementLng)
        if (distance < minDistance) {
          minDistance = distance
          minIndex = i
        }
      }
      // 返回找到的下标，如果没有找到则返回 -1
      return minIndex
    },

    // 所有点被点击
    handlePointClick({ point }, index) {
      clearTimeout(pointClickDebounceTimer)
      pointClickDebounceTimer = setTimeout(async () => {
        if (this.isCutting || this.layerType === '绿视率') return

        if (!this.isPointClick) {
          this.$message({
            message: '数据加载中,请稍等...',
            type: 'warning',
          })
          return
        }
        const { lat, lng } = point
        this.carPoint = point

        if (typeof index === 'object') { // 点击类型是 点位，不是路线
          index = point.id
        }

        const tempRotations = this.roadData[index]
        const tempPointImages = tempRotations.images || tempRotations.imageMerges
        console.log('tempPointImages', tempPointImages)

        // 点击道路轨迹，展示原始道路图
        this.getOriRoadImages({
          clickPoint: point,
          startTime: tempRotations.startTime,
          endTime: tempRotations.endTime,
        })

        if (tempPointImages) {
          const currentImgIndex = this.findClosestIndex(tempPointImages, lat, lng)
          const { gpsLatitude: dLat, gpsLongitude: dLng } = tempPointImages[currentImgIndex] // 拿出图片的位置
          const finalImgIndex = this.tempLayerData.diseasePointData.findIndex((point) => point.lat === dLat && point.lng === dLng)
          // this.$refs.mapPanel.handleUpdateVideo(finalImgIndex)
        } else {
          const finalImgIndex = this.findClosestIndex(this.tempLayerData.diseasePointData, lat, lng)
          // this.$refs.mapPanel.handleUpdateVideo(finalImgIndex, false)
        }

        const tarRotation = {
          lng: tempRotations.lngStart,
          lat: tempRotations.latStart,
        }
        const curRotation = {
          lng: tempRotations.lngEnd,
          lat: tempRotations.latEnd,
        }
        this.setRotation(curRotation, tarRotation)
      }, 200)
    },
    clearData() {
      const that = this
      // 清空数据, 防止绘制多遍
      that.roadData = []
      that.greenPointData = []
      that.yellowPointData = []
      that.redPointData = []
      that.purplePointData = []
      that.bluePointData = []
      that.mapLineData = []
      that.carPoint = {}
      that.diseasePointData = []
      that.tempLayerData = {
        greenPointData: [],
        bluePointData: [],
        purplePointData: [],
        yellowPointData: [],
        redPointData: [],
        diseasePointData: [],
        mapLineData: [],
        GLR_level1PointData: [], // 图例上由上到下颜色
        GLR_level2PointData: [],
        GLR_level3PointData: [],
        GLR_level4PointData: [],
        GLR_level5PointData: [],
        GLR_mapLineData: [],
      }
    },
    async getRoadStatisesAjax() {
      const that = this
      const params = {
        taskId: this.taskId,
        zoom: this.milVal,
      }
      axios.all([getRoadStatises(params), getRoadStatisesRoute(params)]).then(axios.spread((acct, perms) => {
        const acctPayload = acct.payload
        const permsPayload = perms.payload
        // 这里处理API A的结果
        that.$refs.mapPanel.initStatistic(acctPayload)

        if (permsPayload && permsPayload.length > 0) {
          for (let index = 0; index < acctPayload.length; index++) { // for循环速度比较快
            permsPayload[index].images = acctPayload[index].images || acctPayload[index].imageMerges
            permsPayload[index].damageTotalCount = acctPayload[index].damageTotalCount
            // 病害点数据
            const pointImages = permsPayload[index].images
            if (pointImages && pointImages.length > 0) {
              pointImages.forEach((img) => {
                that.tempLayerData.diseasePointData.push({
                  lng: img.gpsLongitude,
                  lat: img.gpsLatitude,
                })
              })
            }
          }
        }
        that.isPointClick = true

        that.roadData = permsPayload
        that.allPoints = []
        let tarRotation; let curRotarion
        for (let index = 0; index < permsPayload.length; index++) { // for循环速度比较快
          const point = permsPayload[index]

          let iconColor = this.GLOBAL_VARIATE.colorA
          if (index === 0) {
            // 起点位置
            that.startPoint.lng = point.lngStart
            that.startPoint.lat = point.latStart
          }

          if (index === 0 && that.taskData.hasEnd) {
            // 小车起始位置
            that.carPoint = {
              lng: point.lngStart,
              lat: point.latStart,
            }

            // 地图中心位置
            that.center = {
              lng: point.lngStart,
              lat: point.latStart,
            }

            tarRotation = { ...that.carPoint }
            curRotarion = {
              lat: permsPayload.length < 2 ? point.latEnd : permsPayload[index + 1].latStart,
              lng: permsPayload.length < 2 ? point.lngEnd : permsPayload[index + 1].lngStart,
            }

            setTimeout(() => {
              that.setRotation(curRotarion, tarRotation)
            })
          }

          if (index === permsPayload.length - 1 && !that.taskData.hasEnd) {
            // 小车起始位置
            that.carPoint = {
              lng: point.lngEnd,
              lat: point.latEnd,
            }

            // 地图中心位置
            that.center = {
              lng: point.lngEnd,
              lat: point.latEnd,
            }

            tarRotation = {
              lat: permsPayload.length < 2 ? point.latStart : permsPayload[index - 1].latStart,
              lng: permsPayload.length < 2 ? point.lngStart : permsPayload[index - 1].lngStart,
            }
            curRotarion = {
              lat: permsPayload.length < 2 ? point.latEnd : permsPayload[index].latStart,
              lng: permsPayload.length < 2 ? point.lngEnd : permsPayload[index].lngStart,
            }
            curRotarion = {
              lat: permsPayload[index].latStart,
              lng: permsPayload[index].lngStart,
            }
            tarRotation = {
              lat: permsPayload[index].latEnd,
              lng: permsPayload[index].lngEnd,
            }

            setTimeout(() => {
              that.setRotation(curRotarion, tarRotation)
            })
          }

          if (index === permsPayload.length - 1 && that.taskData.hasEnd) {
            // 终点位置
            that.endPoint.lng = point.lngEnd
            that.endPoint.lat = point.latEnd
          }
          if (point.pqi >= 90) {
            // 绿
            that.tempLayerData.greenPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
              id: index,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
              id: index + 1,
            })

            that.tempLayerData.mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
                id: index,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: index + 1,
              }],
              color: this.GLOBAL_VARIATE.colorA,
              style: point.rebooted ? 'dashed' : 'solid',
            })

            that.allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
            // iconColor = this.GLOBAL_VARIATE.colorA
          } else if (point.pqi >= 80 && point.pqi < 90) {
            // 蓝
            that.tempLayerData.bluePointData.push({
              lng: point.lngStart,
              lat: point.latStart,
              id: index,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
              id: index + 1,
            })

            that.tempLayerData.mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
                id: index,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: index + 1,
              }],
              color: this.GLOBAL_VARIATE.colorB,
              style: point.rebooted ? 'dashed' : 'solid',
            })

            that.allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
            // iconColor = this.GLOBAL_VARIATE.colorB
          } else if (point.pqi >= 70 && point.pqi < 80) {
            // 紫
            that.tempLayerData.purplePointData.push({
              lng: point.lngStart,
              lat: point.latStart,
              id: index,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
              id: index + 1,
            })

            that.tempLayerData.mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
                id: index,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: index + 1,
              }],
              color: this.GLOBAL_VARIATE.colorC,
              style: point.rebooted ? 'dashed' : 'solid',
            })

            that.allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
            // iconColor = this.GLOBAL_VARIATE.colorC
          } else if (point.pqi >= 60 && point.pqi < 70) {
            // 黄
            that.tempLayerData.yellowPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
              id: index,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
              id: index + 1,
            })

            that.tempLayerData.mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
                id: index,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: index + 1,
              }],
              color: this.GLOBAL_VARIATE.colorD,
              style: point.rebooted ? 'dashed' : 'solid',
            })

            that.allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
            iconColor = this.GLOBAL_VARIATE.colorD
          } else {
            // 红
            that.tempLayerData.redPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
              id: index,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
              id: index + 1,
            })

            that.tempLayerData.mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
                id: index,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: index + 1,
              }],
              color: this.GLOBAL_VARIATE.colorE,
              style: point.rebooted ? 'dashed' : 'solid',
            })

            that.allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
            // iconColor = this.GLOBAL_VARIATE.colorE
          }
        }
        that.showLayers()

        // 地图视野包含所有点
        that.$nextTick(() => {
          that.map.setViewport(that.allPoints, {
            margins: [20, 20, 20, 20],
            enableAnimation: false,
          })
          that.zoom = that.map.getZoom()
          that.mapLoading = false
        })
      }))
    },
    // 获取绿视率落图展示
    async getGLRStatisesAjax() {
      const that = this
      const params = {
        taskId: this.taskId,
        zoom: this.milVal,
      }
      const { status, payload } = await getGLRStatises(params)
      if (status === 200) {
        for (let index = 0; index < payload.length; index++) { // for循环速度比较快
          const point = payload[index]
          if (point.greenRate > 0.25) {
            that.tempLayerData.GLR_level5PointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            that.tempLayerData.GLR_mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBAL_VARIATE.colorA_GLR,
              style: point.rebooted ? 'dashed' : 'solid',
            })
          } else if (point.greenRate > 0.2 && point.greenRate <= 0.25) {
            that.tempLayerData.GLR_level4PointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            that.tempLayerData.GLR_mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBAL_VARIATE.colorB_GLR,
              style: point.rebooted ? 'dashed' : 'solid',
            })
          } else if (point.greenRate > 0.15 && point.greenRate <= 0.2) {
            that.tempLayerData.GLR_level3PointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            that.tempLayerData.GLR_mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBAL_VARIATE.colorC_GLR,
              style: point.rebooted ? 'dashed' : 'solid',
            })
          } else if (point.greenRate > 0.1 && point.greenRate <= 0.15) {
            that.tempLayerData.GLR_level2PointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            that.tempLayerData.GLR_mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBAL_VARIATE.colorD_GLR,
              style: point.rebooted ? 'dashed' : 'solid',
            })
          } else if (point.greenRate <= 0.1) {
            that.tempLayerData.GLR_level1PointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            that.tempLayerData.GLR_mapLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBAL_VARIATE.colorE_GLR,
              style: point.rebooted ? 'dashed' : 'solid',
            })
          }
        }
      }
    },
    showLayers() {
      const that = this
      // 清空数据, 防止绘制多遍
      that.greenPointData = []
      that.yellowPointData = []
      that.redPointData = []
      that.purplePointData = []
      that.bluePointData = []
      that.mapLineData = []
      that.diseasePointData = []

      // const arrowsArr = this.map.getOverlays().filter((item) => item.ca.className === 'BMap_Marker BMap_noprint')
      // const lineArr = this.map.getOverlays().filter((item) => item.aR === 'Polyline')
      // console.log(arrowsArr)
      // if (arrowsArr.length > 0) {
      //   arrowsArr.forEach((arrows) => {
      //     if (arrows.zIndex === 99999999999) return
      //     that.map.removeOverlay(arrows)
      //   })
      // }
      // if (lineArr.length > 0) {
      //   lineArr.forEach((line) => {
      //     that.map.removeOverlay(line)
      //   })
      // }

      if (that.layerType === '路况') {
        that.greenPointData = that.tempLayerData.greenPointData
        that.bluePointData = that.tempLayerData.bluePointData
        that.purplePointData = that.tempLayerData.purplePointData
        that.yellowPointData = that.tempLayerData.yellowPointData
        that.redPointData = that.tempLayerData.redPointData
        that.mapLineData = that.tempLayerData.mapLineData
      } else if (that.layerType === '病害') {
        that.diseasePointData = that.tempLayerData.diseasePointData
      } else if (that.layerType === '绿视率') {
        that.greenPointData = that.tempLayerData.GLR_level5PointData
        that.bluePointData = that.tempLayerData.GLR_level4PointData
        that.purplePointData = that.tempLayerData.GLR_level3PointData
        that.yellowPointData = that.tempLayerData.GLR_level2PointData
        that.redPointData = that.tempLayerData.GLR_level1PointData
        that.mapLineData = that.tempLayerData.GLR_mapLineData
      }
    },
    // 设置小车旋转角度
    setRotation(curPosP, targetPosP) {
      // 计算两点之间的距离
      const distance = this.getDistance(curPosP.lat, curPosP.lng, targetPosP.lat, targetPosP.lng)
      console.log('距离', distance)

      // 设置阈值，距离小于该阈值时不计算角度
      const distanceThreshold = 2 // 单位：米
      if (distance < distanceThreshold) {
        // 距离太小，不改变角度
        return
      }

      // 直接使用经纬度计算方位角，避免像素转换带来的问题（在任何缩放级别下都能保持准确性）
      // 注意：经度是x，纬度是y
      const dLon = (targetPosP.lng - curPosP.lng) * Math.PI / 180
      const y = Math.sin(dLon) * Math.cos(targetPosP.lat * Math.PI / 180)
      const x = Math.cos(curPosP.lat * Math.PI / 180) * Math.sin(targetPosP.lat * Math.PI / 180)
                - Math.sin(curPosP.lat * Math.PI / 180) * Math.cos(targetPosP.lat * Math.PI / 180)
                * Math.cos(dLon)

      // 计算方位角（以正北为0度，顺时针增加）
      let brng = Math.atan2(y, x) * 180 / Math.PI

      // 确保角度在0-360度范围内
      brng = (brng + 360) % 360

      // 添加90度的偏移量，因为车辆图标可能默认指向不同方向
      // 根据实际测试调整此偏移值
      const offset = 270
      this.carRotation = (brng + offset) % 360
      // console.log('旋转角度', this.carRotation)
    },
    // 组件 $emit
    handleSwitchPanel({ isFold }) {
      // isFold true：折叠，false：展开
      const that = this
      this.isUseClass = isFold
      const tmpCarPoint = { ...this.carPoint }
      this.carPoint = {}
      setTimeout(() => {
        that.carPoint = tmpCarPoint
      }, 100)
    },
    handleShowInfoWindow({
      row, index, currentPage, tableRowIndex, tableType, modelIdentifyType,
    }) {
      this.diseaseWindow.urlRecheck = row?.damageTreat?.pictureInfos.length ? row.damageTreat.pictureInfos[0].url : ''
      this.diseaseWindow.description = row?.damageTreat?.description ? row.damageTreat.description : ''
      const that = this
      // this.diseaseWindow.show = false
      this.diseaseWindow.btnsType = modelIdentifyType
      this.diseaseWindow.photoTime = row.photoTime
      this.tagActive = row.type
      if (row?.id) this.YONGFENG_info.id = row.id

      // picState  1正常  2归档  3不存在
      if (row.picState === 1) {
        const image = new Image()
        // image.src = require('../../assets/images/default.jpg')
        image.src = row.pictureUrl

        image.setAttribute('crossOrigin', 'anonymous')
        // image.crossOrigin = 'Anonymous'
        image.onload = () => {
          that.diseaseWindow.show = true
          that.getCtx(image, 'canvas', image.width, image.height)
          if (!that.canvas || !that.ctx) return

          // 图片标注病害处理
          that.ctx.drawImage(image, 0, 0, image.width, image.height)

          // qp需求
          if (tableType === '1') {
            that.ctxDraw(row.type, row.coordinate, row.accuracy)
            that.diseaseWindow.btnsShow = true
          } else {
            row.damages.forEach((damage) => {
              that.ctxDraw(damage.type, damage.coordinate)
            })
            that.diseaseWindow.btnsShow = false
          }

          const imgSrc = that.canvas.toDataURL()
          console.log(modelIdentifyType)

          // 使用对象映射替代长链式三元表达式
          const typeNameMap = {
            1: '病害一览',
            2: '道路资产',
            3: '路面风险',
            5: '沿线设施损坏',
            9: '慢行病害',
            10: '城市管理问题',
          }
          const typeName = typeNameMap[modelIdentifyType] || '病害一览'
          that.diseaseWindow.title = `${typeName}图片（第${currentPage}页第${tableRowIndex + 1}张）`

          that.diseaseWindow.url = imgSrc
          that.diseaseWindow.notUpload = false
          that.diseaseWindow.position = {
            lng: row.gpsLongitude,
            lat: row.gpsLatitude,
          }

          this.diseaseWindow.show = true

          if (this.diseaseWindow.urlRecheck !== '') {
            const point = new this.BMap.Point(row.gpsLongitude, row.gpsLatitude)
            this.$nextTick(() => {
              console.log(that.infOoverlayDom)
              that.setInfoWindowViewport(that.map, point, that.infOoverlayDom)
            })
          }

          if (this.viewerFlag) {
            this.initViewer(1)
          }
          // console.log(that.$refs.diseaseWindow.getPosition());
        }
        // image.onerror = () => {
        //   that.diseaseWindow.postition = {
        //     lng: row.gpsLongitude,
        //     lat: row.gpsLatitude,
        //   }
        //   that.diseaseWindow.title = `病害图片（第${currentPage}页第${tableRowIndex + 1}张）`
        //   that.diseaseWindow.notUpload = true
        //   that.diseaseWindow.show = true
        // }
      } else {
        that.diseaseWindow.postition = {
          lng: row.gpsLongitude,
          lat: row.gpsLatitude,
        }
        that.diseaseWindow.title = `${modelIdentifyType === 1 ? '病害' : '道路资产'}图片（第${currentPage}页第${tableRowIndex + 1}张）`
        that.diseaseWindow.notUpload = true
        that.diseaseWindow.errorTxt = row.picState === 3 ? `您查看的图片时间点是：${row.photoTime.replace('T', ' ')}，当前图片续传到达时间：${row.deviceState.schedulerFrameFileTimestamp ? row.deviceState.schedulerFrameFileTimestamp.replace('T', ' ') : '未知'}，还有${row.deviceState.frameFileFailureNums || '未知'}张图片尚未续传成功，请等待或联系管理员进一步查看。` : '图片已归档，请联系管理员解冻'
        that.diseaseWindow.show = true

        if (this.viewerFlag) {
          this.viewer.hide()
        }
      }

      this.handleForbidWheelZoom()
    },
    handleEchartsClick(index) {
      index = parseInt(index)
      const tmpLng = this.mapLineData[index].line[0].lng
      const tmpLat = this.mapLineData[index].line[0].lat

      this.carPoint = {
        lat: tmpLat,
        lng: tmpLng,
      }
      if (this.layerType === '路况') {
        this.center = {
          lat: tmpLat,
          lng: tmpLng,
        }
      }

      const curRotation = {
        lng: this.mapLineData[index].line[1].lng,
        lat: this.mapLineData[index].line[1].lat,
      }
      const tarRotation = {
        lng: this.mapLineData[index].line[0].lng,
        lat: this.mapLineData[index].line[0].lat,
      }
      this.setRotation(curRotation, tarRotation)
    },
    async handleMilChange(val) {
      this.milVal = val
      this.clearData()
      if (this.taskId === 603) {
        await this.getGLRStatisesAjax()
      }
      this.getRoadStatisesAjax()
    },
    // 根据视频，更新小车位置
    handleUpdataCar(data) {
      // console.log('根据视频，更新小车位置', data)
      if (data === undefined || data === null) return

      // console.log('tempData', tempData)
      // // 小车位置
      this.carPoint = {
        lng: data.lngStart,
        lat: data.latStart,
      }

      // // 地图中心位置
      if (this.layerType === '路况') {
        this.center = {
          lng: data.lngStart,
          lat: data.latStart,
        }
      }

      const tarRotation = { ...this.carPoint }
      const curRotarion = {
        lng: data.lngEnd,
        lat: data.latEnd,
      }

      this.setRotation(curRotarion, tarRotation)
    },
    // 信息框打开时禁止地图滚动
    handleForbidWheelZoom() {
      this.scrollWhellZoom = false
    },
    // 信息框关闭时允许地图滚动
    handleAllowWheelZoom() {
      this.scrollWhellZoom = true
    },
    handleLayerChange(type) {
      this.layerType = type
      // this.map.clearOverlays()
      this.showLayers()
    },
    async svg2Canvas() {
      const svgTags = document.querySelector('.bm-view').getElementsByTagName('svg')
      if (svgTags.length > 0) {
        for (let i = svgTags.length - 1; i >= 0; i -= 1) {
          const svgTag = svgTags[i]
          const svgw = svgTag.scrollWidth
          const svgh = svgTag.scrollHeight

          const { parentNode } = svgTag

          const svgHTML = new XMLSerializer().serializeToString(svgTag)

          const svg = new Blob([svgHTML], { type: 'image/svg+xml;charset=utf-8,' })
          // eslint-disable-next-line no-restricted-globals
          const DOMURL = self.URL || self.webkitURL || self
          const url = DOMURL.createObjectURL(svg)

          // eslint-disable-next-line no-await-in-loop
          await this.loadImg(url).then((img) => {
            const canvas = document.createElement('canvas')
            canvas.classList.add('mapCanvas')
            canvas.setAttribute('width', svgw)
            canvas.setAttribute('height', svgh)
            const ctx = canvas.getContext('2d')
            ctx.drawImage(img, 0, 0, svgw, svgh)
            // parentNode.removeChild(svgTag)
            svgTag.style.display = 'none'
            parentNode.appendChild(canvas)
          })
        }
      }
    },
    html2CanvasFun(targetDom) {
      return new Promise((resolve, rejected) => {
        html2Canvas(targetDom, {
          width: targetDom.offsetWidth,
          height: targetDom.offsetHeight,
          scale: 1,
          useCORS: true,
        }).then((canvas) => {
          resolve(canvas)
        })
      })
    },
    async onMakeReport(arg) {
      const that = this

      if (that.allPoints.length > 0) {
        that.map.setViewport(that.allPoints, {
          margins: [20, 20, 20, 20],
        })
        that.zoom = that.map.getZoom()
      }

      that.$nextTick(async () => {
        await this.svg2Canvas()

        const p1 = this.html2CanvasFun(document.querySelector('.bm-view'))
        const p2 = this.html2CanvasFun(document.querySelector('#PCI-echart'))
        const p3 = this.html2CanvasFun(document.querySelector('#RQI-echart'))
        const p4 = this.html2CanvasFun(document.querySelector('#PQI-echart'))
        Promise.all([p1, p2, p3, p4]).then((result) => {
          const routeImg = result[0].toDataURL('image/jpeg', 1.0)
          const pciImg = result[1].toDataURL('image/jpeg', 1.0)
          const rqiImg = result[2].toDataURL('image/jpeg', 1.0)
          const pqiImg = result[3].toDataURL('image/jpeg', 1.0)

          const postData = {
            taskId: this.taskId,
            routeImg,
            pciImg,
            rqiImg,
            pqiImg,
          }
          let exportFun = null
          if (arg.docType === 1) {
            // 检测报告
            exportFun = exportDoc
          } else if (arg.docType === 2) {
            // 汇总报告
            exportFun = exportSummaryDoc
          }
          exportFun(postData).then((res) => {
            const url = URL.createObjectURL(res.payload)
            const fileFormat = getFileExtension(res.headers['content-disposition'], 'docx')
            const { fileName } = arg
            setTimeout(() => {
              exportFile(url, `${fileName}.${fileFormat}`)
              // 确保浏览器有时间开始下载后再释放
              setTimeout(() => URL.revokeObjectURL(url), 100)
            }, 500)
          })
          const mapCanvas = document.querySelector('.bm-view').getElementsByClassName('mapCanvas')
          if (mapCanvas.length > 0) {
            for (let i = mapCanvas.length - 1; i >= 0; i -= 1) {
              mapCanvas[i].parentNode.querySelector('svg').style.display = 'block'
              mapCanvas[i].parentNode.removeChild(mapCanvas[i])
            }
          }
        })
      })
    },
    handleMouseOverPolyline(index) {
      if (this.layerType === '绿视率') {
        return
      }
      if (this.diseaseWindow.show) {
        // 处理window打开时，鼠标经过路线时不显示路线信息
        return
      }
      const data = this.roadData[index]

      if (!data) return
      this.lineInfo.startTime = data.startTime.replace('T', ' ')
      this.lineInfo.endTime = data.endTime.replace('T', ' ')
      this.lineInfo.startP = `(${data.lngStart}, ${data.latStart})`
      this.lineInfo.endP = `(${data.lngEnd}, ${data.latEnd})`
      this.lineInfo.startPileNum = data.startPileNum
      this.lineInfo.endPileNum = data.endPileNum
      this.lineInfo.distance = data.distance
      this.lineInfo.count = data.damageTotalCount || 0
      this.lineInfo.startBootTime = data.startBootTime ? data.startBootTime.replace('T', ' ') : ''
      this.lineInfo.endBootTime = data.endBootTime ? data.endBootTime.replace('T', ' ') : ''
      this.lineInfo.show = true
    },
    handleMouseOutPolyline(index) {
      this.lineInfo.show = false
      this.lineInfo.startTime = null
      this.lineInfo.endTime = null
      this.lineInfo.startP = null
      this.lineInfo.endP = null
      this.lineInfo.startPileNum = null
      this.lineInfo.endPileNum = null
      this.lineInfo.distance = null
      this.lineInfo.count = 0
      this.lineInfo.startBootTime = null
      this.lineInfo.endBootTime = null
    },
    // 查看大图
    handleShowBigImg(type) {
      this.viewerFlag = true
      this.showBigImgType = type
      this.initViewer(type)
    },
    initViewer(type) {
      const that = this
      const images = [type === 1 ? this.diseaseWindow.url : this.diseaseWindow.urlRecheck]
      if (that.viewer) {
        that.viewer.destroy()
      }
      that.viewer = viewerApi({
        images,
        options: {
          // initialViewIndex: 1,
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: false, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: false, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: false, // 使用 CSS3 过度
          fullscreen: true, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
          moved(e) {
            console.log('moved')
            that.viewerMoveDetail = e.detail
          },
          zoomed(e) {
            console.log('zoomed')
            if (e.detail.originalEvent && e.detail.originalEvent.x) {
              that.viewerZoomDetail = {
                ratio: e.detail.ratio,
              }
            }
          },
          hidden(e) {
            console.log('hidden')
            that.viewer = null
            that.viewerFlag = false
            that.viewerMoveDetail = null
            that.viewerZoomDetail = null
          },
          viewed(e) {
            console.log('viewed')
            if (that.viewerZoomDetail) {
              // console.log(that.viewerZoomDetail)
              that.viewer.zoomTo(that.viewerZoomDetail.ratio)
            }
            if (that.viewerMoveDetail) {
              that.viewer.moveTo(that.viewerMoveDetail.x, that.viewerMoveDetail.y)
            }
          },
        },
      })
    },
    async handleToPicture() {
      const {
        id, deviceKey, roadType, startTime, endTime,
      } = this.taskData

      const { photoTime } = this.diseaseWindow
      const query = {
        taskId: id,
        photoTime,
        pageSize: 15, // 图集页面每页展示数据数
      }
      const { payload } = await locatePictures(query)

      sessionStorage.setItem('picLocateInfo', JSON.stringify(payload))
      this.$router.push({
        path: '/atlas/pictures',
        query: {
          id,
          key: deviceKey,
          roadType,
          startTime,
          endTime,
        },
      })
    },
    // 线路剪切
    handleToggleCut() {
      this.isCutting = !this.isCutting
      const message = this.isCutting ? '进入检测任务裁剪状态,鼠标左键单击标定起止点，右键单击提交数据，Esc取消标定点' : '退出检测任务裁剪状态'
      this.$message({
        message,
        duration: 5000,
      })
      if (this.isCutting) {
        this.handleWatchEsc()
      } else {
        this.handleUnWatchEsc()
        this.cutPoints = []
      }
    },
    handleMapClick({ point }) {
      if (!this.isCutting) return
      if (this.cutPoints.length > 2) return

      this.cutPoints.push(point)
    },
    handleStartDragend({ point }) {
      this.cutPoints[0] = point
    },
    handleEndDragend({ point }) {
      this.cutPoints[1] = point
    },
    handleWatchEsc() {
      const that = this
      document.addEventListener('keydown', (event) => {
        if (event.keyCode === 27) {
          if (that.cutPoints.length > 0 && !this.$refs.cutDialog.dialogFormVisible) {
            that.cutPoints = []
          }
        }
      })
    },
    handleUnWatchEsc() {
      document.removeEventListener('keydown', () => {})
    },
    handleMapRightClick() {
      if (this.cutPoints.length === 2) {
        this.$refs.cutDialog.show()
      }
    },
    handleCutSuccess() {
      this.cutPoints = []
      this.$refs.mapPanel.getInspectionListAjax()
      // this.handleToggleCut(false)
    },

    setInfoWindowViewport(map, point, el) {
      const pixel = map.pointToOverlayPixel(point)

      const leftOffset = 15
      const topOffset = 25
      const margin = [10, 10, 10, 10]

      const widthNum = el.clientWidth
      const heightNum = el.clientHeight

      console.log('widthNum', widthNum)

      el.style.left = `${(pixel.x + leftOffset) - (widthNum / 2)}px`
      el.style.top = `${pixel.y - topOffset - heightNum}px`
      setTimeout(() => {
        const mapContainer = map.getContainer()
        const mapWidth = mapContainer.clientWidth
        const mapHeight = mapContainer.clientHeight
        console.log('mapWidth', mapWidth)
        // 转换像素坐标
        const { x, y } = map.pointToPixel(point)

        // 定义范围，如果点的坐标超出这个范围，就需要偏移地图
        const left = widthNum / 2
        const right = mapWidth - left - leftOffset
        const top = heightNum + topOffset
        const bottom = mapHeight + topOffset

        // 如果 x 和 y 都在范围内，直接返回
        if (left <= x && x <= right && top <= y && y <= bottom) return

        // 如果 点不在地图的可视区域内，将地图的中心设置为该点
        if (!(map.getBounds().containsPoint(point))) {
          console.log('setInfoWindowViewport')
          map.setCenter(point)
          this.setInfoWindowViewport(map, point, el)
          return
        }

        let offsetX = 0
        let offsetY = 0

        // 如果 x 小于左边界，偏移量为左边界减去 x
        if (x < left) offsetX = left - x - margin[3]
        // // 如果 x 大于右边界，偏移量为右边界减去 x 的负数
        else if (x > right) offsetX = -(x - right) - margin[1]

        // 如果 y 小于上边界，偏移量为上边界减去 y
        if (y < top) offsetY = top - y + margin[0]
        // // 如果 y 大于下边界，偏移量为下边界减去 y 的负数
        else if (y > bottom) offsetY = -(y - bottom) + margin[2]

        console.log('111111111111111s', offsetX, offsetY)
        map.panBy(offsetX, offsetY) // https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html
      }, 250)
    },
    // # 点击道路轨迹，展示原始道路图弹窗
    async getOriRoadImages({ clickPoint, startTime, endTime }) {
      this.closeOriRoadImageWindow()
      const params = {
        taskId: this.taskId,
        latitude: clickPoint.lat,
        longitude: clickPoint.lng,
        startTime,
        endTime,
      }
      const { payload } = await getOriginalRoadImages(params)
      if (payload && payload[0]) {
        const { objectStorageUrlPrefix, originalImagePath, roadName } = payload[0]
        this.oriRoadImageWindow.urls = [
          objectStorageUrlPrefix + originalImagePath,
        ]
        this.oriRoadImageWindow.postition = {
          lng: clickPoint.lng,
          lat: clickPoint.lat,
        }
        this.oriRoadImageWindow.roadName = roadName
        this.showOriRoadImageWindow()
      }
    },
    showOriRoadImageWindow() {
      this.oriRoadImageWindow.show = true
      this.handleForbidWheelZoom()
    },
    closeOriRoadImageWindow() {
      this.oriRoadImageWindow.show = false
      this.oriRoadImageWindow.postition = {}
      this.oriRoadImageWindow.urls = []
      this.oriRoadImageWindow.urlIndex = 0
      this.oriRoadImageWindow.roadName = ''
      this.handleAllowWheelZoom()
    },
    showOriRoadBigImage() {
      const that = this
      that.viewer = viewerApi({
        images: this.oriRoadImageWindow.urls,
        options: {
          // initialViewIndex: 1,
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: false, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: false, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: false, // 使用 CSS3 过度
          fullscreen: true, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 1,
        },
      })
    },
  },
}
</script>

<style lang="scss">
// 隐藏信息窗体的shadow
.BMap_shadow[type = 'infowindow_shadow'] {
  display: none !important;
}
</style>
<style lang="scss" scode>
  .map-container {
    width: 100%;
    height: calc(100vh - 94px);
    position: relative;
    .bm-view {
      width: 100%;
      height: 100%;
      &.bm-view_68 {
        width: 68%;
        max-width: calc(100% - 512px);
      }

      .info-overlay {
        // box-sizing: content-box;
        position: absolute;
        height: auto;
        padding: 15px;
        background-color: #fff;
        box-sizing: border-box;
        border: 1px solid #ababab;
        .info-overlay-noprint {
          box-sizing: content-box;
          overflow: hidden;
          z-index: 0;
          position: absolute;
          left: 50%;
          bottom: -16px;
          transform: translate(-50%, 50%);
          width: 34px;
          height: 50px;
        }
        .info-overlay-title {
          display: flex;
          margin-bottom: 15px;
          span {
            flex: 1;
          }
          span:last-of-type {
            padding-left: 10px;
          }
        }

        .info-overlay-footer{
          display: flex;
          & > div {
            flex: 1;
          }
        }

        .disease-img-error {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #9b9b9b;
          border-radius: 8px;
          color: #9b9b9b;
          padding: 0 10px;
          line-height: 1.2;
          @media screen and (max-width: 1280px) {
            font-size: 14px;
          }
        }
        .disease-deal-btns{
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .triangle {
            border: 6px solid transparent;
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            border-top-color: #e6a23c;
          }
        }
        .disease-correction {
          font-family: 'Microsoft Yahei',tahoma,arial,'Hiragin Sans GB';
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          word-wrap: break-word;
          word-break: break-all;
          margin-top: 10px;
          .el-tag {
            margin-right: 5px;
            margin-bottom: 5px;
            cursor: pointer;
          }

          .el-button {
            margin-bottom: 10px;
            margin-left: 0;
            margin-right: 10px;
            &:hover {
              color: #e6a23c;
              background: #fdf6ec;
              border-color: #f5dab1;
            }
            &:last-child:hover {
              background: #66b1ff;
              border-color: #66b1ff;
              color: #fff;
            }
          }
          .el-button--warning {
            &:hover {
              color: #FFFFFF;
              background-color: #f90;
              border-color: #f90;
            }
          }
        }
        .disease-opn-btns {
          border-top: 1px dotted #dedede;
          padding-top: 5px;
          margin-top: 5px;
          text-align: right;
        }

        .disease-deal-btns{
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .triangle {
            border: 6px solid transparent;
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            border-top-color: #e6a23c;
          }
        }
        img {
          width: 100%;
          height: 100%;
        }
        .info-overlay-main {
          display: flex;
          .info-overlay-img-box {
            width: 100%;
            border-radius: 6px;
            overflow: hidden;
            .disease-img {
              width: 100%;
              height: 100%;
            }
            .disease-img-error {
              width: 100%;
              height: 100%;
              box-sizing: border-box;
              display: flex;
              justify-content: center;
              align-items: center;
              border: 1px solid #9b9b9b;
              border-radius: 8px;
              color: #9b9b9b;
              padding: 0 10px;
              line-height: 1.2;
              @media screen and (max-width: 1280px) {
                font-size: 14px;
              }
            }
          }
          @media screen and (max-width: 1440px) {
            width: 500px;
            height: 281px
          }
          @media screen and (max-width: 1280px) {
            width: 380px;
            height: 214px
          }
          @media screen and (min-width: 1440px) {
            width: 700px;
            height: 383px
          }
        }
        .info-overlay-main-recheck {
          display: flex;
          justify-content: space-between;
          .info-overlay-img-box {
            width: 49.5%;
            height: 100%;
            border-radius: 6px;
            overflow: hidden;
          }

          @media screen and (max-width: 1280px) {
            width: 570px;
            height: 159px
          }
          @media screen and (max-width: 1440px) {
            width: 740px;
            height: 208px
          }
          @media screen and (min-width: 1440px) {
            width: 1110px;
            height: 307px
          }
        }
      }
      // bm-info-window样式
      .disease-cont {
        width: auto;
        padding-top: 10px;
        .disease-img-box {
          border-radius: 6px;
          overflow: hidden;
          @media screen and (max-width: 1440px) {
            width: 500px;
            height: 281px
          }
          @media screen and (max-width: 1280px) {
            width: 380px;
            height: 214px
          }
          @media screen and (min-width: 1440px) {
            width: 700px;
            height: 393px
          }
        }
        .disease-img {
          width: 100%;
          height: 100%;
          cursor: pointer;
        }
        .disease-img-error {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #9b9b9b;
          border-radius: 8px;
          color: #9b9b9b;
          padding: 0 10px;
          line-height: 1.2;
          @media screen and (max-width: 1280px) {
            font-size: 14px;
          }
        }
        .disease-deal-btns{
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .triangle {
            border: 6px solid transparent;
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            border-top-color: #e6a23c;
          }
        }
        .disease-correction {
          display: flex;
          flex-wrap: wrap;
          @media screen and (max-width: 1440px) {
            width: 500px;
          }
          @media screen and (max-width: 1280px) {
            width: 380px;
          }
          @media screen and (min-width: 1440px) {
            width: 700px;
          }
          word-wrap: break-word;
          word-break: break-all;
          margin-top: 10px;
          .el-tag {
            margin-right: 5px;
            margin-bottom: 5px;
            cursor: pointer;
          }

          .el-button {
            margin-bottom: 10px;
            margin-left: 0;
            margin-right: 10px;
            &:hover {
              color: #e6a23c;
              background: #fdf6ec;
              border-color: #f5dab1;
            }
            &:last-child:hover {
              background: #66b1ff;
              border-color: #66b1ff;
              color: #fff;
            }
          }
          .el-button--warning {
            &:hover {
              color: #FFFFFF;
              background-color: #f90;
              border-color: #f90;
            }
          }
        }
        .disease-opn-btns {
          border-top: 1px dotted #dedede;
          padding-top: 5px;
          margin-top: 5px;
          text-align: right;
        }
      }
    }
    // 图例
    .legend-cont {
      position: absolute;
      left: 10px;
      bottom: 10px;
      .legeng-btn {
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 50%;
        color: #fff;
        background: #2d8cf0;
        font-size: 14px;
        cursor: pointer;
      }
      .el-icon-question {
        color:#2d8cf0;
        font-size: 20px;
        cursor: pointer;
      }
      .legend-img {
        width: 200px;
        position: absolute;
        left: 0;
        bottom: 35px
      }
    }
    .line-info-div {
      position: absolute;
      left: 0;
      top: 0;
      padding: 10px;
      background: rgba(255,255,255, 0.9);
      border-radius: 6px;
      div {
        padding: 5px 0;
        font-size: 12px;
        color: #303133;
        display: flex;
        justify-content: space-between;
        span {
          display: inline-block;
          width: 50%;
          flex-shrink: 0;
        }
      }
    }
    // 裁剪
    .line-cut {
      position: absolute;
      bottom: 10px;
      left: 50px;
      z-index: 2;
      .el-button {
        &.el-button--primary:focus {
          background: #2d8cf0;
          border-color: #2d8cf0;
          color: #FFFFFF;
        }
        &:focus {
          background: #FFFFFF;
          border: 1px solid #DCDFE6;
          color: #606266;
        }
      }
    }
  }
</style>
