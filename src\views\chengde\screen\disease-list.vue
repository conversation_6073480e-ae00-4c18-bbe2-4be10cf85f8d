<template>
  <div class="disease-list">
    <div class="disease-list-filter">
      <div class="disease-list-filter-row mb10">
        <div class="disease-list-filter-item">
          <div class="disease-list-filter-item-content">
            <el-select
              v-model="timeRange"
              :popper-append-to-body="false"
              placeholder="请选择日期范围"
              clearable
              @change="handletimeRangeChange"
            >
              <el-option v-for="item in TimeRangeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>

        <div class="disease-list-filter-item">
          <div class="disease-list-filter-item-content">
            <RoadTypeCascader
              @change="handleRoadTypeCascaderChange"
              :showAllLevels="false"
              placeholder="病害归属"
            />
          </div>
        </div>
        <div class="disease-list-filter-item">
          <div class="disease-list-filter-item-content">
            <DiseaseTypeSelect placeholder="病害类型" 
            :showAllLevels="false"
            @change="handleDiseaseTypeSelection" />
          </div>
        </div>
      </div>
    </div>

    <div 
      class="disease-list-scroll"
      v-infinite-scroll="getQueryRecentOneDayDamagesFun"
      infinite-scroll-disabled="disabled"
      infinite-scroll-immediate="false"
      :infinite-scroll-distance="50"
    >
      <div
        v-for="disease in diseaseData.content"
        :key="disease.id"
        :class="{ 
            'disease-list-table-item': true, 
            'bounce-animation': isNewDisease(disease.id)
          }"
        @click="handleClickImage(disease)"
      >
        <div class="disease-list-table-item-bg" :class="{ 'active': isItemActive(disease) }">
          <div class="item-image-container">
            <span v-if="disease.belongToStr" class="item-tag" :style="{ backgroundColor: getBelongToColor(disease.belongTo) }">{{ disease.belongToStr.slice(0, 2) }}</span>
            <div v-if="isNewDisease(disease.id)" class="new-indicator"></div>
            <el-image
              v-if="disease.images && disease.images.length > 0"
              fit="contain"
              :lazy="true"
              :src="disease.images[0].objectStorageUrlPrefix + disease.images[0].originalImagePath"
              scroll-container=".disease-list-scroll"
            >
              <template #error>
                <div class="item-image-error"></div>
              </template>
              <template #placeholder>
                <i class="el-icon-loading" style="font-size: 14px;"></i>
              </template>
            </el-image>
            <div v-else class="item-image-no-data">暂无图片</div>
          </div>
          <div class="item-info">
            <div class="item-header">
              <div class="item-damageType">
                <svg-icon icon-class="disease-tag" />
                {{ disease.damageName || '未知类型' }}
              </div>
              <span class="item-location">{{getDescriptionText(disease)}}</span>
            </div>
            <div class="item-time">{{ formatTime(disease.createTime) }}</div>
          </div>
        </div>
      </div>
      <div v-if="loading" class="disease-list-table-loading">
        <i class="el-icon-loading"></i>
        加载中...
      </div>
      <div v-else-if="noMore" class="disease-list-table-no-more">{{ diseaseData.totalElements ? "没有更多了" : "暂无数据" }}</div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs'
import DiseaseTypeSelect from '@/components/DiseaseTypeSelect'
import RoadTypeCascader from '@/components/RoadTypeCascader'
import { getBelongToClass, ROAD_TYPE_OPTIONS, getBelongToColor } from '@/utils/cd_constants'
import { getDiseaseList } from '@/api/cheng-de'
import _ from 'lodash'

export default {
  name: 'DiseaseList',
  components: {
    DiseaseTypeSelect,
    RoadTypeCascader,
  },
  props: {
    diseaseData: {
      type: Object,
      default: () => ({}),
    },
    newDiseaseIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      timeRange: 3,
      TimeRangeOptions: [
        {
          value: 1,
          label: '今天',
        },
        {
          value: -1,
          label: '昨天',
        },
        {
          value: 3,
          label: '最近3天',
        },
        {
          value: 7,
          label: '最近7天',
        },
        {
          value: 14,
          label: '最近14天',
        },
        {
          value: 30,
          label: '最近30天',
        },
      ],
      ROAD_TYPE_OPTIONS,
      activeItem: null, // 当前高亮项
      page: 0,
      loading: false,
      isFilterChanging: false, // 标记筛选条件是否正在变更中
      // 本地请求参数
      requestParams: {
        startTime: '',
        endTime: '',
        damageType: '',
        type: '',
        roadType: '',
        belongTo: '',
        roadName: '',
        page: 0,
        size: 10,
      },
      lastContentIds: [], // 上次内容的ID列表，用于比较
    }
  },
  computed: {
    disabled() {
      // console.log('loading', this.loading)
      console.log('disabled', this.loading || this.noMore)
      return this.loading || this.noMore
    },
    noMore() {
      return this.diseaseData.content.length >= this.diseaseData.totalElements
    },
  },
  mounted() {
    // 监听清除高亮事件
    this.$parent.$on('clearHighlight', () => {
      this.clearHighlight()
    })

    // 初始化请求参数
    this.initRequestParams()
    this.fetchDiseaseList()
    
  },
  beforeDestroy() {
    this.$parent.$off('clearHighlight')
  },
  methods: {
    getQueryRecentOneDayDamagesFun() {
      // 如果正在加载或没有更多数据，则不执行
      if (this.loading || this.noMore) return

      // 设置加载状态
      this.loading = true

      // 设置当前页码
      this.requestParams.page = this.page

      // 直接调用API获取数据
      this.fetchDiseaseList(false)
    },
    // 直接请求病害列表数据
    fetchDiseaseList(isAutoRefresh = false) {
      // 如果是自动刷新请求，且当前有筛选条件正在变更，则跳过本次自动刷新
      if (isAutoRefresh && this.isFilterChanging) {
        console.log('跳过自动刷新，因为筛选条件正在变更中')
        return Promise.resolve() // 返回一个已解决的Promise
      }

      // 仅在非自动刷新时设置loading状态
      if (!isAutoRefresh) {
        this.loading = true
      }
      // 保存当前内容IDs用于比较
      if (this.diseaseData.content && this.diseaseData.content.length > 0) {
        this.lastContentIds = this.diseaseData.content.map((item) => item.id)
      }

      // 克隆请求参数，避免直接修改
      let params = _.cloneDeep(this.requestParams)

      // 如果是轮询，强制使用第一页
      if (isAutoRefresh) {
        params.page = 0
      }

      // 删除空值
      params = _.pickBy(
        params,
        (value) => value !== undefined && value !== null && value !== ''
      )

      // 返回Promise以便调用者可以链式调用
      return new Promise((resolve, reject) => {
        // 调用API
        getDiseaseList(params)
          .then((res) => {
            let updatedContent
            let newDiseaseIds = []

            if (isAutoRefresh) {
              // 轮询时，找出新数据
              const newItems = res.payload.content.filter(
                (item) => !this.lastContentIds.includes(item.id)
              )
              
              if (newItems.length > 0) {
                // 有新数据时，添加到列表前面
                newDiseaseIds = newItems.map(item => item.id)
                updatedContent = [...newItems, ...this.diseaseData.content]
              } else {
                // 没有新数据时，保持原样
                updatedContent = this.diseaseData.content
              }
            } else {
              // 非轮询时，保持原有的分页加载逻辑
              if (
                params.page > 0 &&
                this.diseaseData.content &&
                this.diseaseData.content.length > 0
              ) {
                // 如果是加载更多，则追加数据
                updatedContent = [
                  ...this.diseaseData.content,
                  ...res.payload.content,
                ]
              } else {
                // 否则直接替换数据
                updatedContent = res.payload.content || []
              }
            }

            // 更新总数据
            const updatedData = {
              content: updatedContent,
              totalElements: res.payload.totalElements || 0,
              totalPages: res.payload.totalPages || 0,
            }
            // 通知父组件数据已更新
            this.$emit('diseaseDataUpdated', {
              data: updatedData,
              newDiseaseIds: newDiseaseIds,
            })

            // 根据新数据判断是否有更多
            const hasMore = updatedContent.length < updatedData.totalElements

            // 如果有更多数据，增加页码（只在非轮询时）
            if (hasMore && !isAutoRefresh) {
              this.page++
            }
           
            // 非自动刷新请求完成后，通知父组件重新启动自动刷新
            if (!isAutoRefresh) {
              this.$emit('filterChangeComplete')
            }
           
            resolve(res) // 解决Promise
          })
          .catch((err) => {
            console.log('获取病害数据失败:', err)
            reject(err) // 拒绝Promise
          })
          .finally(() => {
            this.loading = false
          })
      })
    },

    resetPagination() {
      this.page = 0
      // 同时重置请求参数中的页码
      this.requestParams.page = 0
    },
    handleDiseaseTypeSelection(value) {
      this.activeItem = null
      this.resetPagination()

      // 标记筛选条件正在变更
      this.isFilterChanging = true

      // 更新本地请求参数
      this.requestParams.type = value.type
      this.requestParams.damageType = value.damageType
      this.requestParams.roadType = value.roadType

      // 直接请求数据
      this.fetchDiseaseList()
          .then(() => {
            // 请求完成后重置筛选变更标记
            this.isFilterChanging = false
          })
          .catch(() => {
            // 发生错误时也需要重置标记
            this.isFilterChanging = false
          })

      // 仍然发送事件通知父组件（用于其他可能的处理）
      this.$emit('handleDiseaseTypeChange', value)
    },
    // 时间范围改变
    handletimeRangeChange(value) {
      if (!value) {
        this.timeRange = 3
        value = 3
      }
      this.activeItem = null
      this.resetPagination()

      // 标记筛选条件正在变更
      this.isFilterChanging = true

      // 更新本地请求参数
      const { startTime, endTime } = this.timeRangeToDate(value)
      this.requestParams.startTime = startTime
      this.requestParams.endTime = endTime

      // 直接请求数据
      this.fetchDiseaseList()
          .then(() => {
            // 请求完成后重置筛选变更标记
            this.isFilterChanging = false
          })
          .catch(() => {
            // 发生错误时也需要重置标记
            this.isFilterChanging = false
          })

      // 仍然发送事件通知父组件
      this.$emit('handletimeRangeChange', value)
    },
    handleRoadTypeCascaderChange(val) {
      console.log('val', val)
      // val: { belongTo, roadName }
      this.resetPagination()
      
      // 标记筛选条件正在变更
      this.isFilterChanging = true
      
      // 更新本地请求参数
      this.requestParams.belongTo = val.belongTo
      this.requestParams.roadName = val.roadName
      // 直接请求数据
      this.fetchDiseaseList()
          .then(() => {
            // 请求完成后重置筛选变更标记
            this.isFilterChanging = false
          })
          .catch(() => {
            // 发生错误时也需要重置标记
            this.isFilterChanging = false
          })
      
      // 仍然发送事件通知父组件
      this.$emit('handleRoadTypeChange', val)
    },
    handleClickImage(disease, imageIndex = null) {
      // 创建唯一标识符
      const uniqueId = this.generateUniqueId(disease, imageIndex)

      // 设置高亮项
      this.setActiveItem(uniqueId)

      // 触发点击事件通知父组件
      this.$emit('clickDiseaseListImage', {
        disease,
        imageIndex,
      })
    },
    formatTime(time) {
      if (!time) return dayjs().format('YYYY-MM-DD HH:mm:ss')
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    setActiveItem(itemId) {
      this.activeItem = itemId
    },
    isItemActive(disease, imageIndex = null) {
      const uniqueId = this.generateUniqueId(disease, imageIndex)

      // 检查该项是否是当前高亮项
      return this.activeItem === uniqueId
    },
    // 生成唯一标识符
    generateUniqueId(disease, imageIndex = null) {
      return imageIndex === null
        ? `${disease.id}`
        : `${disease.id}_${imageIndex}`
    },
    // 清除高亮
    clearHighlight() {
      // 仅在开发环境下输出日志
      if (process.env.NODE_ENV !== 'production') {
        console.log('清除高亮状态')
      }
      this.activeItem = null
    },
    getDescriptionText(data) {
      if (!data) return ''

      const addressPart = data.addressDescription
        ? `${data.addressDescription}`
        : ''
      const damagePart = data.damageDescription
        ? `${data.damageDescription}`
        : ''

      if (addressPart && damagePart) {
        return `${addressPart}，${damagePart}`
      }

      return addressPart || damagePart
    },
    isNewDisease(id) {
      return this.newDiseaseIds && this.newDiseaseIds.includes(id)
    },
    getTagClass(disease) {
      // 使用统一配置的getBelongToClass函数
      return getBelongToClass(disease.belongTo)
    },
    // 获取病害标签颜色
    getBelongToColor(belongTo) {
      return getBelongToColor(belongTo)
    },
    // 添加时间范围转换方法
    timeRangeToDate(value) {
      let startTime, endTime

      switch (value) {
        case 1:
          // 今天
          startTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
          endTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
          break
        case -1:
          // 昨天
          startTime = dayjs()
            .subtract(1, 'day')
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss')
          endTime = dayjs()
            .subtract(1, 'day')
            .endOf('day')
            .format('YYYY-MM-DD HH:mm:ss')
          break
        default:
          startTime = dayjs()
            .subtract(value, 'day')
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss')
          endTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
          break
      }

      return {
        startTime,
        endTime,
      }
    },
    // 初始化请求参数
    initRequestParams() {
      // 设置初始时间范围
      const { startTime, endTime } = this.timeRangeToDate(this.timeRange)
      this.requestParams.startTime = startTime
      this.requestParams.endTime = endTime

      // 如果有其他初始参数，也可以在这里设置
      if (this.requestParams.belongTo && this.requestParams.roadName) {
        this.requestParams.belongTo = this.roadTypeCascaderValue[0].belongTo
        this.requestParams.roadName = this.roadTypeCascaderValue[0].roadName
      }
    },
  },
}
</script>
<style lang="scss" scoped>
$border-color: rgba(44, 116, 193, 0.5);
$is-active-color: #439EFF;
$gradient-color: linear-gradient(
  to right,
  rgba(68, 128, 221, 0.15),
  rgba(67, 142, 226, 0),
  rgba(65, 157, 231, 0.16)
);
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
::v-deep .disease-list-wrapper {
  overflow-x: hidden;
}
.disease-list-scroll {
  height: calc(100% - 54px - 15px);
  overflow-y: overlay;
  overflow-x: hidden;
  padding: 0 10px;
  box-sizing: border-box;

  /* 默认隐藏滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
    opacity: 0;
    transition: opacity 0.3s;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s;
  }
  &:hover::-webkit-scrollbar {
    opacity: 1;
  }
  &:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

/* 为不支持 overlay 的浏览器提供备选方案 */
@supports not (overflow: overlay) {
  .disease-list-scroll {
    overflow-y: auto;
    padding-right: 16px; /* 为滚动条预留空间 */
    margin-right: -6px; /* 抵消滚动条宽度 */
  }
}

.disease-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
 
  &-filter {
    height: 54px;
    width: 100%;
    margin-bottom: 15px;
    &-row {
      display: flex;
      gap: 10px;
    }
    &-item {
      flex: 1;
      height: 44px;
      &-content {
        flex: 1;
        ::v-deep .el-select {
          width: 100%;

          .el-input__inner {
            height: 44px;
            line-height: 44px;
            border-radius: 0px;
            color: #fff;
            border: 1px solid $border-color;
            background: $gradient-color;
          }

          .popper__arrow {
            border-bottom-color: $border-color;
            &::after {
              border-bottom-color: $border-color;
            }
          }
          /** 下拉框 */
          .el-select-dropdown {
            border: 1px solid $border-color;
            background: #040d2e;
            border-radius: 0px;
          }
          /** 下拉框选项 */
          .el-select-dropdown__item {
            color: #fff;
          }
          .el-select-dropdown__item.selected {
            color: $is-active-color;
            background: $gradient-color;
          }
          .el-select-dropdown__item.hover,
          .el-select-dropdown__item:hover {
            background: $gradient-color;
          }
        }

        ::v-deep .el-cascader {
          width: 100%;
          input {
            height: 44px;
            line-height: 44px;
            border-radius: 0px;
            color: #fff;
            border: 1px solid $border-color;
            background: $gradient-color;
          }
          .popper__arrow {
            border-bottom-color: $border-color;
            &::after {
              border-bottom-color: $border-color;
            }
          }
          .el-popper {
            border: 1px solid $border-color;
            background: #040d2e;
            border-radius: 0px;
          }
          .el-cascader-node {
            color: #fff;
          }
          .el-cascader-node.is-active {
            background: $gradient-color;
            color: #439EFF;
          }
          .el-cascader-menu {
            border-right: 1px solid $border-color;
          }
          .el-cascader-menu__wrap {
            min-height: 370px;
          }
          &:not(.is-disabled):hover .el-input__inner {
            border-color: $border-color;
          }
          .el-cascader-node:not(.is-disabled):hover,
          .el-cascader-node:not(.is-disabled):focus {
            background: $gradient-color;
          }
          .el-cascader__suggestion-item {
            color: #fff;
          }
          .el-cascader__suggestion-item:hover,
          .el-cascader__suggestion-item:focus {
            background: $gradient-color;
          }

          .el-input.is-focus .el-input__inner {
            border-color: $border-color;
          }
        }
      }
    }
  }
 
  &-table {
    flex: 1;
    max-height: 100%;
    min-height: 0;
    &-empty {
      margin-top: 50px;
      height: 35px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #999;
    }
    
    &-status-common {
      height: 35px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    &-loading {
      @extend .disease-list-table-status-common;
      color: #2d8cf0;
      .el-icon-loading {
        margin-right: 5px;
      }
    }

    &-no-more {
      @extend .disease-list-table-status-common;
      color: #999;
    }

    &-item {
      position: relative;
      height: 126px;
      margin-bottom: 10px;
      background: url('~@/assets/cheng-de-screen/list-item-bg.png') no-repeat
        bottom center;
      background-size: contain;
      padding: 4px 0px;
      box-sizing: border-box;
      cursor: pointer;

      &:last-child {
        margin-bottom: 0px;
      }

      .active {
        background: linear-gradient(
          to right,
          rgba(68, 128, 221, 0.1),
          rgba(68, 128, 221, 0.1)
        );
      }
      .disease-list-table-item-bg {
        width: 100%;
        height: 100%;
        display: flex;
        padding: 2px 10px;
        box-sizing: border-box;
        align-items: center;
      }
      .item-image-container {
        position: relative;
        width: 113px;
        height: 85px;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.2);

        .item-image-no-data {
          color: #999;
          font-size: 14px;
          line-height: 24px;
          z-index: 0;
        }

        .item-tag {
          position: absolute;
          top: 35px;
          left: -22px;
          z-index: 1;
          width: 80px;
          height: 18px;
          font-size: 12px;
          line-height: 18px;
          align-items: center;
          text-align: center;
          transform-origin: 0 0;
          transform: rotate(-45deg);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #040D2E;
          box-sizing: border-box;
        }
      }

      .item-info {
        flex: 1;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 85px;
        .item-damageType {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;
          color: white;
          padding: 0 10px 0 5px;
          font-size: 14px;
          line-height: 24px;
          height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .svg-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .item-header {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .item-location {
          color: white;
          font-size: 14px;
          line-height: 24px;
        }

        .item-time {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
          margin-top: auto;
        }
      }
    
    }
  }
}

.new-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 12px;
  height: 12px;
  background-color: #ff5252;
  border-radius: 50%;
  box-shadow: 0 0 5px 2px rgba(255, 82, 82, 0.7);
  animation: pulse 1.5s infinite;
  z-index: 2;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

.bounce-animation {
  animation: bounceList 0.45s ease-out 3;
  transform-origin: center center;
}

@keyframes bounceList {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    /* 增加跳动幅度和缩放效果 */
    transform: translateY(-10px) scale(1.04);
  }
}
::v-deep .el-loading-mask {
  margin-top: 50px;
  height: 35px;
  background-color: transparent;
}
</style>

