/*
 * @Author: guowy
 * @Date: 2020-08-18 17:27:16
 * @LastEditors: guowy
 * @LastEditTime: 2020-10-22 17:43:43
 */
module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: 2019, // 使用当前年月减1.5-2年。比如今天是2020年8月，那这里就用2018或者2019的。确保所用标准有1-2年的成熟期
    ecmaFeatures: {
      impliedStrict: true,
    },
    parser: 'babel-eslint',
    sourceType: 'module'
  },
  env: {
    browser: true,
    node: true,
    es6: true,
    es2017: true,
    jest: true,
  },
  extends: [
    'plugin:vue/recommended',
    'eslint:recommended',
    'eslint-config-airbnb-base',
    'plugin:import/errors',
    'plugin:import/warnings',
  ],
  plugins:[
    'vue'
  ],

  settings: {
    'import/resolver': {
      alias: {
        map: [
          ['@', './src/']
        ]
      }
    }
  },

  // add your custom rules here
  rules: {
    'comma-dangle': ['error', 'always-multiline'],
    indent: ['warn', 2],
    "vue/max-attributes-per-line": ['error', {
      "singleline": 10,
      "multiline": {
        "max": 1,
        "allowFirstLine": false
      }
    }],
    "vue/singleline-html-element-content-newline": "off",
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-unused-vars': [2, {
      'vars': 'all',
      'args': 'none'
    }],
    'padded-blocks': [2, 'never'],
    'quotes': [2, 'single', {
      'avoidEscape': true,
      'allowTemplateLiterals': true
    }],
    'max-len': ['warn', {'code': 120}],
    'no-var': 'error',
    'linebreak-style': ['error', 'windows'],
    semi: ['error', 'never', { beforeStatementContinuationChars: 'always' }],
    'no-shadow': 'off',
    'no-param-reassign': 'off'
  }
}
