<!--
 * @Author: guowy
 * @Date: 2020-02-19 16:51:54
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-08-03 16:02:05
 -->
<template>
  <div class="sidebar-logo-container" :class="[collapse ? 'collapse' : '', menuTheme]" @click="handleToHome">
    <transition name="sidebarLogoFade">
      <a
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        href="javascript:void(0)"
        :class="{
          'sidebar-logo-link-gj': title === 'AI巡检云平台',
          'sidebar-logo-link-ssby': title === '路面智能巡检仪',
          
        }"
      >
        <img
          v-if="logo"
          :src="logo"
          class="sidebar-logo"
          :class="{
            'sidebar-logo-fx': title === '峰巡',
            'sidebar-logo-xly': title === '巡路云',
            'sidebar-logo-ltsk': title === '数字道路一体化平台',
          }"
        >
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </a>
      <a
        v-else
        key="expand"
        class="sidebar-logo-link"
        href="javascript:void(0)"
        :class="{
          'sidebar-logo-link-gj': title === 'AI巡检云平台',
          'sidebar-logo-link-ssby': title === '路面智能巡检仪',
          'sidebar-logo-link-ltsk': title === '数字道路一体化平台',
        }"
      >
        <img
          v-if="logo"
          :src="logo"
          class="sidebar-logo"
          :class="{
            'sidebar-logo-fx': title === '峰巡',
            'sidebar-logo-xly': title === '巡路云',
          }"
        >
        <h1 class="sidebar-title">{{ title }}</h1>
      </a>
    </transition>
  </div>
</template>

<script>
// eslint-disable-next-line import/no-unresolved
import defaultLogo from '@/assets/logo.png'
import sfLogo from '@/assets/logo-sf.png'
import fxLogo from '@/assets/logo-fx-t.png'
import ylyLogo from '@/assets/login/logo-yly.png'
import gjLogo from '@/assets/login/logo-gj.png'
import xlyLogo from '@/assets/login/logo-xly.png'
import ssbyLogo from '@/assets/logo-ssby.png'
import ltskLogo from '@/assets/logo-ltsk.png'
import bjldxjLogo from '@/assets/login/logo-bjldxj.png'
import cdLogo from '@/assets/logo-cd.png'

// eslint-disable-next-line import/no-unresolved
import defaultSettings from '@/settings'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      title: defaultSettings.title || '',
      logo: defaultLogo,
    }
  },
  computed: {
    menuTheme() {
      return this.$store.state.settings.menuTheme
    },
  },
  created() {
    const { hostname, host } = window.location

    console.log('window.location', hostname, host)
    if (hostname === 'qinyi.yiluyun.robusoft.cn') {
      this.title = '翌路巡'
      this.logo = sfLogo
    } else if (hostname === 'fengxun.robusoft.cn' || host === '***************:10001') {
      this.title = '峰巡'
      this.logo = fxLogo
    } else if (hostname === 'yangluyun.robusoft.cn') {
      this.title = '养路云'
      this.logo = ylyLogo
    } else if (hostname === 'ai.guojiaomap.com') {
      // ai.guojiaomap.com
      this.title = 'AI巡检云平台'
      this.logo = gjLogo
    } else if (hostname === 'xunluyun.robusoft.cn') {
      // xunluyun.robusoft.cn
      this.title = '巡路云'
      this.logo = xlyLogo
    } else if (hostname === 'xunjian.robusoft.cn') {
      // 盛世博业
      this.title = '路面智能巡检仪'
      this.logo = ssbyLogo
    } else if (hostname === 'unicom.robusoft.cn' || host === '***************:10002') {
      // 联通数科
      this.title = '数字道路一体化平台'
      this.logo = ltskLogo
    } else if (host === '***************:10003') {
      // 北京绿道巡检管护平台
      // this.title = '北京绿道巡检管护平台'
      this.title = '绿道巡检'
      this.logo = bjldxjLogo
    } else if (host === '***************:10005') {
      this.title = '承德市政'
      this.logo = cdLogo
    } else {
      this.title = '智能巡检'
      this.logo = defaultLogo
    }
  },
  methods: {
    handleToHome() {
      const { path } = this.$route
      if (path === '/home') {
        this.$store.commit('account/SET_HOME')
      } else {
        this.$router.push({
          path: '/home',
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 63px;
  line-height: 63px;
  background: #191a23;
  text-align: center;
  overflow: hidden;
  border-bottom: 1px solid #101117;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;

      &.sidebar-logo-fx {
        margin-top: -6px;
      }

      &.sidebar-logo-xly {
        width: 31px!important;
        height: 31px!important;
        border-radius: 4px;
      }

      &.sidebar-logo-ltsk {
        width: 35px!important;
        height: 21px!important;
        border-radius: 4px;
      }

    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #ffffff;
      font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  & .sidebar-logo-link-gj {
    padding-left: 5px!important;

    img {
      width: 30px!important;
      height: 30px!important;
      border-radius: 4px;
    }

  }
  & .sidebar-logo-link-ssby {
    padding-left: 6px!important;
    img {
      width: 24px!important;
      height: 24px!important;
      margin-right: 6px!important;
    }
    .sidebar-title {
      font-size: 15px;
    }
  }
  // 联通数科
  & .sidebar-logo-link-ltsk {
    padding-left: 3px!important;
    img {
      width: 20px!important;
      height: 12px!important;
      margin-right: 5px!important;
    }
    .sidebar-title {
      font-size: 13px;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }

    .sidebar-logo-link-gj {
      padding-left: 0!important;
    }
  }

  &.light, &.light-green {
    background: #ffffff;
    border-bottom: 1px solid #f8f8f9;

    & .sidebar-logo-link {
      & .sidebar-title {
        color: #333333;
      }
    }
  }
}
</style>
