<template>
  <div ref="slider" class="slider" @click.stop="handelClickSlider">
    <div class="process" :style="{ width, background: bgColor }" />
    <div ref="trunk" class="thunk" :style="{ left }">
      <div ref="dot" class="block" />
    </div>
  </div>
</template>
<script>
/*
 * min 进度条最小值
 * max 进度条最大值
 * v-model 对当前值进行双向绑定实时显示拖拽进度
 * */
export default {
  props: {
    // 最小值
    min: {
      type: Number,
      default: 0,
    },
    // 最大值
    max: {
      type: Number,
      default: 100,
    },
    // 当前值
    value: {
      type: Number,
      default: 0,
    },
    // 进度条颜色
    bgColor: {
      type: String,
      default: '#545e72',
    },
    // 是否可拖拽
    isDrag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      slider: null, // 滚动条DOM元素
      thunk: null, // 拖拽DOM元素
      per: this.value, // 当前值
    }
  },
  computed: {
    // 设置一个百分比，提供计算slider进度宽度和trunk的left值
    // 对应公式为  当前值-最小值/最大值-最小值 = slider进度width / slider总width
    // trunk left =  slider进度width + trunk宽度/2
    scale() {
      return (this.per - this.min) / (this.max - this.min)
    },
    width() {
      return this.slider ? `${this.slider.offsetWidth * this.scale}px` : '0px'
    },
    left() {
      return this.slider ? `${this.slider.offsetWidth * this.scale - this.thunk.offsetWidth / 2}px` : '0px'
    },
  },
  watch: {
    value: {
      handler() {
        this.per = this.value
      },
    },
  },
  mounted() {
    this.slider = this.$refs.slider
    this.thunk = this.$refs.trunk
    const _this = this
    if (!this.isDrag) return
    this.thunk.onmousedown = function (e) {
      const width = parseInt(_this.width)
      const disX = e.clientX
      document.onmousemove = function (e) {
        // value, left, width
        // 当value变化的时候，会通过计算属性修改left，width
        // 拖拽的时候获取的新width
        const newWidth = e.clientX - disX + width
        // 计算百分比
        const scale = newWidth / _this.slider.offsetWidth

        _this.per = ((_this.max - _this.min) * scale + _this.min).toFixed(3)
        // 限制值大小
        _this.per = Math.max(_this.per, _this.min)
        _this.per = Math.min(_this.per, _this.max)

        _this.$emit('input', _this.per)
      }
      document.onmouseup = function () {
        // 当拖拽停止发送事件
        _this.$emit('stop', _this.per)
        // 清除拖拽事件
        document.onmousemove = document.onmouseup = null
      }
    }
  },
  methods: {
    handelClickSlider(event) {
      // 禁止点击
      if (!this.isDrag) return
      const { dot } = this.$refs
      if (event.target == dot) return
      // 获取元素的宽度l
      const width = this.slider.offsetWidth
      // 获取元素的左边距
      const ev = event || window.event
      // 获取当前点击位置的百分比
      const scale = ((ev.offsetX / width) * 100).toFixed(3) * 1
      this.per = scale
      this.$emit('stop', this.per)
    },
  },
}
</script>
<style lang="scss" scoped>

.clear:after {
  content: "";
  display: block;
  clear: both;
}

.slider {
  position: relative;
  width: 100%;
  height: 6px;
  top: 50%;
  background: #e6ebf5;
  border-radius: 5px;
  cursor: pointer;
  z-index: 99999;
  .process {
    position: absolute;
    left: 0;
    top: 0;
    width: 110px;
    height: 6px;
    border-radius: 5px;
    background: #545e72;
    z-index: 111;
  }
  .thunk {
    position: absolute;
    left: 100px;
    top: -50%;
    width: 12px;
    height: 12px;
    z-index: 122;
    .block {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #545e72;
      transition: 0.2s all;
      &:hover {
        transform: scale(1.15);
      }
    }
  }
}
</style>
