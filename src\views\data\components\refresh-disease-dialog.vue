<template>
  <el-dialog
    :title="title"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :append-to-body="false"
    :modal="modal"
    class="my-dialog"
    :destroy-on-close="true"
  >
    <el-row :gutter="30">
      <el-col :span="4">模型选择</el-col>
      <el-col :span="20">
        <el-select v-model="modelId" size="small" style="width: 100%;" @change="handleModelChange">
          <el-option
            v-for="item in modelList"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </el-col>
    </el-row>

    <el-row :gutter="30">
      <el-col :span="4">公共置信度</el-col>
      <el-col :span="20">
        <el-slider
          v-model.number="commonAccuracy"
          style="pointer-events: auto;"
          :min="0.01"
          :max="1"
          :step="0.01"
          show-input
          @input.native="inputChane"
          @change="handelChangeAfterThreshold"
        />
      </el-col>
    </el-row>
    <el-row v-for="(item, index) in accuracyList" :key="item.label" :gutter="30">
      <el-col :span="4">{{ item.chineseName }}</el-col>
      <el-col :span="20">
        <el-slider
          v-model.number="item.accuracy"
          style="pointer-events: auto;"
          :min="0.01"
          :max="1"
          :step="0.01"
          show-input
          @input.native="inputChane"
          @change="handelChangeAfterThreshold"
        />
      </el-col>
    </el-row>
    <span v-if="footerShow" slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handelSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import ConstantsMixin from '@/mixin/constants'
import { getModels } from '@/api/model'

export default {
  mixins: [ConstantsMixin],
  props: {
    modal: {
      type: Boolean,
      default: true,
    },
    footerShow: {
      type: Boolean,
      default: true,
    },
    sliderChange: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      modelList: [],
      version: null,
      taskId: '',
      title: '',
      accuracyList: [],
      commonAccuracy: 0.3,
      modelId: '',
    }
  },
  methods: {

    handleModelChange(val) {
      const temp = this.modelList.find((item) => item.id === val)
      if (temp) {
        this.commonAccuracy = temp.modelDetail.commonAccuracy
        this.accuracyList = temp.modelDetail.labelList
      }
    },
    async show(row) {
      const that = this
      this.dialogVisible = true
      this.taskId = row.id
      const modelType = row.roadType
      this.title = `${row.id}-${that.Model_Cn_Name[modelType]}-置信度设置`
      const { payload } = await getModels({ modelType, status: '1' })
      this.modelList = payload.map((item) => {
        item.label = `${that.Model_Cn_Name[item.modelType]}  ${item.versionName}`
        return item
      }).filter((item) => item !== undefined)

      if (payload.length) {
        this.accuracyList = payload[0].modelDetail.labelList
        this.commonAccuracy = payload[0].modelDetail.commonAccuracy
        this.modelId = payload[0].id
      }
    },
    async handelSubmit() {
      this.dialogVisible = false
      const params = {
        taskId: this.taskId,
        apiUrl: this.modelList.find((item) => item.id === this.modelId)?.apiUrl || '',
        modelDetail: {
          commonAccuracy: this.commonAccuracy,
          labelList: this.accuracyList,
        },
      }

      this.$emit('nextDiseaseRefresh', params)
    },
    handleClose() {
      this.dialogVisible = false
      this.modelList = []
      this.$emit('onResetAccuracyList')
    },
    handelChangeAfterThreshold() {

      // if(!this.sliderChange) return
      // const params = {
      //   accuracyList: [],
      // }
      // if(this.modelType === 1) {
      //   // 沥青路面
      //   params.accuracyList = this.pitch.map(({ value }) => value);
      // } else if (this.modelType === 2) {
      //   // 水泥混凝土路面
      //   params.accuracyList = this.cement.map(({ value }) => value);
      // }
      // this.$emit('nextDiseaseRefresh', params)

    },
    onResetAccuracyList() {
      const that = this
      that.pitch.forEach((item, index) => {
        item.value = that.defaultPitchValue[index]
      })
      that.cement.forEach((item) => {
        item.value = 0.1
      })
    },
    inputChane(event, index) {
      const { min, max, value } = event.target
      if (isNaN(value) || !value) {
        event.target.value = 0
        return
      }
      console.log(value >= parseInt(max))
      const regex = /^(0(\.\d{0,3})?|1(\.0{0,3})?)$/
      if (regex.test(value)) {
        if (value >= parseInt(max)) {
          event.target.value = max * 1
          return
        }
        event.target.value = value
      } else if (value >= parseInt(max)) {
        event.target.value = max * 1
      } else {
        // 如果超过两位小数，只保留两位小数
        event.target.value = parseFloat(value).toFixed(3) * 1
      }
    },
  },
}
</script>

<style lang='scss' scoped>
/* @import url(); 引入css类 */
.el-col {
  line-height: 2.5;
}

::v-deep .el-dialog {
  .el-dialog__body {
    padding: 10px 20px;
    max-height: 60vh;
    overflow-y: auto;
  }
}
::v-deep .el-dialog__headerbtn {
  pointer-events: auto !important;
}

</style>
