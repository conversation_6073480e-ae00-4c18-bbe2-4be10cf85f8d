/*
 * @Author: wangyj
 * @Date: 2022-08-04 15:36:22
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-08-11 18:03:41
 */

<template>
  <div class="video-container">
    <div v-loading="loading" class="video-box">
      <canvas id="playCanvas" :width="canvasWidth" :height="canvasHeight" style="display: none" />
      <template v-if="!loading">
        <template v-if="imageUrls.length > 0">
          <div v-show="imgError" class="empty">暂无图片</div>
          <img v-show="!imgError" lazy mode="aspectFit" :src="imageUrl" @error="imgError = true" @load="imgError =false">
        </template>
        <template v-else>
          <div class="empty">暂无图片</div>
        </template>
      </template>
    </div>
    <div v-if="imageUrls.length > 0" class="video-progress">
      <el-progress :percentage="percentage" :show-text="false" color="#545E72" />
      <i class="el-icon-refresh-left" title="返回起点" @click="handleReturnToStart" />
    </div>
    <div v-if="imageUrls.length > 0" class="video_btn">
      <p v-show="!playFlag" class="active" @click="handelePlay" @mouseover="playBool = true" @mouseout="playBool = false">
        <i v-if="playBool == true"> <img src="@/assets/video/play_icon.png" alt=""></i>
        <i v-else> <img src="@/assets/video/play_default_icon.png" alt=""></i>
        播放
      </p>
      <p v-show="playFlag" @click="handeleStop" @mouseover="stopBool = true" @mouseout="stopBool = false">
        <i v-if="stopBool == true"> <img src="@/assets/video/stop_icon.png" alt=""></i>
        <i v-else> <img src="@/assets/video/stop_default_icon.png" alt=""></i>
        暂停
      </p>
      <p @click="handeleLast" @mouseover="lastFrameBool = true" @mouseout="lastFrameBool = false">
        <i v-if="lastFrameBool == true"> <img src="@/assets/video/last_frame_icon.png" alt=""></i>
        <i v-else> <img src="@/assets/video/last_frame_default_icon.png" alt=""></i>
        上一帧
      </p>
      <p @click="handeleNext" @mouseover="nextFrameBool = true" @mouseout="nextFrameBool = false">
        <i v-if="nextFrameBool == true"> <img src="@/assets/video/next_frame_icon.png" alt=""></i>
        <i v-else> <img src="@/assets/video/next_frame_default_icon.png" alt=""></i>
        下一帧
      </p>
      <p>
        <el-select v-model="slowValue" placeholder="慢放倍率" size="small" @change="handelChangeSlow">
          <el-option v-for="(sel, index) in slowPlayList" :key="index + sel.value" :label="sel.label" :value="sel.value" />
        </el-select>
      </p>
    </div>
  </div>
</template>

<script>
import { correctDisease } from '@/api/data'

export default {
  props: {
    pageQuery: {
      type: Object,
      default: () => ({
        page: 0,
        size: 100,
      }),
    },
    speed: {
      type: String,
      default: '慢速播放',
    }, // 当前播放倍数
    getFun: {
      type: Function,
    },
  },
  data() {
    return {
      loading: true,
      imgError: false,
      playBool: false,
      stopBool: false,
      lastFrameBool: false,
      nextFrameBool: false,
      slowValue: '慢速播放',
      slowPlayList: [{
        value: '1',
        label: '1.0X',
      },
      {
        value: '2',
        label: '0.5X',
      },
      {
        value: '5',
        label: '0.2X',
      },
      {
        value: '10',
        label: '0.1X',
      }],
      imageIndex: 0, // 当前播放图片索引
      imageUrl: null, // 当前播放图片
      imageUrls: [], // 所有病害图集合
      timer: null, // 定时器
      playFlag: false, // 是否正在播放
      percentage: 0, // 播放进度
      totalPages: 0,
      totalElements: 0,
      canvasWidth: 640,
      canvasHeight: 360,
      canvas: null,
      ctx: null,
      lineIndexArr: [], // 有病害图片的数据索引
      keyIndexArr: [], // 每段数据对应的图片索引值，存在即要进入到下一段
      lineData: [],
    }
  },
  computed: {
    videoTimeSpeed() { // 播放倍速
      if (this.slowValue === '慢速播放') {
        return 200
      }
      return parseInt(this.slowValue) * 200
    },
  },
  watch: {
    imageIndex(val) {
      if (this.getFun) {
        if (val !== 0 && val % 50 === 0 && this.pageQuery.page < this.totalPages - 1) {
          const num = val / 50
          if (num % 2 !== 0) {
            let { page } = this.pageQuery
            this.$emit('updatePage', page += 1)
            this.getPictures()
          }
        }
      } else if (val === 0) {
        const dataIndex = this.lineIndexArr[0]
        this.$emit('updateCar', dataIndex)
      } else {
        const j = this.keyIndexArr.indexOf(val)
        if (j > -1) {
          // 要换到下一段数据
          const dataIndex = this.lineIndexArr[j]
          this.$emit('updateCar', dataIndex)
        }
      }
    },
    speed: {
      immediate: true,
      handler(newVal, oldVal) {
        this.slowValue = newVal
      },
    },
  },
  mounted() {
    this.canvas = document.getElementById('playCanvas')
    this.ctx = this.canvas.getContext('2d')
  },
  methods: {
    async initImgs(lineData) {
      this.imageIndex = 0
      if (lineData) {
        this.getPicturesFromData(lineData)
      } else {
        this.getPictures()
      }
      this.doPlay()
    },
    loadImg(src) {
      return new Promise((resolve, reject) => {
        const image = new Image()
        image.src = src
        image.setAttribute('crossOrigin', 'anonymous')
        image.onload = () => {
          resolve(image)
        }
        image.onerror = () => {
          resolve('')
        }
      })
    },
    async getPictures() {
      const that = this
      const { payload } = await this.getFun()
      this.totalPages = payload.totalPages
      this.totalElements = payload.totalElements

      const imgQueue = []
      const coordinateArr = []
      for (let i = 0; i < payload.content.length; i++) {
        const { pictureUrl, coordinates } = payload.content[i]
        imgQueue.push(that.loadImg(pictureUrl))
        coordinateArr.push(coordinates)
      }

      Promise.all(imgQueue).then((arr) => {
        arr.forEach((image, i) => {
          let imgSrc = ''
          if (image !== '') {
            that.canvasWidth = image.width
            that.canvasHeight = image.height

            that.ctx.drawImage(image, 0, 0, image.width, image.height)
            that.ctx.lineWidth = 2
            that.ctx.font = 'bold 18px'

            if (coordinateArr[i] && coordinateArr[i].length > 0) {
            // 图片中存在病害
              coordinateArr[i].forEach((item) => {
                that.ctxDraw(item.type, item.coordinate)
              })
            }

            imgSrc = that.canvas.toDataURL()
          }

          if (i === 0 && this.pageQuery.page === 0) {
            this.imageUrl = imgSrc
          }
          this.imageUrls.push(imgSrc)
        })

        this.loading = false
      }).catch((e) => {
        console.log(e)
        this.loading = false
      })
    },
    async getPicturesFromData(lineData) {
      const that = this
      this.$nextTick(async () => {
        this.canvas = document.getElementById('playCanvas')
        this.ctx = this.canvas.getContext('2d')
        this.lineData = lineData

        const imgQueue = []
        const typeArr = []
        const coordinateArr = []
        for (let dataI = 0; dataI < lineData.length; dataI++) {
          if (lineData[dataI].images) {
            const data = lineData[dataI]
            this.lineIndexArr.push(dataI)
            that.totalElements += data.images.length
            that.keyIndexArr.push(that.totalElements - 1)

            for (let i = 0; i < data.images.length; i++) {
              const { picture, type, coordinate } = data.images[i]
              imgQueue.push(this.loadImg(picture))
              typeArr.push(type)
              coordinateArr.push(coordinate)
            }
          }
        }

        Promise.all(imgQueue).then((arr) => {
          arr.forEach((image, i) => {
            that.canvasWidth = image.width
            that.canvasHeight = image.height

            // 图片标注病害处理

            that.ctx.drawImage(image, 0, 0, image.width, image.height)
            that.ctx.lineWidth = 2
            that.ctx.font = 'bold 18px'

            // #0000FF 蓝
            // #00FF00 绿
            // #FF0000 红
            // #FFCC00 黄
            that.ctxDraw(typeArr[i], coordinateArr[i])

            const imgSrc = that.canvas.toDataURL()

            if (i === 0) {
              that.imageUrl = imgSrc
            }
            that.imageUrls.push(imgSrc)
          })
          that.loading = false
        }).catch((e) => {
          console.log(e)
          this.loading = false
        })
      })
    },
    ctxDraw(type, coordinate) {
      const that = this
      let txt = ''
      if (type === 'longitudinal') {
        that.ctx.strokeStyle = '#0000FF'
        that.ctx.fillStyle = '#0000FF'
        txt = '纵向裂纹'
      } else if (type === 'transverse') {
        that.ctx.strokeStyle = '#00FF00'
        that.ctx.fillStyle = '#00FF00'
        txt = '横向裂纹'
      } else if (type === 'aligator') {
        that.ctx.strokeStyle = '#FF0000'
        that.ctx.fillStyle = '#FF0000'
        txt = '龟裂'
      } else if (type === 'pothole') {
        that.ctx.strokeStyle = '#FFCC00'
        that.ctx.fillStyle = '#FFCC00'
        txt = '坑洞'
      } else {
        that.ctx.strokeStyle = '#ff00cc'
        that.ctx.fillStyle = '#ff00cc'
        txt = '其他'
      }
      const coordinateArr = coordinate.split(' ')
      that.ctx.strokeRect(coordinateArr[0] * 1, coordinateArr[1] * 1, coordinateArr[2] - coordinateArr[0], coordinateArr[3] - coordinateArr[1])
      that.ctx.fillText(txt, coordinateArr[0] * 1 + 2, coordinateArr[1] * 1 + 14, coordinateArr[2] - coordinateArr[0] - 4)
    },
    doPlay() {
      const that = this
      clearTimeout(this.timer)

      if (this.playFlag) {
        if (that.imageIndex < that.imageUrls.length - 1) {
          that.imageIndex += 1
          that.imageUrl = that.imageUrls[that.imageIndex]
          that.percentage = (that.imageIndex / that.totalElements).toFixed(2) * 100
        } else {
          this.playFlag = false
          this.percentage = 100
        }
      }

      this.timer = setTimeout(() => {
        that.doPlay()
      }, this.videoTimeSpeed)
    },
    handleReturnToStart() {
      this.imageIndex = 0
      this.imageUrl = this.imageUrls[this.imageIndex]
      this.percentage = 0
    },
    handelePlay() {
      this.playFlag = true
    },
    handeleStop() {
      this.playFlag = false
    },
    handeleLast() {
      if (this.playFlag) {
        this.$message({
          message: '请先暂停视频',
          type: 'error',
        })
        return
      }
      if (this.imageIndex === 0) {
        this.$message({
          message: '已经是第一张图片了',
          type: 'warning',
        })
        return
      }

      this.imageIndex -= 1
      this.imageUrl = this.imageUrls[this.imageIndex]
      this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
    },
    handeleNext() {
      if (this.playFlag) {
        this.$message({
          message: '请先暂停视频',
          type: 'error',
        })
        return
      }
      if (this.imageIndex === this.imageUrls.length - 1) {
        this.$message({
          message: '已经是最后一张图片了',
          type: 'warning',
        })
        return
      }

      this.imageIndex += 1
      this.imageUrl = this.imageUrls[this.imageIndex]
      this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
    },
    handelChangeSlow(val) {
      // this.videoTimeSpeed = parseInt(val) * 100
    },
    reset() {
      this.imageUrl = null
      this.imageUrls = []
      clearTimeout(this.timer)
      this.timer = null
      this.playFlag = false
      this.percentage = 0
      this.slowValue = this.speed
      // this.videoTimeSpeed = '200'
      this.loading = true
      this.imgError = false
      this.pageQuery.page = 0
    },
    // 点击地图上的点后更新视频到相应位置
    // 如果点击的点对应的这段有视频，则显示这段的第一张图片
    // 如果点击的点对应的这段没有视频，则显示暂无图片，进度调整到上一个有视频的这段
    handleUpdateVideo(dataIndex) {
      if (this.lineIndexArr.indexOf(dataIndex) > -1) {
        const j = this.lineIndexArr.indexOf(dataIndex)
        const keyIndex = this.keyIndexArr[j]
        const { length } = this.lineData[dataIndex].images

        this.imageIndex = keyIndex - length + 1
        this.imageUrl = this.imageUrls[this.imageIndex]
        this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
      } else {
        const i = this.lineIndexArr.findIndex((item) => item > dataIndex)
        if (i > 0) {
          const keyIndex = this.keyIndexArr[i - 1]
          this.imageIndex = keyIndex
          this.imageUrl = ''
          this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
        } else {
          const keyIndex = this.keyIndexArr[this.keyIndexArr.length - 1]
          this.imageIndex = keyIndex
          this.imageUrl = ''
          this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
    .video-container {
        .video-box {
        width: 100%;
        border-radius: 6px;
        margin-bottom: 15px;
        position: relative;
        height: 370px;

        img {
            width: 100%;
            height: 100%;
            border-radius: 6px;
        }
        .empty {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .play-icon {
            position:absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            .el-icon-video-play {
              font-size: 100px;
              color: rgba(0,0,0,0.4)
            }
        }
        }
        .video-progress {
            padding: 0 15px 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .el-progress {
                width: 96%
            }
            i {
                font-size: 18px;
                cursor: pointer;
            }
        }
        .video_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 50px;
            background: #EDF0F4;
            border-radius: 31px;
            margin: 0 10px;
            padding: 0 5px;

            p {
                flex: 1;
                height: 28px;
                background: #FFFFFF;
                border-radius: 22px;
                font-size: 14px;
                font-family: Source Han Sans CN;
                font-weight: 500;
                color: #545E72;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 5px;
                cursor: pointer;

                &:hover {
                background: #306AF1;
                box-shadow: 0px 7px 11px rgba(48, 106, 241, 0.31);
                color: #fff;
                }

                &:last-child {
                padding: 0;
                }

                i {
                display: block;
                width: 19px;
                height: 19px;
                margin-right: 8px;

                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                }
                }

                &:nth-child(3),
                &:nth-child(4) {
                i {
                    width: 16px;
                    height: 16px;
                }
                }

                ::v-deep .el-input__inner {
                border-radius: 22px;
                border: none;
                }
            }
        }
    }
</style>
