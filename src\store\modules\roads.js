import { getRoadList } from '@/api/cheng-de'
import { ROAD_TYPE_OPTIONS } from '@/utils/cd_constants'

const state = {
  allRoads: [], // 所有道路数据
  roadsByBelongTo: {}, // 按归属类型分组的道路数据
  lastFetchTime: null, // 最后一次获取数据的时间
}

const mutations = {
  SET_ALL_ROADS: (state, roads) => {
    state.allRoads = roads
  },
  SET_ROADS_BY_BELONG_TO: (state, roadsByBelongTo) => {
    state.roadsByBelongTo = roadsByBelongTo
  },
  SET_LAST_FETCH_TIME: (state, time) => {
    state.lastFetchTime = time
  }
}

const actions = {
  // 获取所有道路数据
  fetchAllRoads({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 如果已有数据，直接返回
      if (state.allRoads.length > 0 && state.roadsByBelongTo && Object.keys(state.roadsByBelongTo).length > 0) {
        resolve(state.roadsByBelongTo)
        return
      }

      // 调用API获取数据
      getRoadList({ belongTo: "" })
        .then(res => {
          const allRoads = res.payload || []
          commit('SET_ALL_ROADS', allRoads)
          
          // 按belongTo属性对道路数据进行分组
          const roadsByBelongTo = allRoads.reduce((acc, road) => {
            // 确保belongTo存在，如果不存在则分到"其他"类别
            const belongTo = road.belongTo || 'other'
            
            // 如果该belongTo类别不存在，则创建一个空数组
            if (!acc[belongTo]) {
              acc[belongTo] = []
            }
            
            // 将道路添加到对应的belongTo类别中
            acc[belongTo].push(road)
            
            return acc
          }, {})
          
          // 确保数据结构完整性
          ROAD_TYPE_OPTIONS.forEach(option => {
            if (!roadsByBelongTo[option.value]) {
              roadsByBelongTo[option.value] = []
            }
          })
          
          commit('SET_ROADS_BY_BELONG_TO', roadsByBelongTo)
          commit('SET_LAST_FETCH_TIME', new Date().getTime())
          
          resolve(roadsByBelongTo)
        })
        .catch(error => {
          // 错误处理
          console.error('获取道路数据失败:', error)
          
          // 创建空数据结构，确保界面不会崩溃
          const emptyRoadsByBelongTo = {}
          
          // 为每个归属类型创建空数组
          ROAD_TYPE_OPTIONS.forEach(option => {
            emptyRoadsByBelongTo[option.value] = []
          })
          
          commit('SET_ROADS_BY_BELONG_TO', emptyRoadsByBelongTo)
          reject(error)
        })
    })
  },
  
  // 强制刷新道路数据
  refreshRoads({ dispatch }) {
    return dispatch('fetchAllRoads', null, { force: true })
  }
}

const getters = {
  // 获取按归属类型分组的道路数据
  getRoadsByBelongTo: state => state.roadsByBelongTo,
  
  // 获取所有道路数据
  getAllRoads: state => state.allRoads,
  
  // 获取最后一次获取数据的时间
  getLastFetchTime: state => state.lastFetchTime
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 