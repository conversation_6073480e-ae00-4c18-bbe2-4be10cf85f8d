<template>
  <bm-overlay
    ref="customOverlay"
    :class="{sample: true}"
    pane="labelPane"
    @draw="draw"
  >
    <div @contextmenu.prevent.stop="deleteShow = true">{{ index + 1 }}</div>
    <div v-show="deleteShow" class="delete-cont" @click.stop="deletePoint">删除顶点</div>
  </bm-overlay>
</template>
<script>
import { BmOverlay } from 'vue-baidu-map'

export default { // 用来接受传入的值，用来定制样式
  components: {
    BmOverlay,
  },
  props: ['index', 'position'],
  data() {
    return {
      deleteShow: false,
    }
  },
  watch: {
    position: {
      handler() {
        this.$refs.customOverlay.reload() // 当位置发生变化时，重新渲染，内部会调用draw
      },
      deep: true,
    },
  },
  methods: {
    // 这是百度地图的重绘函数,用于维持覆盖物的位置（这里的值貌似会影响拖拉时的偏移度）
    draw({ el, BMap, map }) {
      const that = this
      const { lng, lat } = this.position
      const pixel = map.pointToOverlayPixel(new BMap.Point(lng, lat))
      el.style.left = `${pixel.x - 16}px`
      el.style.top = `${pixel.y - 16}px`

      el.getElementsByClassName('delete-cont')[0].style.left = `32px`
      el.getElementsByClassName('delete-cont')[0].style.top = `16px`

      map.addEventListener('click', (e) => {
        // this.points.push(e.point)
        that.deleteShow = false
      })
    },
    deletePoint() {
      this.$emit('deletePoint', this.index)
    },
  },
}
</script>
<style lang="scss" scoped>
.sample {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 32px;
  background: rgba(87,163,243, 0.8);
  // overflow: hidden;
  // box-shadow: 0 0 5px #57a3f3;
  color: #fff;
  text-align: center;
  position: absolute;
  font-size: 12px;

  .delete-cont {
    width: 100px;
    line-height: 20px;
    position: absolute;
    border: 1px solid #ccc;
    box-shadow: rgb(102, 102, 102) 1px 2px 6px;
    background: #fff;
    color: #000;
    border-radius: 5px;
    padding: 2px 6px;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      color: rgb(102, 136, 204)
    }
  }
}
.sample.active {
  background: rgba(0,0,0,0.75);
  color: #fff;
}
</style>
