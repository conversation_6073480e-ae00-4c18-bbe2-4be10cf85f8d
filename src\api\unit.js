import request from '@/utils/request'

/**
 * 列表查询
 * @param {*} params
 * @returns
 */
export function getUnits(params) {
  return request({
    url: '/work-units',
    method: 'get',
    params,
  })
}
/**
 * 查询单位名称下拉列表
 * @returns
 */
export function getUnitsNames() {
  return request({
    url: '/work-units/names',
    method: 'get',
  })
}

/**
 * 查看单位详情
 * @param {*} id
 * @returns
 */
export function getUnitDetail(id) {
  return request({
    url: `/work-units/${id}`,
    method: 'get',
  })
}

/**
 * 添加/修改 不传id是新增，传id是修改
 * @param {*} data
 * @returns
 */
export function createOrUpdateUnit(data) {
  return request({
    url: '/work-units',
    method: 'post',
    data,
  })
}

/**
 * 删除单位
 * @param {*} params
 * @returns
 */
export function deleteUnit(params) {
  return request({
    url: '/work-units/delete',
    method: 'get',
    params,
  })
}

export function getUnitInfo() {
  return request({
    url: '/work-units/unit-info',
    method: 'get',
  })
}
