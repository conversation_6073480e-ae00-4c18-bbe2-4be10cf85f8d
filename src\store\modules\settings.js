/*
 * @Author: guowy
 * @Date: 2020-05-26 09:42:38
 * @LastEditors: guowy
 * @LastEditTime: 2021-02-20 10:14:19
 */
import { tagsView, fixedHeader, sidebarLogo } from '@/settings'
import { menuTheme, login } from '@/robu-settings'

const state = {
  tagsView,
  fixedHeader,
  sidebarLogo,
  menuTheme,
  login,
}
const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
}
const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
