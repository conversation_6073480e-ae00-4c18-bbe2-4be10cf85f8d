<!--
 * @Author: guowy
 * @Date: 2020-02-19 16:51:53
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-08-03 16:02:11
 -->

<h1 align="center">智能巡检</h1>

## 项目说明

基于[vue-cli3](https://cli.vuejs.org/zh/)、[vue-admin-template](http://panjiachen.github.io/vue-admin-template)构建的基础项目

## 技术架构

- node (>= 10.0)
- yarn
- webpack
- eslint
- @vue/cli ~3
- [Element UI组件](https://element.eleme.cn/#/zh-CN/component/installation)
- vue-router
- vuex
- axios
- [async-validator表单校验规则](https://github.com/yiminghe/async-validator)

## 项目结构
```
├── build                      // 构建相关  
├── mock                       // 项目mock 模拟数据
├── public                     // 入口资源(入口，图标等)
│   ├── favicon.ico            // favicon图标
│   ├── index.html             // html模板
├── src                        // 源代码
│   ├── api                    // 所有请求
│   ├── assets                 // 主题 字体等静态资源
│   ├── components             // 全局公用组件
│   ├── filters                // Vue全局过滤器
│   ├── icons                  // 项目所有 svg icons
│   ├── layout                 // 布局
│   ├── router                 // 路由
│   ├── store                  // 全局 store管理
│   ├── styles                 // 全局样式
│   ├── utils                  // 全局公用方法
│   ├── views                  // view
│   ├── App.vue                // 入口页面
│   ├── main.js                // 入口 加载组件 初始化等
│   └── permission.js          // 权限管理
│   └── setting.js             // 网站配置
├── tests                      // 测试用例
├── .editorconfig              // 编辑器配置
├── .env.development           // 开发环境配置
├── .env.production            // 生产环境配置
├── .env.staging               // 模拟生产环境 
├── .eslintignore              // eslint 忽略项
├── .eslintrc.js               // eslint 配置项
├── .gitignore                 // git 忽略项
├── .yarnrc                     // 私有库地址配置
├── .npmrc                     // 私有库地址配置
├── .travis.yml                // 持续集成服务配置文件
└── babel.config.js            // babel配置
└── jest.config.js             // jest配置
├── jsconfig.json              // 指定根文件和JavaScript语言服务提供的功能选项。
└── package.json               // package.json
├── README.md                  // 说明文档
└── vue.config.js              // vue配置
```

## 功能

```
```

## 构建

```bash
# 安装依赖
yarn install

# 启动服务
yarn run dev
```
 
浏览器访问 [http://localhost:9528](http://localhost:9528)

## 发布

```bash
# 构建测试环境
yarn run build:stage

# 构建生产环境
yarn run build:prod
```

## 其它

```bash
# 预览发布环境效果
yarn run preview

# 预览发布环境效果 + 静态资源分析
yarn run preview -- --report

# 代码格式检查
yarn run lint

# 代码格式检查并自动修复
yarn run lint -- --fix
```

## 浏览器支持

现代浏览器和Internet Explorer 10+。

| IE / Edge | Firefox | Chrome | Safari |
| --------- | --------- | --------- | --------- |
| IE10, IE11, Edge| last 2 versions| last 2 versions| last 2 versions


## 客户定制
根据域名判断显示不同的title，logo

修改的文件包含

src/view/login/index.vue

src/utils/get-page-title.js

src/layout/components/Navbar.vue

node_modules/@robu/menu-show/src/components/Logo.vue

```
import defaultLogo from '@/assets/logo.png'
import sfLogo from '@/assets/logo-sf.png'

data() {
    return {
      title: defaultSettings.title || '',
      logo: defaultLogo,
    }
  },
created() {
    const { hostname } = window.location
    if (hostname === 'qinyi.yiluyun.robusoft.cn') {
        this.title = '翌路巡'
        this.logo = sfLogo
    } else {
        this.title = '智能巡检'
        this.logo = defaultLogo
    }
}
```

