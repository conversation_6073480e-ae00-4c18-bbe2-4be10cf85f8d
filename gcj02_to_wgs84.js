/**
 * GCJ-02坐标系统转换为WGS84(GPS)坐标系统
 * 参考：https://github.com/googollee/eviltransform
 */

// 地球长半轴
const a = 6378245.0;
// 扁率
const ee = 0.00669342162296594323;

/**
 * 判断坐标是否在中国境内
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {boolean} - 是否在中国
 */
function outOfChina(lng, lat) {
  return (lng < 72.004 || lng > 137.8347) || (lat < 0.8293 || lat > 55.8271);
}

/**
 * 计算经度偏移
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {number} - 偏移量
 */
function transformLng(lng, lat) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
  return ret;
}

/**
 * 计算纬度偏移
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {number} - 偏移量
 */
function transformLat(lng, lat) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

/**
 * GCJ-02坐标转换为WGS84坐标
 * @param {number} lng - GCJ-02经度
 * @param {number} lat - GCJ-02纬度
 * @returns {Object} - WGS84坐标对象 {lng, lat}
 */
function gcj02ToWgs84(lng, lat) {
  if (outOfChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dLat = transformLat(lng - 105.0, lat - 35.0);
  let dLng = transformLng(lng - 105.0, lat - 35.0);
  
  const radLat = lat / 180.0 * Math.PI;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  
  const sqrtMagic = Math.sqrt(magic);
  dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI);
  dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI);
  
  const mgLat = lat + dLat;
  const mgLng = lng + dLng;
  
  return {
    lng: lng * 2 - mgLng,
    lat: lat * 2 - mgLat
  };
}

/**
 * 格式化坐标输出
 * @param {number} value - 坐标值
 * @returns {string} - 格式化后的坐标值
 */
function formatCoord(value) {
  return value.toFixed(8);
}

/**
 * 转换并格式化输出坐标
 * @param {number} lng - GCJ-02经度
 * @param {number} lat - GCJ-02纬度
 * @returns {string} - 格式化后的WGS84坐标
 */
function convertAndFormat(lng, lat) {
  const wgs84 = gcj02ToWgs84(lng, lat);
  return `经纬度: ${formatCoord(lng)}, ${formatCoord(lat)} (GCJ-02坐标) => ${formatCoord(wgs84.lng)}, ${formatCoord(wgs84.lat)} (WGS-84坐标)`;
}

// 导出函数
module.exports = {
  gcj02ToWgs84,
  convertAndFormat
}; 