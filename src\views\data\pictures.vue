<!-- eslint-disable no-await-in-loop -->
<template>
  <div class="app-container" style="height: 100%">
    <div class="flex jcsb" style="margin-bottom:30px; height: 36px">
      <span class="flex" style="align-items: center">
        <el-radio-group
          v-model="radioType"
          style="margin-left: 10px; margin-top: -1px;"
          @change="handleRadioChange($event, 'left')"
        >
          <el-radio-button :label="1">去重图片</el-radio-button>
          <el-radio-button :label="2">全量图片</el-radio-button>
          <el-radio-button :label="3">标注图片</el-radio-button>
          <el-radio-button :label="5">抓拍图集</el-radio-button>
          <!-- <el-radio-button :label="3">gps纠错</el-radio-button> -->
        </el-radio-group>

        <template v-if="radioType === 1 || radioType === 3">
          <el-select
            v-model="modelIdentifyType"
            style="width: 132px; margin-left: 10px;"
            :clearable="modelIdentifyType != 'all'"
            @change="handelIdentifyTypeChange"
            @clear="handelIdentifyTypeClear"
          >
            <el-option label="全部" value="all" />
            <el-option
              v-if="roadType == 9"
              label="慢行病害"
              value="9"
            />
            <el-option
              v-else
              :label="roadLevel === 6 ? '城市道路病害' : '路面病害'"
              value="1"
            />
            <el-option
              v-if="roadType == 9"
              label="路面病害"
              value="1"
            />
            
            <template v-if="roadType != 10">
              <!-- "城市管理问题" 隐藏 -->
              <el-option
                label="道路资产"
                value="2"
              />
              <el-option
                label="路面风险"
                value="3"
              />
              <el-option
                label="沿线设施损坏"
                value="5"
              />
            </template>
            <template v-if="roadType == 1">
              <el-option
                label="慢行病害"
                value="9"
              />
              <el-option
                label="城市管理问题"
                value="10"
              />
            </template>
            <el-option
              v-if="roadType == 10 || roadType == 9"
              label="城市管理问题"
              value="10"
            />
          </el-select>
          <!-- 病害类型 -->
          <el-select
            v-if="showDiseaseTypes"
            v-model="diseaseType"
            placeholder="请选择病害类型"
            :clearable="diseaseType !== 'all'"
            style="width: 132px; margin-left: 10px;"
            @change="handelDiseasesChange"
            @clear="handelDiseasesClear"
          >
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in diseaseTypeOptions"
              :key="item.engName"
              :label="item.chineseName"
              :value="item.engName"
            />
          </el-select>
        </template>

        <el-select
          v-if="radioType === 2"
          v-model="selectRoadType"
          style="width: 132px; margin-left: 10px;"
          @change="handelroadTypeChange"
        >
          <el-option
            label="全部"
            value="all"
          />
          <el-option
            label="沥青"
            :value="0"
          />
          <el-option
            label="水泥"
            :value="1"
          />
          <el-option
            label="砂石"
            :value="2"
          />
        </el-select>
        <!-- 边界 判断条件：1. 不是抓拍图集 2. 有导出权限  -->
        <span v-if="radioType !== 5 && (allowDownloadPhoto || $auths(['*:*:*', 'inspect-tasks-batch-download-ai:view:inspect-tasks-batch-download-ai']))" class="boundary" />

        <el-button
          v-if="allowDownloadPhoto && radioType !== 3 && radioType !== 5"
          icon="el-icon-download"
          :title="allowDownloadPhoto ? '' : '无道路图集导出权限'"
          @click="handleExport"
        >导出</el-button>

        <el-button
          v-if="radioType !== 2 && radioType !== 5 && $auths(['*:*:*', 'inspect-tasks-batch-download-ai:view:inspect-tasks-batch-download-ai'])"
          icon="el-icon-download"
          @click="handleExportAI"
        >导出AI训练图集</el-button>

        <!-- <el-button
          v-if="admin && radioType !== 2"
          icon="el-icon-download"
          @click="handleExportAILabel"
        >导出AI标注文件</el-button> -->
      </span>
      <!-- <el-button v-if="taskId == 603"  style="float: right" @click="handleGLR">
        绿视率
      </el-button>-->

      <span>
        <el-radio-group
          v-if="radioType === 1 || radioType === 4"
          v-model="radioType"
          style="margin: -1px 10px 0 10px;"
          @change="handleRadioChange($event, 'right')"
        >
          <el-radio-button :label="1">图片</el-radio-button>
          <el-radio-button :label="4">轨迹</el-radio-button>
        </el-radio-group>
      </span>
    </div>
    <canvas id="canvas" width="640" height="360" style="display: none" />
    <el-row v-show="leftRadioTypeList.includes(radioType)" v-loading="loading" class="picture-row" :gutter="25">
      <template v-if="pictures.length > 0">
        <el-col v-for="(picture, index) in pictures" :key="index" :span="25">
          <!-- @contextmenu.prevent="handleOpenMenu($event)" -->
          <template v-if="picture">
            <div
              class="picture-container"
              title="点击查看大图"
              @click="handleShowDialog(picture, index)"
              @contextmenu.prevent="handleOpenMenu($event, picture)"
            >
              <img v-if="picture.picState === 1" :src="picture.imgSrc">
              <div v-if="picture.picState === 2" class="empty-div">图片已归档，请联系管理员解冻</div>
              <div
                v-if="picture.picState === 3 || picture.picState === null"
                class="empty-div"
              >
                您查看的图片时间点是：{{ picture.photoTime ? picture.photoTime.replace('T', ' ') : '未知' }}，当前图片续传到达时间：{{ picture.deviceState.schedulerFrameFileTimestamp ? picture.deviceState.schedulerFrameFileTimestamp.replace('T', ' ') : '未知' }}，还有{{ picture.deviceState.frameFileFailureNums || '未知' }}张图片尚未续传成功，请等待或联系管理员进一步查看。
              </div>
            </div>
            <div class="desc">
              <template v-if="radioType === 1 || radioType === 3">{{ Math.round(picture.distance * 100) / 100 }}m &nbsp;&nbsp; &nbsp;</template>
              <!-- 抓拍图集 -->
              <div v-if="radioType === 5" class="capture-info">
                <el-tooltip v-model="tooltipVisible[index]" class="capture-describe" effect="dark" :content="picture.describe" placement="top" :manual="true">
                  <span class="text-ellipsis" @mouseenter="checkOverflow($event, index)" @mouseleave="tooltipVisible[index] = false"> {{ picture.describe }}</span>
                </el-tooltip>
                <span class="capture-time">
                  {{ picture.photoTime.replace('T', ' ') }}</span>
              </div>
              <template v-if="radioType !== 5">
                {{ picture.damageCount }}个病害
              </template>
              <template v-if="radioType === 2 || radioType === 4 ">
                &nbsp;&nbsp; &nbsp; {{ picture.photoTime.replace('T', ' ') }}
              </template>
            </div>
          </template>
        </el-col>
      </template>
      <div v-if="!loading && pictures.length === 0" style="color: #9b9b9b;margin-top: 50px;text-align:center">暂无数据</div>
      <!-- 右击弹框设置 -->
      <ul v-if="menuVisible" :style="{ left: menuL + 'px', top: menuT + 'px' }" class="contextmenu">
        <li v-auths="['*:*:*', 'task-photo-area-calc:view:task-photo-area-calc']" @click="$refs.calibrateDailog.show(tempPictureData)">面积计算标定</li>
        <template v-auth="'*:*:*'">
          <li @click="$refs.compareDialog.show(tempPictureData)">模型识别对比</li>
        </template>
      </ul>
    </el-row>

    <div v-if="radioType === 4" class="track-box">
      <Track :gps-point-list="gpsPointList" :page="listQuery.currentPage" />
    </div>

    <span class="flex jcsb">
      <Pagination
        v-if="total > 0"
        :total="total"
        :page.sync="listQuery.currentPage"
        :limit.sync="listQuery.pageSize"
        layout="total, prev, pager, next, jumper"
        @pagination="handlePaginationChange"
      />
    </span>
    <CanvasDialog
      ref="canvasDialog"
      :annotate="annotate"
      :download="true"
      @updateList="getList"
      @updateSingle="updateSingleData"
      @onPrev="handlePrev"
      @onNext="handleNext"
      @onClose="handleCloseCanvasDialog"
    />
    <GreenDialog ref="greenDialog" />
    <calibrateDialog ref="calibrateDailog" :device-key="deviceKey" />
    <necCalibrateDialog ref="necCalibrateDialog" :device-key="deviceKey" />
    <compareDialog ref="compareDialog" :device-key="deviceKey" />
  </div>
</template>

<script>

import { getPicturesList, getRoadPictures, getPicturesListByCapture } from '@/api/data'
import { getModels } from '@/api/model'
import Pagination from '@/components/Pagination/index.vue'
import CanvasMixin from '@/mixin/canvas'
import { exportFile } from '@/utils/index'
import { mapState } from 'vuex'
import { RequestCancelManager } from '@/utils/request'
import CanvasDialog from './components/canvas-dialog.vue'
import GreenDialog from './components/green-dialog.vue'
import CalibrateDialog from './components/calibrate-dialog.vue'
import CompareDialog from './components/compare-dialog.vue'
import Track from './components/track.vue'
import necCalibrateDialog from './components/nec-calibrate-dialog.vue'
import LRUCache from './utils/LRUCache'

export default {
  props: {
    dataSource: {
      type: Number,
      default: 1,
    },
  },
  name: 'DataPictures',
  components: {
    Pagination,
    CanvasDialog,
    GreenDialog,
    CalibrateDialog,
    CompareDialog,
    necCalibrateDialog,
    Track,
  },
  mixins: [CanvasMixin],
  data() {
    return {
      taskId: null,
      deviceKey: null,
      startTime: null,
      endTime: null,
      roadType: null,
      roadLevel: null,
      total: 0,
      listQuery: {
        currentPage: 1,
        pageSize: 15,
      },
      pictures: [],
      picturesCache: new LRUCache(5), // 限制缓存10页数据
      curPictures: [], // 当前页数据--用于标注页显示
      loading: true,
      canvas: null,
      ctx: null,
      dialogImgIndex: null,
      radioType: 1,
      leftRadioTypeList: [1, 2, 3, 5],
      modelsList: [],
      menuVisible: false, // 控制菜单栏的隐藏与展示
      menuT: 0, // 定义菜单栏出现的位置
      menuL: 0, // 定义菜单栏出现的位置
      tempPictureData: {},
      gpsPointList: [], // 当前页面图片的轨迹点
      modelIdentifyType: 'all',
      diseaseType: 'all',
      selectRoadType: 'all',
      loadingCache: new Set(), // 记录正在加载中的页码,
      dialogOpenPage: null, // 记录打开弹窗时的页码
      pictureRequestId: null, // 存储图片列表请求ID
      imageLoadRequestId: null, // 存储图片加载请求ID
      tooltipVisible: {}, // 控制每个tooltip是否显示
      currentOperationId: null, // 添加操作ID跟踪
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      userType: (state) => state.account.userType,
      allowDownloadPhoto: (state) => state.account.allowDownloadPhoto,
      username: (state) => state.account.username,
      workUnit: (state) => state.account.workUnit,

    }),
    showDiseaseTypes() {
      return (this.radioType === 1 || this.radioType === 3) && this.modelIdentifyType !== 'all'
    },
    annotate() {
      return this.$auths(['*:*:*', 'task-photo-manual-labeling:view:task-photo-manual-labeling']) && this.radioType === 1
    },
    diseaseTypeOptions() {
      if (this.modelIdentifyType === '1') {
        return [...this.allDiseaseType]
      }
      if (this.modelIdentifyType === '2') {
        return [...this.allRoadAssetsType]
      }
      if (this.modelIdentifyType === '3') {
        return [...this.allRoadForeignMatter]
      }
      if (this.modelIdentifyType === '5') {
        return [...this.allAlongLine]
      }
      if (this.modelIdentifyType === '9') {
        return [...this.slowPatrolType]
      }
      if (this.modelIdentifyType === '10') {
        // console.log('cityManagementType', this.cityManagementType)
        return [...this.cityManagementType]
      }
      return []
    },
  },
  watch: {
    //   监听属性对象，newValue为新的值，也就是改变后的值
    menuVisible(newValue, oldValue) {
      if (newValue) {
        // 菜单显示的时候
        // document.body.addEventListener，document.body.removeEventListener它们都接受3个参数
        // ("事件名" , "事件处理函数" , "布尔值");
        // 在body上添加事件处理程序
        document.body.addEventListener('click', this.handleCloseMenu)
      } else {
        // 菜单隐藏的时候
        // 移除body上添加的事件处理程序
        document.body.removeEventListener('click', this.handleCloseMenu)
      }
    },
    async dialogImgIndex(newValue, oldValue) {
      const { currentPage, pageSize } = this.listQuery

      const params = {
        page: currentPage - 1,
        size: pageSize,
        taskId: this.taskId,
      }

      if (this.diseaseType !== 'all') params.type = this.diseaseType
      if (this.modelIdentifyType !== 'all') params.modelIdentifyType = this.modelIdentifyType
      if (this.selectRoadType !== 'all') params.roadType = this.selectRoadType
      
      // PicturesCache
      const positionRatio = newValue / pageSize // 当前查看的图片在当前页面中的位置比例
      const threshold = 0.4 // 阈值设为0.3

      // 判断是否需要预加载下一页:
      // 1. 当前图片位置超过设定阈值(已浏览70%的内容)
      // 2. 当前不是最后一页
      if (positionRatio >= (1 - threshold) && currentPage < Math.ceil(this.total / pageSize)) {
        const nextPage = params.page + 1
        // 检查是否已经在加载中或已有缓存
        if (!this.picturesCache.has(nextPage) && !this.loadingCache.has(nextPage)) {
          try {
            this.loadingCache.add(nextPage) // 标记为加载中
            params.page = nextPage
            const res = await getPicturesList(params)
            this.picturesCache.set(nextPage, res.payload.content)
          } finally {
            this.loadingCache.delete(nextPage) // 无论成功失败都移除加载状态
          }
        }
      }

      // 判断是否需要预加载上一页的判断条件:
      // 1. positionRatio <= threshold: 用户正在浏览当前页前30%的图片
      // 2. currentPage > 1: 当前不是第一页
      if (positionRatio <= threshold && currentPage > 1) {
        // this.picturesCache.set(params.page - 1, '上一页')
        const prevPage = params.page - 1
        // 检查是否已经在加载中或已有缓存
        if (!this.picturesCache.has(prevPage) && !this.loadingCache.has(prevPage)) {
          try {
            this.loadingCache.add(prevPage) // 标记为加载中
            params.page = prevPage
            const res = await getPicturesList(params)
            this.picturesCache.set(prevPage, res.payload.content)
          } finally {
            this.loadingCache.delete(prevPage) // 无论成功失败都移除加载状态
          }
        }
      }
    },
    pictures() {
      // 图片列表更新时，重置tooltip状态
      this.tooltipVisible = {}
    },
  },
  async activated() {
    // console.log(this.$store.state.menu.routes);
    // console.log(this.$route)
    const picLocateInfo = JSON.parse(sessionStorage.getItem('picLocateInfo'))
    
    if (this.taskId !== this.$route.query.id) {
      this.picturesCache.clear() // 清除人工标注页面缓存
      sessionStorage.removeItem('ModelVersionValue') // 删除《模型对比》模型版本value的缓存
      this.modelIdentifyType = 'all'
      this.diseaseType = 'all'
      this.radioType = 1
      this.total = 0
      this.dialogImgIndex = 0
      this.listQuery.currentPage = picLocateInfo
        ? Number(picLocateInfo.page + 1)
        : 1
      const {
        id, key, startTime, endTime, roadType, roadLevel,
      } = this.$route.query
      this.taskId = id
      this.deviceKey = key
      this.startTime = startTime
      this.endTime = endTime
      if (this.roadType && this.roadType !== roadType) this.requested = false
      this.roadType = roadType
      this.roadLevel = Number(roadLevel)

      // 城市道路病害（roadType = 1 且 roadLevel = 6）需特殊处理，其病害筛选条件为 road_type=6且classification=1
      this.getModelIdentifyTypes(
        this.roadLevel === 6 ? 6
          : roadType == 10 ? 1 : roadType,
      )
      this.getModelsAjax()
      await this.getList()
      if (picLocateInfo) {
        const locateIndex = picLocateInfo.offset
        const locatePic = this.pictures[locateIndex]

        this.handleShowDialog(locatePic, locateIndex)
        sessionStorage.removeItem('picLocateInfo')
      }
    } else if (picLocateInfo) {
      this.total = 0
      this.listQuery.currentPage = Number(picLocateInfo.page + 1)
      await this.getList()
      const locateIndex = picLocateInfo.offset
      const locatePic = this.pictures[locateIndex]
      this.handleShowDialog(locatePic, locateIndex)
      sessionStorage.removeItem('picLocateInfo')
    }
  },
  methods: {
    async handlePaginationChange() {
      // 单独取消请求，替代批量取消
      this.cancelCurrentRequests()

      const currentPage = this.listQuery.currentPage - 1

      if (this.picturesCache.has(currentPage)) {
        this.pictures = this.picturesCache.get(currentPage)
        this.loadingImg({
          content: this.pictures,
          totalElements: this.total,
        })
      } else {
        await this.getList()
      }

      this.dialogImgIndex = null
      this.curPictures = []
      this.dialogOpenPage = null
    },
    async getList() {
      try {
        this.pictures = []

        this.loading = true
        const { currentPage, pageSize } = this.listQuery

        const params = {
          page: currentPage - 1, // 转换为从0开始的页码
          size: pageSize,
          taskId: this.taskId,
        }

        const currentPageIndex = params.page
        if (this.picturesCache.has(currentPageIndex)) {
          // 使用缓存数据
          const cachedData = this.picturesCache.get(currentPageIndex)

          this.loadingImg({ content: cachedData, totalElements: this.total })
          return
        }

        // 没有缓存则请求新数据
        if (!this.loadingCache.has(currentPageIndex)) {
          try {
            this.loadingCache.add(currentPageIndex)
            const res = await this.getPictures(params)
            this.loadingImg(res.payload)
            this.picturesCache.set(currentPageIndex, res.payload.content)
          } catch (error) {
            if (RequestCancelManager.isCancel(error)) {
              // console.log('图片列表请求被取消')
            } else {
              console.error('获取图片列表失败', error)
            }
            this.loading = false
          } finally {
            this.loadingCache.delete(currentPageIndex)
          }
        }
      } catch (error) {
        console.error('getList错误:', error)
        this.loading = false
      }
    },

    async getPictures(params) {
      // 如果有正在进行的请求，先取消
      if (this.pictureRequestId) {
        RequestCancelManager.cancel(this.pictureRequestId, '取消请求')
        this.pictureRequestId = null // 立即清除ID
      }

      // 修复requestId格式，并添加时间戳确保唯一性
      const requestId = `getPictures_${this.radioType}_${Date.now()}`
      // 创建新的取消令牌
      const token = RequestCancelManager.create(requestId)
      this.pictureRequestId = requestId

      let res = {}
      try {
        if (this.radioType === 1) {
          // 去重图片
          if (this.modelIdentifyType && this.modelIdentifyType !== 'all') params.modelIdentifyType = this.modelIdentifyType
          if (this.diseaseType && this.diseaseType !== 'all') params.type = this.diseaseType

          res = await getPicturesList({ ...params, cancelToken: token })
        } else if (this.radioType === 2) {
          // 全量图片
          delete params.taskId
          params.deviceId = this.deviceKey
          params.startTime = this.startTime
          params.endTime = this.endTime
          if (this.selectRoadType !== 'all') params.roadType = this.selectRoadType

          res = await getRoadPictures({ ...params, cancelToken: token })
        } else if (this.radioType === 3) {
          // 标注图片
          if (this.modelIdentifyType && this.modelIdentifyType !== 'all') params.modelIdentifyType = this.modelIdentifyType
          if (this.diseaseType && this.diseaseType !== 'all') params.type = this.diseaseType
          params.existManualDamage = true
          res = await getPicturesList({ ...params, cancelToken: token })
        } else if (this.radioType === 5) {
          // 抓拍图片
          // console.log('调用抓拍图集接口', params)
          res = await getPicturesListByCapture({ ...params, cancelToken: token })
          if (res.payload && res.payload.content) {
            res.payload.content = res.payload.content.map((item) => ({
              ...item,
              picState: 1,
              pictureUrl: item.objectStorageUrlPrefix + item.originalImagePath,
              distance: 0,
              damages: [],
              coordinates: [],
              damageCount: 0,
            }))
          }
        }
        return res // 只在成功时返回
      } catch (error) {
        if (RequestCancelManager.isCancel(error)) {
          console.log('请求被取消:', error.message)
          throw error // 重新抛出取消异常，确保调用方知道请求被取消
        }
        console.error('接口错误', error)
        throw error // 其他错误也应抛出，保持一致性
      } finally {
        // 请求完成后清除ID
        if (this.pictureRequestId === requestId) {
          this.pictureRequestId = null
        }
      }
    },
    async loadImagesWithAbort(pictures) {
      // 取消之前的请求
      if (this.imageLoadRequestId) {
        RequestCancelManager.cancel(this.imageLoadRequestId, '新的图片加载请求开始')
        this.imageLoadRequestId = null
      }

      // 生成新的操作ID
      const operationId = Date.now().toString()
      this.currentOperationId = operationId

      // 创建新的请求ID和取消令牌
      const requestId = `loadImages_${operationId}`
      const token = RequestCancelManager.create(requestId)
      this.imageLoadRequestId = requestId

      console.log(`开始加载图片，操作ID: ${operationId}`)

      try {
        for (let i = 0; i < pictures.length; i++) {
          const picture = pictures[i]
          if (!picture || picture.picState !== 1) continue

          // 检查操作是否仍有效
          if (this.currentOperationId !== operationId) {
            console.log(`操作 ${operationId} 已过期，中止处理`)
            break
          }

          const { damages, coordinates } = picture

          if ((damages && damages.length > 0) || (coordinates && coordinates.length > 0)) {
            try {
              // 加载图片
              const image = await this.loadImageWithToken(picture.pictureUrl, token, operationId)

              // 再次检查操作有效性
              if (this.currentOperationId !== operationId) {
                console.log(`图片 ${i} 加载完成，但操作已过期`)
                continue
              }

              // 绘制图片和标注
              this.getCtx(image, 'canvas', image.width, image.height)
              if (!this.canvas || !this.ctx) continue

              this.ctx.drawImage(image, 0, 0, image.width, image.height)

              if (this.radioType === 1 || this.radioType === 3 && damages?.length > 0) {
                damages.forEach((item) => this.ctxDraw(item.type, item.coordinate))
              } else if (this.radioType === 2 && coordinates?.length > 0) {
                coordinates.forEach((item) => this.ctxDraw(item.type, item.coordinate))
              }

              const imgSrc = this.canvas.toDataURL()

              // 更新前再次检查操作有效性
              if (this.currentOperationId === operationId) {
                this.$set(this.pictures, i, { ...picture, imgSrc })
              }
            } catch (error) {
              // 处理错误
              if (error.name === 'AbortError') {
                console.log(`图片 ${i} 加载被取消 ${operationId}`)
                // 检查是否因为操作过期而被取消
                if (this.currentOperationId !== operationId) {
                  break // 如果操作已过期，中止整个循环
                }
              } else {
                console.error(`加载图片 ${i} 出错:`, error)
              }
            }
          } else {
            // 无需处理的图片
            if (this.currentOperationId === operationId) {
              this.$set(this.pictures, i, { ...picture, imgSrc: picture.pictureUrl })
            }
          }
        }
      } finally {
        // 清理请求ID
        if (this.imageLoadRequestId === requestId) {
          this.imageLoadRequestId = null
        }
      }
    },
    loadingImg(payload) {
      // 生成新的操作ID
      const operationId = Date.now().toString()
      this.currentOperationId = operationId

      this.total = payload?.totalElements
      const content = [...payload.content]
      this.pictures = content
      this.loading = false
      this.gpsPointList = []

      // 处理GPS点
      content.forEach((item, i) => {
        const {
          gpsLatitude, gpsLongitude, distance, damages, coordinates,
        } = item

        this.gpsPointList.push({
          id: i,
          lng: gpsLongitude,
          lat: gpsLatitude,
          distance: distance ?? 0,
          coordinates: damages ? damages.length : coordinates ? coordinates.length : 0,
        })
      })

      // 加载图片
      this.loadImagesWithAbort(content)
    },
    async handelDiseasesChange(val) {
      this.listQuery.currentPage = 1
      this.picturesCache.clear()
      // console.log('handelDiseasesChange', this.picturesCache)
      this.getList()
    },
    handelDiseasesClear(val) {
      this.diseaseType = 'all'
      this.picturesCache.clear()
      // console.log('handelDiseasesClear', this.picturesCache)
      this.getList()
    },
    async handleRadioChange(val, type = 'left') {
      console.log(`切换类型: ${val}`)

      // 生成新的操作ID，使旧操作无效
      this.currentOperationId = Date.now().toString()

      // 取消当前请求
      this.cancelCurrentRequests()

      // 等待取消操作完成
      await new Promise((resolve) => setTimeout(resolve, 20))

      if (val === 1 || val === 3) {
        this.modelIdentifyType = 'all'
        this.diseaseType = 'all'
      }

      if (type === 'left') {
        this.selectRoadType = 'all'
        this.listQuery.currentPage = 1
        this.picturesCache.clear()
        await this.getList()
      }
    },
    handelIdentifyTypeChange(val) {
      this.picturesCache.clear()
      this.diseaseType = 'all'
      this.listQuery.currentPage = 1
      this.getList()
    },
    handelIdentifyTypeClear(val) {
      this.picturesCache.clear()
      // console.log('handelIdentifyTypeClear', this.picturesCache)
      this.modelIdentifyType = 'all'
      this.getList()
    },
    handelroadTypeChange(val) {
      this.picturesCache.clear()
      // console.log('handelroadTypeChange', this.picturesCache)
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleExport() {
      if (this.radioType === 1) {
        let url = ''
        if (this.modelIdentifyType === 'all') {
          url = `task-photo-remove-dups/batchDownload?taskId=${this.taskId}`
        } else if (this.diseaseType === 'all') url = `task-photo-remove-dups/batchDownload?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}`
        else url = `task-photo-remove-dups/batchDownload?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}&type=${this.diseaseType}`

        exportFile(url)
      } else {
        exportFile(`image-infers/batchDownload?taskId=${this.taskId}`)
      }
    },
    handleExportAI() {
      let url = ''
      if (this.radioType === 3) {
        if (this.modelIdentifyType === 'all') {
          url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}&existManualDamage=true`
        } else if (this.diseaseType === 'all') url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}&existManualDamage=true&modelIdentifyType=${this.modelIdentifyType}`
        else url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}&existManualDamage=true&modelIdentifyType=${this.modelIdentifyType}&type=${this.diseaseType}`
      } else if (this.modelIdentifyType === 'all') {
        url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}`
      } else if (this.diseaseType === 'all') url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}`
      else url = `task-photo-remove-dups/batchDownloadAI?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}&type=${this.diseaseType}`

      exportFile(url)
    },
    handleShowDialog(picture, index) {
      console.log('picture', picture)
      this.dialogImgIndex = index

      console.log('点击打开图片框--handleShowDialog', picture)
      this.curPictures = [...this.pictures]
      this.$refs.canvasDialog.show(
        picture,
        index,
        this.radioType,
        this.roadType,
        this.roadLevel,
      )
    },
    async handlePrev() {
      // 1. 如果是当前页的第一张图片
      if (this.dialogImgIndex === 0) {
        if (this.listQuery.currentPage === 1) {
          this.$message.closeAll()
          this.$message({
            message: '已经是第一张图片了',
            type: 'warning',
          })
          return
        }

        // 先获取上一页数据,避免切换时的卡顿
        const prevPage = this.listQuery.currentPage - 2 // 当前页的上一页(从0开始)
        let prevPageData = null

        if (this.picturesCache.has(prevPage)) {
          prevPageData = this.picturesCache.get(prevPage)
        } else {
          const params = {
            page: prevPage,
            size: this.listQuery.pageSize,
            taskId: this.taskId,
          }
          const res = await getPicturesList(params)
          prevPageData = res.payload.content
          this.picturesCache.set(prevPage, prevPageData)
        }

        // 数据准备好后再保存当前标注
        this.$refs.canvasDialog.handleSubmit()
        // 重置标注状态
        this.$refs.canvasDialog.resetAnnotationState()

        // 更新数据和显示
        this.listQuery.currentPage -= 1
        this.pictures = prevPageData
        this.curPictures = [...prevPageData]

        // 设置为上一页的最后一张并显示
        this.dialogImgIndex = this.pictures.length - 1
        const data = this.pictures[this.dialogImgIndex]
        this.$refs.canvasDialog.show(
          data,
          this.dialogImgIndex,
          this.radioType,
          this.roadType,
          this.roadLevel,
        )
      } else {
        // 同页切换保持不变
        this.$refs.canvasDialog.handleSubmit()
        // 重置标注状态
        this.$refs.canvasDialog.resetAnnotationState()
        this.dialogImgIndex -= 1
        const data = this.curPictures[this.dialogImgIndex]
        this.$refs.canvasDialog.show(
          data,
          this.dialogImgIndex,
          this.radioType,
          this.roadType,
          this.roadLevel,
        )
      }
    },
    async handleNext() {
      // 1. 如果是当前页的最后一张图片
      if (this.dialogImgIndex === this.pictures.length - 1) { // 使用实际图片数量判断
        const totalPage = Math.ceil(this.total / this.listQuery.pageSize)

        if (this.listQuery.currentPage === totalPage) {
          this.$message.closeAll()
          this.$message({
            message: '已经是最后一张图片了',
            type: 'warning',
          })
          return
        }

        // 先获取下一页数据,避免切换时的卡顿
        const nextPage = this.listQuery.currentPage
        let nextPageData = null

        if (this.picturesCache.has(nextPage)) {
          nextPageData = this.picturesCache.get(nextPage)
        } else {
          const params = {
            page: nextPage,
            size: this.listQuery.pageSize,
            taskId: this.taskId,
          }
          const res = await getPicturesList(params)
          nextPageData = res.payload.content
          this.picturesCache.set(nextPage, nextPageData)
        }

        // 数据准备好后再保存当前标注
        this.$refs.canvasDialog.handleSubmit()
        this.$refs.canvasDialog.resetAnnotationState()

        // 更新数据和显示
        this.listQuery.currentPage += 1
        this.pictures = nextPageData
        this.curPictures = [...nextPageData]

        // 重置图片索引并显示
        this.dialogImgIndex = 0
        const data = this.pictures[0]
        this.$refs.canvasDialog.show(
          data,
          this.dialogImgIndex,
          this.radioType,
          this.roadType,
          this.roadLevel,
        )
      } else {
        // 同页切换保持不变
        this.$refs.canvasDialog.handleSubmit()
        this.$refs.canvasDialog.resetAnnotationState()
        this.dialogImgIndex += 1
        const data = this.curPictures[this.dialogImgIndex]
        this.$refs.canvasDialog.show(
          data,
          this.dialogImgIndex,
          this.radioType,
          this.roadType,
          this.roadLevel,
        )
      }
    },
    handleGLR() {
      this.$refs.greenDialog.show(this.taskId)
    },
    async getModelsAjax() {
      const { payload } = await getModels()
      this.modelsList = payload
    },
    handleCommand(command) {
      this.$refs.greenDialog.show(this.taskId, 1, command)
    },
    // 更新图片列表单条数据
    updateSingleData({ newData, i }) {
      const that = this
      const {
        pictureUrl, damages, coordinates, picState,
      } = newData
      if (picState === 1) {
        if (
          (damages && damages.length > 0)
          || (coordinates && coordinates.length > 0)
        ) {
          const image = new Image()
          image.src = pictureUrl // 异步执行，需加载才显示
          image.setAttribute('crossOrigin', 'anonymous')
          image.onload = () => {
            let imgSrc = ''
            that.getCtx(image, 'canvas', image.width, image.height)
            if (!that.canvas || !that.ctx) return

            that.ctx.drawImage(image, 0, 0, image.width, image.height)
            if (that.radioType === 1 && damages && damages.length > 0) {
              damages.forEach((item) => {
                that.ctxDraw(item.type, item.coordinate)
              })
            } else if (
              that.radioType === 2
              && coordinates
              && coordinates.length > 0
            ) {
              coordinates.forEach((item) => {
                that.ctxDraw(item.type, item.coordinate)
              })
            }

            imgSrc = that.canvas.toDataURL()
            newData.imgSrc = imgSrc

            that.$set(that.pictures, i, newData)
            that.$set(that.curPictures, i, newData)
          }
        } else {
          newData.imgSrc = pictureUrl
          that.$set(that.pictures, i, newData)
          that.$set(that.curPictures, i, newData)
        }
      } else {
        that.$set(that.pictures, i, newData)
        that.$set(that.curPictures, i, newData)
      }
    },

    handleOpenMenu(e, picture) {
      if (
        this.$auths(['*:*:*', 'task-photo-area-calc:view:task-photo-area-calc']) && picture.picState === 1 && this.radioType === 1
      ) {
        // 设置菜单栏出现的位置
        const x = e.clientX
        const y = e.clientY

        this.menuT = y
        this.menuL = x

        // 右击打开了菜单栏
        this.menuVisible = true
        this.tempPictureData = { roadType: parseInt(this.roadType), ...picture }
      }
    },
    // 菜单栏关闭执行回调
    handleCloseMenu() {
      this.menuVisible = false
    },
    handleExportAILabel() {
      let url = ''
      if (this.radioType === 3) {
        if (this.modelIdentifyType === 'all') {
          url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}&existManualDamage=true`
        } else if (this.diseaseType === 'all') url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}&existManualDamage=true&modelIdentifyType=${this.modelIdentifyType}`
        else url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}&existManualDamage=true&modelIdentifyType=${this.modelIdentifyType}&type=${this.diseaseType}`
      } else if (this.modelIdentifyType === 'all') {
        url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}`
      } else if (this.diseaseType === 'all') url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}`
      else url = `task-photo-remove-dups/downloadXmlZip?taskId=${this.taskId}&modelIdentifyType=${this.modelIdentifyType}&type=${this.diseaseType}`
      exportFile(url)
    },
    handleCloseCanvasDialog() {
      // 只有当页码改变时才需要更新列表
      if (this.dialogOpenPage !== this.listQuery.currentPage) {
        this.loadingImg({ content: this.curPictures, totalElements: this.total })
      }
      this.dialogImgIndex = null
      this.curPictures = []
      this.dialogOpenPage = null
    },
    beforeDestroy() {
      // 替换RequestCancelManager.cancelGroup为单独取消
      if (this.pictureRequestId) {
        RequestCancelManager.cancel(this.pictureRequestId, '组件销毁')
      }

      if (this.imageLoadRequestId) {
        RequestCancelManager.cancel(this.imageLoadRequestId, '组件销毁')
      }
    },
    // 图片加载方法
    loadImageWithToken(url, token, operationId = null) {
      return new Promise((resolve, reject) => {
        const image = new Image()
        image.crossOrigin = 'anonymous'

        // 检查操作是否已过期
        if (operationId && this.currentOperationId !== operationId) {
          reject(new DOMException('Operation superseded', 'AbortError'))
          return
        }

        // 图片加载成功回调
        image.onload = () => {
          // 再次检查操作是否仍有效
          if (operationId && this.currentOperationId !== operationId) {
            reject(new DOMException('Operation superseded after load', 'AbortError'))
            return
          }
          resolve(image)
        }

        // 图片加载失败回调
        image.onerror = () => {
          reject(new DOMException('Image load failed', 'NetworkError'))
        }

        // 检查token状态
        if (token && token._source && token._source.isCanceled) {
          reject(new DOMException('Aborted', 'AbortError'))
          return
        }

        // 设置图片源
        image.src = url

        // 如果token被取消，中止图片加载
        if (token && token._source) {
          const originalCancel = token._source.cancel
          token._source.cancel = function (message) {
            image.src = '' // 取消图片加载
            originalCancel.call(token._source, message)
          }
        }
      })
    },
    cancelCurrentRequests() {
      // 取消图片请求
      if (this.imageLoadRequestId) {
        RequestCancelManager.cancel(this.imageLoadRequestId, '用户操作取消')
        this.imageLoadRequestId = null
      }

      // 取消API请求
      if (this.pictureRequestId) {
        RequestCancelManager.cancel(this.pictureRequestId, '用户操作取消')
        this.pictureRequestId = null
      }
    },
    // 检测元素是否溢出并控制tooltip显示
    checkOverflow(event, index) {
      const el = event.target
      // 溢出检测：当元素的内容宽度大于容器宽度时表示溢出
      const isOverflow = el.scrollWidth > el.clientWidth
      this.$set(this.tooltipVisible, index, isOverflow)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';
.picture-row {
  height: calc(100vh - 252px);
  overflow-y: auto;
  @include scrollBar;
}
::v-deep .el-col-25 {
  width: 20%;
  margin-bottom: 10px;
  // cursor: pointer;

  .picture-container {
    cursor: pointer;
    width: 100%;
  }

  img {
    display: block;
    width: 100%;
    aspect-ratio: 16/9;
    height: auto;
    object-fit: cover;
    border-radius: 6px;
  }
  img[src=''],
  img:not([src]) {
    opacity: 0;
  }

  .empty-div {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    aspect-ratio: 16/9;
    height: auto;
    border-radius: 6px;
    border: 1px solid #ccc;
    color: #9b9b9b;
    padding: 0 10px;
    word-break: break-all;
    font-size: 14px;
    line-height: 1.2;
  }

  .desc {
    padding: 12px 0;
    .capture-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .capture-describe {
        flex: 1;
        padding-right: 20px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .capture-time {
        min-width: 148px;
      }
    }
  }

  &:nth-of-type(12),
  &:nth-of-type(13),
  &:nth-of-type(14),
  &:nth-of-type(15) {
    margin-bottom: 0;
  }
}

.pagination-container[data-v-72233bcd] {
  padding: 18px 0 0;
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: fixed; //关键样式设置固定定位
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}
.contextmenu li:hover {
  background: #2d8cf0;
  color: #fff;
}
.flex {
  display: flex;
}
.jcsb {
  justify-content: space-between;
}

.track-box {
  height: calc(100vh - 200px);
  border-radius: 6px;
  overflow: hidden;
}
.boundary {
  display: inline-block;
  height: 36px;
  width: 3px;
  margin: 0 35px;
  background-color: #dcdfe6;
  border-radius: 2px;
}
</style>
