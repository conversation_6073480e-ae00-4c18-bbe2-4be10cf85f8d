/*
 * @Author: guowy
 * @Date: 2020-05-27 11:03:57
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-14 17:02:08
 */

import request from '@/utils/request'

export function getDevices(query) {
  return request({
    url: '/devices',
    method: 'get',
    params: query,
  })
}

export function getDevice(id) {
  return request({
    url: `/devices/${id}`,
    method: 'get',
  })
}

export function createDevice(data) {
  return request({
    url: '/devices',
    method: 'post',
    data,
  })
}

export function updateDevice(data) {
  return request({
    url: `/devices/${data.id}`,
    method: 'put',
    data,
  })
}

export function deleteDevice(id) {
  return request({
    url: `/devices/${id}`,
    method: 'delete',
  })
}

export function getDeviceConfig(id) {
  return request({
    url: `/devices/${id}/getParamConfig`,
    method: 'get',
  })
}

export function updateDeviceConfig(data) {
  return request({
    url: `/devices/${data.id}/updateParamConfig`,
    method: 'put',
    data,
  })
}
// 获取单位及关联设备名称选项列表
export function getDeviceWorkUnits() {
  return request({
    url: `/devices/queryWorkUnits`,
    method: 'get',
  })
}

// 获取所有设备名称选项列表
export function getDeviceNames() {
  return request({
    url: `/devices/queryDeviceNames`,
    method: 'get',
  })
}

export function getUsersWorkUnits() {
  return request({
    url: `/users/queryWorkUnits`,
    method: 'get',
  })
}
export function getDeviceDataInfo(query) {
  return request({
    url: `/device-states/findByDeviceKey`,
    method: 'get',
    params: query,
  })
}

// 查询设备未上传数据的15天内变化列表
export function getChangedFrameNums(query) {
  return request({
    url: `/image-infers/queryChangedFrameNums`,
    method: 'get',
    params: query,
    timeout: undefined,
  })
}
