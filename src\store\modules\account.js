/*
 * @Author: wangyj
 * @Date: 2022-07-07 18:11:38
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-12-06 13:33:42
 */
import { getCurrentUserInfo } from '@/api/user'

const state = {
  username: '',
  admin: false,
  userType: 1,
  workUnit: '',
  workUnitId: '',
  homeNum: 0,
  allowDownloadPhoto: false,
  owningRegion: '',
  plyPaths: null,
  plys: [],
  permissions: [],
}

const mutations = {
  SET_USERNAME: (state, value) => {
    state.username = value
  },
  SET_ADMIN: (state, value) => {
    state.admin = value
  },
  SET_USERTYPE: (state, value) => {
    state.userType = value
  },
  SET_WORKUNIT: (state, value) => {
    state.workUnit = value
  },
  SET_WORKUNITID: (state, value) => {
    state.workUnitId = value
  },
  SET_HOME: (state, value) => {
    state.homeNum += 1
  },
  SET_ALLOWDOWNLOADPHOTO: (state, value) => {
    state.allowDownloadPhoto = value
  },
  SET_OWNINGREGION: (state, value) => {
    state.owningRegion = value
  },
  SET_PLYS: (state, value) => {
    state.plys = [...value]
  },
  SET_PLYPATHS: (state, value) => {
    state.plyPaths = value
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
}

const actions = {
  // 获取用户信息
  getAccountInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getCurrentUserInfo().then((response) => {
        const { payload } = response
        const {
          username, workUnit, workUnitId, admin, userType, allowDownloadPhoto, regionProvince, regionCity, regionDistrict, perms,
        } = payload

        const owningRegion = `${regionProvince || ''}${regionCity || ''}${regionDistrict || ''}`
        console.log('所属区域:', owningRegion)
        if (!admin && owningRegion) {
          const plys = []
          const bd = new BMap.Boundary()

          bd.get(owningRegion, (data) => {
            const count = data.boundaries.length
            for (let i = 0; i < count; i++) {
              const ply = new BMap.Polygon(data.boundaries[i], {
                strokeWeight: 2, strokeColor: 'blue', fillOpacity: 0, fillColor: '',
              })
              plys.push(ply)
            }
            commit('SET_PLYPATHS', plys.map((path) => path.getPath()).flat()) // 数组扁平化flat()
            commit('SET_PLYS', plys)
          })
        }

        commit('SET_OWNINGREGION', owningRegion)
        commit('SET_USERNAME', username)
        commit('SET_ADMIN', admin)
        commit('SET_PERMISSIONS', perms)
        commit('SET_USERTYPE', userType)
        commit('SET_WORKUNIT', workUnit)
        commit('SET_WORKUNITID', workUnitId)
        commit('SET_ALLOWDOWNLOADPHOTO', allowDownloadPhoto)

        resolve(payload)
      }).catch((error) => {
        reject(error)
      })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
