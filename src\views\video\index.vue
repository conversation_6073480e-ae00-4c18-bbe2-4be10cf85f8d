<!--  -->
<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-select v-model="listQuery.name" class="filter-item">
        <el-option>名称1</el-option>
        <el-option>名称2</el-option>
        <el-option>名称3</el-option>
      </el-select>
      <el-input
        v-model="listQuery.number"
        clearable
        placeholder="编号"
        class="filter-item"
      />
      <el-select v-model="listQuery.state" class="filter-item">
        <el-option :value="0">不在线</el-option>
        <el-option :value="1">在线</el-option>
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        查询
      </el-button>
      <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">
        添加视频流
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="序号" prop="idX" />
      <el-table-column align="center" label="编号" prop="number" />
      <el-table-column align="center" label="名称" prop="name" />
      <el-table-column align="center" label="视频地址" prop="videoUrl" />
      <el-table-column align="center" label="预览地址" prop="previewUrl" />
      <el-table-column align="center" label="坐标类型" prop="coordinateType" />
      <el-table-column align="center" label="经度" prop="longitude" />
      <el-table-column align="center" label="纬度" prop="latitude" />
      <el-table-column align="center" label="详细地址" prop="address" />
      <el-table-column align="center" label="行政区域编码" prop="areaCode" />
      <el-table-column align="center" label="创建日期">
        <template slot-scope="{row}">
          {{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handlePreview(row)">预览</el-button>
          <el-button type="text" @click="handleUpdate(row)">修改</el-button>
          <el-button type="text" @click="handleDetail(row)">坐标详情</el-button>
          <el-button type="text" @click="handleDelete(row,$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <CreateDialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import CreateDialog from './components/create-dialog.vue'

export default {

  components: { Pagination, CreateDialog },
  data() {
    return {
      listQuery: {
        name: null,
        number: '',
        state: null,
      },
      listLoading: false,
      list: [
        {
          idX: 1,
          number: '-',
          name: '鲁朗巡路',
          videoUrl: 'rtsp://*************:8554/live/c',
          previewUrl: 'rtsp://*************:8554/live/c',
          coordinateType: '原始坐标',
          longitude: 116.729614,
          latitude: 39.874493,
          address: '城市绿心森林公园',
          areaCode: 110112105,
          createTime: '2022-08-19T12:20:58',
        },
      ],
      temp: {},
    }
  },

  computed: {},

  created() {},

  mounted() {},

  methods: {
    getList() {},
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
  },
}

</script>
<style lang='scss' scoped>
</style>
