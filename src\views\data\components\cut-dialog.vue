/*
 * @Author: wangyj
 * @Date: 2022-11-16 18:00:44
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-17 11:05:32
 */

<template>
  <el-dialog title="编辑巡检信息" :visible.sync="dialogFormVisible" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="起点经度" prop="lngStart">
        <el-input v-model="temp.lngStart" placeholder="起点经度" disabled />
      </el-form-item>
      <el-form-item label="起点纬度" prop="latStart">
        <el-input v-model="temp.latStart" placeholder="起点纬度" disabled />
      </el-form-item>
      <el-form-item label="止点经度" prop="lngEnd">
        <el-input v-model="temp.lngEnd" placeholder="止点经度" disabled />
      </el-form-item>
      <el-form-item label="止点维度" prop="latEnd">
        <el-input v-model="temp.latEnd" placeholder="止点维度" disabled />
      </el-form-item>
      <el-form-item label="行政区划编码" prop="adCode">
        <el-input v-model="temp.adCode" placeholder="请输入行政区划编码" />
      </el-form-item>
      <el-form-item label="路线编号" prop="roadCode">
        <el-input v-model="temp.roadCode" placeholder="请输入路线编号" />
      </el-form-item>
      <el-form-item label="路线名称" prop="roadName">
        <el-input v-model="temp.roadName" placeholder="请输入路线名称" />
      </el-form-item>
      <el-form-item label="路面宽度（m）" prop="pavementWidth">
        <el-input v-model="temp.pavementWidth" placeholder="请输入路面宽度" />
      </el-form-item>
      <el-form-item label="道路等级" prop="roadLevel">
        <el-radio-group v-model="temp.roadLevel">
          <el-radio :label="5">高速公路</el-radio>
          <el-radio :label="1">一级公路</el-radio>
          <el-radio :label="2">二级公路</el-radio>
          <el-radio :label="3">三级公路</el-radio>
          <el-radio :label="4">四级及以下公路</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="路面类型" prop="roadType">
        <el-radio-group v-model="temp.roadType">
          <el-radio :label="1">沥青路面</el-radio>
          <el-radio :label="2">水泥混凝土路面</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="检测方向" prop="detectDirection">
        <el-radio-group v-model="temp.detectDirection">
          <el-radio :label="1">上行</el-radio>
          <el-radio :label="2">下行</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="起点桩号" prop="startPileNum">
        <el-input v-model="temp.startPileNum" placeholder="请输入起点桩号" />
      </el-form-item>
      <!-- <el-form-item label="止点桩号" prop="endPileNum">
        <el-input v-model="temp.endPileNum" placeholder="请输入止点桩号" disabled />
      </el-form-item> -->
      <el-form-item label="检测单位" prop="detectUnit">
        <el-input v-model="temp.detectUnit" placeholder="请输入检测单位" />
      </el-form-item>
      <el-form-item label="委托单位" prop="delegateUnit">
        <el-input v-model="temp.delegateUnit" placeholder="请输入委托单位" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="submitData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { cutTask } from '@/api/data'

export default {
  props: {
    taskData: {
      type: Object,
    },
    points: {
      type: Array,
    },
  },
  data() {
    return {
      temp: {},
      dialogFormVisible: false,
      rules: {
        detectDirection: [{ required: true, message: '请选择检测方向', trigger: 'blur' }],
        roadName: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
        pavementWidth: [{ required: true, message: '请输入路面宽度', trigger: 'blur' }],
        roadLevel: [{ required: true, message: '请选择道路级别', trigger: 'blur' }],
        roadType: [{ required: true, message: '请选择路面类型', trigger: 'blur' }],
        startPileNum: [
          { required: true, message: '请输入起点桩号', trigger: 'blur' },
          { pattern: /^(?:[1-9]\d*|0)(?:\.\d{1,3})?$/, message: '请输入大于等于0的数字, 小数最多保留三位' },
        ],
        // adCode: [{ pattern: /^[1-8][0-7]\d{4}$/, message: '行政区划编码格式不正确' }],
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = {
          ...this.taskData,
          ...{
            lngStart: this.points[0].lng,
            latStart: this.points[0].lat,
            lngEnd: this.points[1].lng,
            latEnd: this.points[1].lat,
          },
        }

        this.temp.roadLevel = this.temp.roadLevel || 5
        this.temp.roadType = this.temp.roadType || 1
        this.temp.detectDirection = this.temp.detectDirection || 1
        this.temp.startPileNum = this.temp.startPileNum || 0

        console.log(this.taskData)
        console.log(this.temp)
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    submitData() {
      const that = this
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const {
            id, lngStart, latStart, lngEnd, latEnd, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          } = this.temp

          const tempData = {
            id, lngStart, latStart, lngEnd, latEnd, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          }

          const loading = this.$loading({
            lock: true,
            text: '检测任务裁剪中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })

          cutTask(tempData).then((res) => {
            if (res.status === 200) {
              loading.close()
              this.$message({
                message: '检测任务裁切成功！当前仍处于任务裁剪状态，可继续裁剪。',
                type: 'success',
                duration: 5000,
              })

              this.dialogFormVisible = false

              that.$emit('cutSuccess')
            } else {
              loading.close()
              this.$message({
                message: '检测任务裁切失败，请重新提交',
                type: 'error',
              })
            }
          }, (res) => {
            loading.close()
          })
        }
      })
    },
    show(points) {
      this.dialogFormVisible = true
    },
  },
}
</script>
