/*
 * @Author: wangyj
 * @Date: 2022-11-11 11:01:55
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-11 14:32:12
 */
<template>
  <el-dialog
    title="查看检测历史记录"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="70%"
    custom-class="history-dialog"
  >
    <div class="history-container">
      <el-row :gutter="20" class="history-list">
        <el-col v-for="(o, index) in 12" :key="o" :span="6">
          <el-card class="history-card" :body-style="{padding: '10px'}">
            <div class="history-card_img">
              <img src="@/assets/login/bj.jpg">
            </div>
            <div class="history-card_info">
              <div>检测结果：</div>
              <div>检测时间：</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <Pagination :total="total" />
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'

export default {

  components: { Pagination },
  data() {
    return {
      dialogVisible: false,
      total: 1,
    }
  },

  computed: {},

  created() {},

  mounted() {},

  methods: {
    show() {
      this.dialogVisible = true
    },
  },
}

</script>
<style lang='scss' scoped>
  @import '@/styles/mixin.scss';
  ::v-deep .history-dialog {
    .el-dialog__body {
      max-height: 90%!important;
      height: 65vh;
      padding: 20px 20px 10px;
    }
  }
  .history-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .history-list {
      overflow-y: auto;
      @include scrollBar;

      .history-card {
        margin-bottom: 20px;
        .history-card_img {
          width: 100%;
          padding-bottom: 56.25%;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
          }
        }

        .history-card_info {
          margin-top: 10px;
          div {
            padding-bottom: 10px;
          }
        }
      }
    }

    .pagination-container {
      padding: 10px;
    }

  }

</style>
