<template>
  <div ref="container" class="container" style="cursor: default">
    <baidu-map
      v-car-loading="mapLoading"
      class="bm-view"
      :center="center"
      :zoom="zoom"
      :auto-resize="true"
      :scroll-wheel-zoom="true"
      @ready="handler"
      @zoomend="handleZoomEnd"
    >
      <!-- 道路病害图 -->
      <canvas id="canvas" :width="canvasWidth" :height="canvasHeight" />
      <bml-marker-clusterer
        v-if="devices.length > 0"
        :average-center="true"
        :styles="styles"
      >
        <bm-marker
          v-for="device in devices"
          :key="device.deviceKey"
          :position="{ lat: device.gpsLatitude, lng: device.gpsLongitude }"
          :rotation="device.deviceModel !== 1 ? device.rotation : undefined"
          :icon="getDeviceIcon(device)" 
          :top="true"
          @click="handleShow(device)"
        >
          <bm-label
            v-if="device.deviceKey === curDevice.deviceKey"
            :content="device.deviceName"
            :label-style="{
              borderRadius: '10px',
              color: '#57a3f3',
              fontSize: '16px',
              fontWeight: 'bold',
              border: 'none',
              padding: '2px 10px',
              transform: 'translateX(-50%)',
            }"
            :offset="{ height: -20, width: 32 }"
          />
        </bm-marker>
      </bml-marker-clusterer>
      <!-- 原始配色 '#7EBE2A', '#28CAE3', '#705FFF', '#F4D539', '#FF1C1C' -->
      <!-- 所有绿点 -->
      <bm-point-collection
        :points="greenPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="pointShow ? GLOBAL_VARIATE.colorA : 'rgba(255,255,255,0)'"
        :size="pointSize"
        @click="handlePointClick($event)"
      />
      <!-- 所有蓝点 -->
      <bm-point-collection
        :points="bluePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="pointShow ? GLOBAL_VARIATE.colorB : 'rgba(255,255,255,0)'"
        :size="pointSize"
        @click="handlePointClick($event)"
      />
      <!-- 所有紫点 -->
      <bm-point-collection
        :points="purplePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="pointShow ? GLOBAL_VARIATE.colorC : 'rgba(255,255,255,0)'"
        :size="pointSize"
        @click="handlePointClick($event)"
      />
      <!-- 所有黄点 -->
      <bm-point-collection
        :points="yellowPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="pointShow ? GLOBAL_VARIATE.colorD : 'rgba(255,255,255,0)'"
        :size="pointSize"
        @click="handlePointClick($event)"
      />
      <!-- 所有红点 -->
      <bm-point-collection
        :points="redPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="pointShow ? GLOBAL_VARIATE.colorE : 'rgba(255,255,255,0)'"
        :size="pointSize"
        @click="handlePointClick($event)"
      />

      <!-- 无数条线段集合，每一节颜色都不一样 -->
      <bm-polyline
        v-for="(item, index) in mapLineData"
        :id="item.id"
        :key="index + item.color"
        :path="item.line"
        :stroke-color="item.color"
        :stroke-opacity="1"
        :stroke-weight="7"
        @click="handlePointClick($event, item.id)"
      />

      <!-- 巡检中车辆拉一个巡检轨迹 -->
      <bm-polyline
        v-for="item in runningLineData"
        :key="item.deviceKey"
        :path="item.line"
        stroke-color="#FF48C1"
        :stroke-opacity="1"
        :stroke-weight="7"
      />
      <bm-info-window
        ref="diseaseWindow"
        :position="diseaseWindow.position"
        :title="diseaseWindow.title"
        :show="diseaseWindow.show"
        :close-on-click="false"
        :auto-pan="false"
        @close="handleInfoWindowClose"
        @open="handleInfoWindowOpen"
        @clickclose="handleInfoWindowClickClose"
      >
        <div v-cloak v-if="diseaseWindow.show" class="disease-cont">
          <div class="disease-img-box">
            <img
              v-show="diseaseWindow.url !== 3 && diseaseWindow.url !== 2"
              :src="diseaseWindow.url"
              class="disease-img"
              alt="鼠标滑轮滚动缩放图片"
              title="点击放大图片"
              style="object-fit: cover;"
              width="700px"
              height="393px"
              @click="handleShowBigImg"
            >
            <div v-if="diseaseWindow.url === 3" class="empty">
              图片尚未续传成功，请耐心等待。
            </div>

            <div v-if="diseaseWindow.url === 2" class="empty" style="color: #409eff;">
              正在加载中...
            </div>

            <!-- <div
              v-if="diseaseWindow.notUpload"
              class="disease-img disease-img-error"
            >
              {{ diseaseWindow.errorTxt }}
            </div> -->
          </div>

          <div class="disease-deal-btns">
            <div style="position: relative">
              <!-- <div
                v-if="diseaseWindow.btnsShow"
                style="color: #606266; line-height: 32px"
              >
                选择正确的{{
                  diseaseWindow.btnsType === 1
                    ? "病害"
                    : diseaseWindow.btnsType === 2
                      ? "道路资产"
                      : "路面风险"
                }}类型：
              </div> -->
            </div>
            <div>
              <el-button
                size="small"
                round
                @click="handlePrev"
              >
                上一张
              </el-button>
              <el-button
                size="small"
                round
                @click="handleNext"
              >
                下一张
              </el-button>
            </div>
          </div>
        </div>
      </bm-info-window>
    </baidu-map>
    <div class="legend-cont">
      <div class="legeng-btn" title="图例" @click="legendShow = !legendShow">
        ?
      </div>
      <div v-show="legendShow" class="legend-img">
        <img src="@/assets/new-sample.png" width="100%">
      </div>
    </div>

    <div ref="contOute" class="select-cont-outer animate__animated animate__fast" :class="animatedClass">
      <el-select
        v-model.number="curMilVal"
        placeholder="请选择"
        @change="getMapData(false)"
      >
        <el-option
          v-for="item in milArr"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <div v-show="isShow" class="inspection-panel">
      <el-card class="card">
        <i class="el-icon-close" @click="handleHide" />
        <div class="title active-tab">
          <i class="el-icon-discount active-tab" />
          <span class="active-tab">设备信息</span>
        </div>
        <div class="info">
          <div class="info-item">
            <p>设备单位：{{ curDevice.workUnit }}</p>
            <p>设备名称：{{ curDevice.deviceName }}</p>
            <p>
              设备状态：{{ curDevice.using ? '工作中' : '未工作' }}
              <span v-if="curDevice.using" class="using" />
              <span v-else class="no-using" />
            </p>
          </div>
        </div>
        <div class="title">
          <span class="cursor-p" :class="{'active-tab': activeTab === 1}" @click="activeTab = 1">
            <i class="el-icon-time" />
            <span>设备巡检历史</span>
          </span>
          <span v-if="videoUrl" class="boundary" />
          <span v-if="videoUrl" class="cursor-p":class="{'active-tab': activeTab === 2}" @click="activeTab = 2">
            <i class="el-icon-video-camera" />
            <span>实时视频</span>
          </span>
        </div>
        <el-table
          v-show="activeTab === 1"
          v-loading="loading"
          size="mini"
          :data="inspectionListData"
          border
          :max-height="tableHeight"
          style="width: 100%"
        >
          <el-table-column align="center" prop="roadName" label="路线名称" width="90" />
          <el-table-column align="center" prop="detectDirection" label="检测方向" width="70">
            <template slot-scope="{ row }">
              {{ row.detectDirection === 1 ? "上行" : "下行" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="巡检开始时间" width="120">
            <template slot-scope="{ row }">
              {{ new Date(row.startTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}") }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="巡检结束时间" width="120">
            <template slot-scope="{ row }">
              <template v-if="row.hasEnd">
                {{ new Date(row.endTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}") }}
              </template>
              <template v-else>巡检中</template>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="巡检里程(km)"
            prop="inspectMileage"
            width="95"
          />
          <el-table-column align="center" label="操作" width="60">
            <template slot-scope="{ row, $index }">
              <el-button type="text" @click="handleDetail(row, $index)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="activeTab === 2" class="live-box">
          <Jessibuca v-if="isShow && activeTab === 2 && videoUrl" :play-url="videoUrl" />
        </div>
        <Pagination
          v-show="activeTab === 1 && total > 0"
          :total="total"
          :page.sync="listQuery.currentPage"
          :limit.sync="listQuery.pageSize"
          :pager-count="5"
          layout="total, prev, pager, next"
          @pagination="getInspectionListAjax"
        />
      </el-card>
    </div>
  </div>
</template>

<script>

import { simpleStyleJson } from '@/utils/map-style'
import { getAnalysis } from '@/api/analysis'
import { getQueryDamagePictures, getQueryOneDamagePicture, getQueryRecentOneDayDamages } from '@/api/home'
import CanvasMixin from '@/mixin/canvas'
import Pagination from '@/components/Pagination/index.vue'
import { BmlMarkerClusterer } from 'vue-baidu-map-v3'
import 'viewerjs/dist/viewer.css'
import { api as viewerApi } from 'v-viewer'
import { mapState } from 'vuex'
import _ from 'lodash'

// import "../utils/marker-clusterer";

import binghaiicon from '@/assets/home/<USER>'
import zichanicon from '@/assets/home/<USER>'
import sheshiicon from '@/assets/home/<USER>'
import fengxianicon from '@/assets/home/<USER>'
import binghaiCluster from '@/assets/home/<USER>'
import fengxianCluster from '@/assets/home/<USER>'
import zichanCluster from '@/assets/home/<USER>'
import sheshiCluster from '@/assets/home/<USER>'
import quanbuCluster from '@/assets/home/<USER>'
import Jessibuca from '@/components/Jessibuca.vue'
import {
  markerData,
  resetMarkerData,
  setMarkerData,
  setInfoWindowViewport,
} from '../utils/factory'
import DevicesMixin from '../mixin/devices'

let pointClickDebounceTimer // 海量点的防抖
let damagesList = [] // 路段所有病害信息
let cacheDamagesObj = {} // 缓存当前路段的图片
let locIndex = 0 // 当前病害位置
let locId = 0 // 当前海量点 折线 点击的路段id
let locSize = 0 // 当前路段图片的数量

export default {
  name: 'NotAdmin',
  components: { BmlMarkerClusterer, Pagination, Jessibuca },
  mixins: [DevicesMixin, CanvasMixin],
  props: {
    rightShow: {
      type: Boolean,
      default: true,
    },
    leftShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      worker: null,
      isIconClick: true,
      canvas: null,
      ctx: null,
      canvasWidth: 640,
      canvasHeight: 360,
      center: {
        lng: 116.404,
        lat: 39.915,
      },
      legendShow: true, // 图例显隐
      viewer: null,
      viewerFlag: false,
      viewerZoomDetail: null,
      viewerMoveDetail: null,
      mapLayerType: {
        1: {
          title: '病害类型',
          type: 'disease',
        },
        2: {
          title: '资产类型',
          type: 'assets',
        },
        3: {
          title: '风险类型',
          type: 'risk',
        },
        5: {
          title: '设施类型',
          type: 'facility',
        },
        9: {
          title: '病害类型',
          type: 'disease',
        },
        10: {
          title: '城市管理',
          type: 'cityManagement',
        },
        all: {
          title: '全部',
          type: 'all',
        },
      },
      mapLoading: false, // 地图加载状态
      mapLineData: [], // 线段集合数据
      greenPointData: [], // 绿色点集合数据
      bluePointData: [], // 蓝色点集合数据
      purplePointData: [], // 紫色点集合数据
      yellowPointData: [], // 黄色点集合数据
      redPointData: [], // 红色点集合数据
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnit: null,
        deviceKey: null,
        startTime: null,
        endTime: null,
        deviceKeyInput: '',
      },
      pointSize: 'BMAP_POINT_SIZE_NORMAL', // 海量点的大小 https://lbsyun.baidu.com/cms/jsapi/class/jsapi_reference.html#a3b23s

      curMilVal: 14,
      milArr: [
        {
          label: '100m',
          value: 17,
        },
        {
          label: '200m',
          value: 16,
        },
        {
          label: '500m',
          value: 15,
        },
        {
          label: '1km',
          value: 14,
        },
      ],
      runningLineData: [], // 运行中车辆轨迹信息
    }
  },
  watch: {
    plyPaths: {
      handler(value) {
        // 这里是第一次进入页面渲染行政边界、设置视野, 第二次进页面走地图ready回调
        // 为什么不判断map存在呢,因为plyPath是异步的执行完之后map实例(同步执行)是必然存在的
        // 在地图渲染之前就ply区域边界就执行完,地图视野就不会先定位默认的center
        if (value) {
          // console.log('监听', value);
          this.addPolygon()
        }
      },
      deep: true,
    },
  },
  computed: {
    // 判断海量点显隐
    pointShow() {
      return this.zoom > 12
    },
    ...mapState({
      owningRegion: (state) => state.account.owningRegion,
      plys: (state) => state.account.plys,
      plyPaths: (state) => state.account.plyPaths,
    }),
    animatedClass() {
      if (this.rightShow) return ['slideInRight']
      return ['slideOutRight']
    },

  },
  async created() {
    this.listQuery.workUnit = await this.workUnit
    this.worker = new Worker('/static/workers/cache.worker.js')
    this.worker.onmessage = (event) => {
      if (event.data.key === markerData.index) {
        console.log(event.data)
        if (this.diseaseWindow.url === '' || this.diseaseWindow.url === 2) {
          this.diseaseWindow.url = event.data.image
        }
      }
      markerData.caches[markerData.mapTypes[markerData.currentCacheType]][event.data.key].url = event.data.image
    }
  },
  activated() {
    const that = this
    if (that.allIdentifyType.length === 0) that.getModelIdentifyTypesAll()
    this.keyDown()
  },
  deactivated() {
    // 取消键盘监听事件
    document.onkeydown = null
  },
  destroyed() {
    // 将覆盖物删除(一个覆盖物实例只能向地图中添加一次)
    this.plys.forEach((ply, index) => {
      this.map.removeOverlay(ply)
    })
  },
  methods: {
    handler({ BMap, map }) {
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
      map.disableKeyboard()
      // // map.getContainer().style.backgroundColor = '#edf3f3' // 解决加载瓦片跟地图背景不一致
      const that = this
      that.map = map
      that.BMap = BMap
      that.zoom = 6

      if (this.plyPaths) this.addPolygon() // 第二次进入页面不走监听plyPath(因为第一次plyPath在登录的时候就缓存了,监听还得判断map是否存在)

      this.getMapData(!this.owningRegion) // 有所属行政, 就只显示所属行政的范围
      const svgDom = map.getPanes().mapPane.getElementsByTagName('svg')[0] // 解决海量点覆盖(路线)polyline引发的鼠标事件丢失问题
      if (svgDom) svgDom.style.zIndex = 900
      this.center = map.getCenter()
    },
    handleZoomEnd() {
      if (this.map) {
        this.zoom = this.map.getZoom() // 获取并更新地图缩放级别
        this.center = this.map.getCenter()
      }
    },
    // 添加行政区划边界
    addPolygon() {
      const that = this
      that.map.setViewport(this.plyPaths, {
        margins: [0, 460, 0, 370],
        delay: 0,
        enableAnimation: false,
        // zoomFactor: 1
      })
      this.plys.forEach((ply) => that.map.addOverlay(ply))
    },
    async getMapData(setViewport = true) {
      const that = this
      this.greenPointData = []
      this.yellowPointData = []
      this.redPointData = []
      this.purplePointData = []
      this.bluePointData = []
      this.mapLineData = []

      this.mapLoading = true

      const mapGLineData = []
      const mapBLineData = []
      const mapPLineData = []
      const mapYLineData = []
      const mapRLineData = []

      const {
        workUnit, deviceKey, startTime, endTime, deviceKeyInput,
      } = this.listQuery
      const params = {
        workUnit: workUnit === '' ? null : workUnit,
        deviceKey: null,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
        zoom: this.curMilVal,
      }
      if (deviceKey === null || deviceKey === '') {
        if (deviceKeyInput !== '') {
          params.deviceKey = deviceKeyInput
        }
      } else {
        params.deviceKey = deviceKey
      }
      const { payload } = await getAnalysis(params)
      if (!payload) {
        this.mapLoading = false
        return
      }
      const allPoints = []
      payload.routes.forEach((route) => {
        route.forEach((point) => {
          if (point.pqi >= 90) {
            this.greenPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
                id: point.id,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: point.id,
              },
            )

            mapBLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              id: point.id,
              color: this.GLOBAL_VARIATE.colorA,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 80 && point.pqi < 90) {
            // 蓝
            that.bluePointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
                id: point.id,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: point.id,
              },
            )

            mapBLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              id: point.id,
              color: this.GLOBAL_VARIATE.colorB,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 70 && point.pqi < 80) {
            // 紫
            that.purplePointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
                id: point.id,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: point.id,
              },
            )

            mapPLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              id: point.id,
              color: this.GLOBAL_VARIATE.colorC,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else if (point.pqi >= 60 && point.pqi < 70) {
            // 黄
            that.yellowPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
                id: point.id,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: point.id,
              },
            )

            mapYLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              id: point.id,
              color: this.GLOBAL_VARIATE.colorD,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          } else {
            // 红
            that.redPointData.push(
              {
                lng: point.lngStart,
                lat: point.latStart,
                id: point.id,
              },
              {
                lng: point.lngEnd,
                lat: point.latEnd,
                id: point.id,
              },
            )

            mapRLineData.push({
              line: [
                {
                  lng: point.lngStart,
                  lat: point.latStart,
                },
                {
                  lng: point.lngEnd,
                  lat: point.latEnd,
                },
              ],
              id: point.id,
              color: this.GLOBAL_VARIATE.colorE,
            })

            allPoints.push(
              new this.BMap.Point(point.lngStart, point.latStart),
              new this.BMap.Point(point.lngEnd, point.latEnd),
            )
          }
        })
      })

      this.$nextTick(() => {
        that.mapLineData = [
          ...mapGLineData,
          ...mapBLineData,
          ...mapPLineData,
          ...mapYLineData,
          ...mapRLineData,
        ]
      })

      // 地图视野包含所有点
      if (setViewport) that.map.setViewport(allPoints, { margins: [0, 370, 0, 370] })
      that.zoom = that.map.getZoom()

      this.handleGetQueryRecentOneDayDamages()
      this.mapLoading = false
    },
    getBMC({ markers, url, minClusterSize }) {
      return new BMapLib.MarkerClusterer(this.map, {
        markers,
        isAverangeCenter: true,
        maxZoom: 20,
        minClusterSize: minClusterSize || 10,
        styles: [
          {
            url,
            size: new BMap.Size(53, 53),
            textSize: 12,
          },
        ],
      })
    },
    getBmarker({ lnt, lat, url }) {
      return new BMap.Marker(new BMap.Point(lnt, lat),
        {
          icon: new BMap.Icon(
            url,
            new BMap.Size(36, 41),
            {
              imageSize: new BMap.Size(36, 41),
            },
          ),
          offset: new BMap.Size(0, -18),
        })
    },
    /**
     * 点聚合的显示隐藏
     * @param {number} modelIdentifyType
     *
     * @return
    */
    markerClusterVisibility(modelIdentifyType) {
      this.diseaseWindow.show = false

      const key = this.mapLayerType[modelIdentifyType].type

      Object.keys(markerData.markerClusterers).forEach((k) => {
        const markerClusterers = markerData.markerClusterers[k]

        if (markerClusterers) {
          if (k === key) {
            const markers = markerData.markers[k]

            if (markerClusterers.getMarkers().length === 0 && markers.length) {
              markerClusterers.addMarkers(markers)
            }
          } else {
            markerClusterers.clearMarkers()
          }
        }
      })
    },
    async handleGetQueryRecentOneDayDamages(params = {}) {
      const { payload: res } = await getQueryRecentOneDayDamages(params)
      resetMarkerData()

      if (!res.length) return
      let aIndex = 0; let dIndex = 0; let fIndex = 0; let
        rIndex = 0
      const options = {
        key: '',
        currentIndex: 0,
        allIndex: 0,
        tag: {},
        marker: null,
        allMarkers: null,
      }

      for (let i = 0; i < res.length; i++) {
        const tag = res[i]
        const { gpsLongitude: lnt, gpsLatitude: lat, modelIdentifyType } = tag
        options.allIndex = i
        options.tag = tag

        switch (modelIdentifyType) {
        case 1: // 病害
          options.marker = this.getBmarker({ lnt, lat, url: binghaiicon })
          options.key = 'disease'
          options.currentIndex = dIndex
          options.allMarkers = this.getBmarker({ lnt, lat, url: binghaiicon })
          setMarkerData(options)
          dIndex++
          break
        case 2: // 资产
          options.marker = this.getBmarker({ lnt, lat, url: zichanicon })
          options.key = 'assets'
          options.currentIndex = aIndex
          options.allMarkers = this.getBmarker({ lnt, lat, url: zichanicon })
          setMarkerData(options)
          aIndex++
          break
        case 3: // 风险
          options.marker = this.getBmarker({ lnt, lat, url: fengxianicon })
          options.key = 'risk'
          options.currentIndex = rIndex
          options.allMarkers = this.getBmarker({ lnt, lat, url: fengxianicon })
          setMarkerData(options)
          rIndex++
          break
        case 5: // 设施
          options.marker = this.getBmarker({ lnt, lat, url: sheshiicon })
          options.key = 'facility'
          options.currentIndex = fIndex
          options.allMarkers = this.getBmarker({ lnt, lat, url: sheshiicon })
          setMarkerData(options)
          fIndex++
          break
        default:
          break
        }
        this.markerClick(options.marker)
        this.markerClick(options.allMarkers)
      }

      const {
        facility, disease, risk, assets, all,
      } = markerData.markers
      markerData.markerClusterers.all = this.getBMC({ markers: all, url: quanbuCluster, minClusterSize: 30 })
      markerData.markerClusterers.facility = this.getBMC({ markers: facility, url: sheshiCluster })
      markerData.markerClusterers.disease = this.getBMC({ markers: disease, url: binghaiCluster })
      markerData.markerClusterers.risk = this.getBMC({ markers: risk, url: fengxianCluster })
      markerData.markerClusterers.assets = this.getBMC({ markers: assets, url: zichanCluster })

      this.markerClusterVisibility(this.$parent.inspectionData.params.modelIdentifyType) // 默认显示病害
    },

    markerClick(marker) {
      marker.addEventListener('click', ({ target }) => {
        // target.setTop(true)
        const that = this
        const {
          currentIndex, allIndex, type, coordinate, modelIdentifyType, gpsLongitude, gpsLatitude, pictureUrl, area, address,
        } = target.tag

        const parentModelIdentifyType = this.$parent.inspectionData.params.modelIdentifyType
        this.setIconCache(parentModelIdentifyType === 'all' ? allIndex : currentIndex, parentModelIdentifyType)
        const image = new Image()
        image.src = pictureUrl
        image.setAttribute('crossOrigin', 'anonymous')
        image.onload = () => {
          that.getCtx(image, 'canvas', image.width, image.height)
          if (!that.canvas || !that.ctx) return
          // 图片标注病害处理
          that.ctx.drawImage(image, 0, 0, image.width, image.height)
          that.ctxDraw(type, coordinate)
          const params = {
            title: `${this.getLayerTypeTitle(modelIdentifyType)}: ${this.$parent.allIdentifyTypeMap[type]} 面积: ${area ? area.toFixed(3) : '-'}㎡  道路: ${address || '-'}`,
            url: that.canvas.toDataURL(),
            position: {
              lng: gpsLongitude,
              lat: gpsLatitude,
            },
          }
          this.openWindow(params)
        }
        image.onerror = (error) => {
          const params = {
            title: `${this.getLayerTypeTitle(modelIdentifyType)}: ${this.$parent.allIdentifyTypeMap[type]}  面积: ${area ? area.toFixed(3) : '-'}㎡  道路: ${address || '-'}`,
            url: 3,
            position: {
              lng: gpsLongitude,
              lat: gpsLatitude,
            },
          }
          this.openWindow(params)
        }
      })
    },
    // canvas 绘制矩形和文字
    ctxDraw1(color, typeName, coordinate, accuracy = null) {
      const that = this
      that.ctx.lineWidth = 3
      if (that.canvas.width > 640) {
        that.ctx.font = 'bold 22px Arial'
      } else {
        that.ctx.font = 'bold 22px Arial'
      }

      that.ctx.strokeStyle = color
      that.ctx.fillStyle = color

      if (accuracy) {
        typeName = `${typeName}(${(accuracy * 100).toFixed(1)}%)`
      }
      const coordinateArr = coordinate.split(' ')
      that.ctx.strokeRect(
        coordinateArr[0] * 1,
        coordinateArr[1] * 1,
        coordinateArr[2] - coordinateArr[0],
        coordinateArr[3] - coordinateArr[1],
      )

      // 文字位置：框上或下，对齐：左对齐或右对齐
      const txtW = that.ctx.measureText(typeName).width
      if (coordinateArr[0] * 1 + txtW > that.canvas.width) {
        that.ctx.textAlign = 'right'
        if (coordinateArr[1] * 1 < 25) {
          if (that.canvas.width > 640) {
            that.ctx.fillText(
              typeName,
              coordinateArr[2] * 1,
              coordinateArr[3] * 1 + 25,
            )
          } else {
            that.ctx.fillText(
              typeName,
              coordinateArr[2] * 1,
              coordinateArr[3] * 1 + 25,
            )
          }
        } else {
          that.ctx.fillText(
            typeName,
            coordinateArr[2] * 1,
            coordinateArr[1] * 1 - 6,
          )
        }
      } else {
        that.ctx.textAlign = 'left'
        if (coordinateArr[1] * 1 < 25) {
          if (that.canvas.width > 640) {
            that.ctx.fillText(
              typeName,
              coordinateArr[0] * 1,
              coordinateArr[3] * 1 + 25,
            )
          } else {
            that.ctx.fillText(
              typeName,
              coordinateArr[0] * 1,
              coordinateArr[3] * 1 + 25,
            )
          }
        } else {
          that.ctx.fillText(
            typeName,
            coordinateArr[0] * 1,
            coordinateArr[1] * 1 - 6,
          )
        }
      }
    },
    getImg(obj, key) {
      return new Promise((resolve, reject) => {
        const that = this
        const {
          gpsLongitude, gpsLatitude, pictureUrl, damages,
        } = obj
        that.loadImg(pictureUrl).then((image) => {
          that.getCtx(image, 'canvas', image.width, image.height)
          if (!that.canvas || !that.ctx) return
          // 图片标注病害处理
          that.ctx.drawImage(image, 0, 0, image.width, image.height)
          damages.forEach((damage) => {
            this.ctxDraw1(damage.color, damage.typeName, damage.coordinate)
          })

          const imgSrc = that.canvas.toDataURL()

          const obj = {
            position: {
              lng: gpsLongitude,
              lat: gpsLatitude,
            },
            url: imgSrc,
            title: `病害图片 ${key + 1}/${locSize}张`,
          }
          if (!cacheDamagesObj[key]) cacheDamagesObj[key] = obj
          resolve(obj)
        })
      })
    },
    getLayerTypeTitle(modelIdentifyType) {
      return this.mapLayerType[modelIdentifyType].title
    },
    setIconCache(index, mapType) {
      return new Promise((resolve, reject) => {
        this.isIconClick = true
        const chunkSize = 3 // 缓存切片值
        markerData.index = index
        markerData.currentCacheType = mapType

        const markers = markerData.markers[markerData.mapTypes[mapType]]
        const caches = markerData.caches[markerData.mapTypes[mapType]]

        const start = Math.max(0, index - chunkSize)
        const end = Math.min(markers.length, index + chunkSize)

        const { length } = markers.slice(start, end)
        for (let i = 0; i < length; i++) {
          const key = start + i
          if (!caches[key]) {
            const {
              type, coordinate, modelIdentifyType, gpsLongitude, gpsLatitude, pictureUrl, area, address,
            } = markers[key].tag
            const params = {
              key,
              pictureUrl,
              type,
              coordinate,
              allIdentifyType: this.allIdentifyType,
            }
            this.worker.postMessage(params)
            caches[key] = {
              title: `${this.getLayerTypeTitle(modelIdentifyType)}: ${this.$parent.allIdentifyTypeMap[type]} 面积: ${area ? area.toFixed(3) : '-'}㎡  道路: ${address || '-'}`,
              url: 2,
              position: {
                lng: gpsLongitude,
                lat: gpsLatitude,
              },
            }
          }
        }
      })
    },
    // 缓存病害图片
    cacheImgData(type = 'loc') {
      return new Promise(() => {
        const params = {
          id: locId,
          picTime: '',
        }

        let start; let end
        const chunkSize = 10 // 缓存切片值

        if (type === 'loc') {
          // 某路段第一次加载缓存前后几张
          start = Math.max(0, locIndex - chunkSize)
          end = Math.min(locSize, locIndex + chunkSize + 1)
          const tempDamagesList = damagesList.slice(start, end)
          for (let i = 0; i < tempDamagesList.length; i++) {
            this.onGetQueryOneDamagePicture(start + i, params)
          }
        } else if (type === 'prev') {
          // 上一张的时候计算下标提前调用接口 存到缓存(cacheDamagesObj)
          end = Math.max(0, locIndex - chunkSize)
          if (cacheDamagesObj[end]) return // 判断缓存的最大最小key(小标) 有的话就不调接口(节省性能)
          this.onGetQueryOneDamagePicture(end, params)
        } else {
          // 下一张的时候计算下标提前调用接口 存到缓存(cacheDamagesObj)
          end = Math.min(locSize, locIndex + chunkSize + 1)
          if (cacheDamagesObj[end]) return // 判断缓存的最大最大key(下标) 有的话就不调接口(节省性能)
          this.onGetQueryOneDamagePicture(end - 1, params)
        }
      })
    },
    async onGetQueryOneDamagePicture(key, params) {
      return new Promise(async () => {
        if (cacheDamagesObj[key]) return
        params.picTime = damagesList[key].photoTime
        const { payload } = await getQueryOneDamagePicture(params)
        this.getImg(payload, key)
      })
    },
    setDiseaseWindow(data) {
      this.diseaseWindow.url = data.url
      this.diseaseWindow.position = data.position
      this.diseaseWindow.title = data.title
    },
    // 上一张病害图片
    handlePrev: _.throttle(async function () {
      if (this.isIconClick) { // 点击地图的点
        const {
          index, caches, mapTypes, currentCacheType,
        } = markerData
        if (index === 0) {
          this.$message({
            message: '已经是第一张图片了',
            type: 'warning',
          })
          return
        }

        const cache = caches[mapTypes[currentCacheType]]
        markerData.index -= 1
        this.openWindow(cache[markerData.index])
        this.setIconCache(markerData.index, currentCacheType)
      } else {
        let { inspectionData: { content }, activeVar } = this.$parent
        if (this.$parent.activeVar === 0) {
          this.$message({
            message: '已经是第一张图片了',
            type: 'warning',
          })
          return
        }
        activeVar -= 1
        this.$parent.activeFun(content[activeVar], activeVar, 'prev')
      }
    }, 250),
    // 下一张病害图片
    handleNext: _.throttle(async function () {
      if (this.isIconClick) { // 点击地图的点
        const {
          markers, caches, mapTypes, currentCacheType,
        } = markerData
        const cache = caches[mapTypes[currentCacheType]]
        const marker = markers[mapTypes[currentCacheType]]

        if (markerData.index === marker.length - 1) {
          this.$message({
            message: '已经是最后一张图片了',
            type: 'warning',
          })
          return
        }
        markerData.index += 1
        this.openWindow(cache[markerData.index])
        this.setIconCache(markerData.index, currentCacheType)
      } else {
        let {
          inspectionData: {
            content, loading, totalElements,
          }, activeVar,
        } = this.$parent
        if (loading && activeVar >= content.length - 1) {
          this.$message({
            message: '正在加载中,请稍等!',
            type: 'warning',
          })
          return
        }
        if (activeVar + 1 === totalElements) {
          this.$message({
            message: '已经是最后一张图片了',
            type: 'warning',
          })
          return
        }
        activeVar += 1

        if (activeVar > content.length) this.$parent.getQueryRecentOneDayDamagesFun()
        else this.$parent.activeFun(content[activeVar], activeVar, 'next')
      }
    }, 250),
    openWindow(data) {
      const { url, position, title } = data
      const { leftShow, rightShow } = this.$parent
      const point = new BMap.Point(position.lng, position.lat)
      setInfoWindowViewport(this.map, point, leftShow, rightShow)
      this.diseaseWindow.url = url
      this.diseaseWindow.title = title
      this.diseaseWindow.position = position
      this.diseaseWindow.show = true
    },
    async handlePointClick({ point }, id) {
      return
      clearTimeout(pointClickDebounceTimer)

      pointClickDebounceTimer = setTimeout(async () => {
        try {
          if (typeof id !== 'number') id = point.id

          if (id !== locId) cacheDamagesObj = {} // 多次点击同一路段缓存不置空
          this.$refs.diseaseWindow.redraw()

          const params = {
            id,
            gpsLongitude: point.lng,
            gpsLatitude: point.lat,
          }
          locId = id

          this.mapLoading = true
          const {
            payload: { damages, locIndex: copyLocIndex, size },
          } = await getQueryDamagePictures(params)

          damagesList = damages
          locIndex = copyLocIndex
          locSize = size
          this.cacheImgData('loc') // 异步提前缓存该图片的前后几张,不会阻塞后边执行
          const data = await this.getImg(damagesList[locIndex], locIndex)
          this.diseaseWindow.url = data.url
          this.diseaseWindow.position = data.position
          this.diseaseWindow.title = data.title
          this.diseaseWindow.show = true
          this.mapLoading = false
        } catch (error) {
          this.mapLoading = false
        }
      }, 200)
    },
    // 查看大图
    handleShowBigImg() {
      this.viewerFlag = true
      this.initViewer()
    },
    initViewer() {
      const that = this
      if (that.viewer) {
        that.viewer.destroy()
      }
      that.viewer = viewerApi({
        images: [this.diseaseWindow.url],
        options: {
          // initialViewIndex: 1,
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: false, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: false, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: false, // 使用 CSS3 过度
          fullscreen: true, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
          moved(e) {
            console.log('moved')
            that.viewerMoveDetail = e.detail
          },
          zoomed(e) {
            console.log('zoomed')
            if (e.detail.originalEvent && e.detail.originalEvent.x) {
              that.viewerZoomDetail = {
                ratio: e.detail.ratio,
              }
            }
          },
          hidden(e) {
            console.log('hidden')
            that.viewer = null
            that.viewerFlag = false
            that.viewerMoveDetail = null
            that.viewerZoomDetail = null
          },
          viewed(e) {
            console.log('viewed')
            if (that.viewerZoomDetail) {
              that.viewer.zoomTo(that.viewerZoomDetail.ratio)
            }
            if (that.viewerMoveDetail) {
              that.viewer.moveTo(
                that.viewerMoveDetail.x,
                that.viewerMoveDetail.y,
              )
            }
          },
        },
      })
    },
    // 监听左右键实现上一张，下一张
    keyDown() {
      document.onkeydown = (e) => {
        if (!this.diseaseWindow.show) return
        // 事件对象兼容
        const e1 = e || window.event
        // 键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40
        if (e1 && e1.keyCode === 37) {
          // 按下左箭头
          this.handlePrev()
        } else if (e1 && e1.keyCode === 39) {
          // 按下右箭头
          this.handleNext()
        }
      }
    },
    // 设置运行中车辆的轨迹信息
    setRunningLineData(datas) {
      const that = this
      // console.log('轨迹')
      // console.log(datas)
      datas.forEach((data, index) => {
        const tempIndex = that.runningLineData.findIndex((item) => item.deviceKey === data.deviceKey)
        if (data.using) {
          const { deviceKey, gpsLatitude, gpsLongitude } = data
          if (tempIndex > -1) {
            that.runningLineData[tempIndex].line.push({ lng: gpsLongitude, lat: gpsLatitude })
          } else {
            that.runningLineData.push({
              deviceKey,
              line: [{ lng: gpsLongitude, lat: gpsLatitude }],
            })
          }
        } else if (tempIndex > -1) {
          that.runningLineData.splice(tempIndex, 1)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  position: relative;

  .animation-suoxiao {
    animation-name: zoomOut;
    animation-duration: 400ms;
    animation-fill-mode: forwards;
  }
  .animation-fangda {
    animation-name: zoomIn;
    animation-duration: 300ms;
    animation-fill-mode: forwards;
  }

  .tool-container {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 1;
  }

  .bm-view {
    width: 100%;
    height: 100%;

    // bm-info-window样式
    .disease-cont {
      width: auto;
      padding-top: 10px;
      .disease-img-box {
        border-radius: 6px;
        overflow: hidden;
        box-sizing: border-box;

        @media screen and (max-width: 1440px) {
          width: 500px;
          height: 281px;
        }
        @media screen and (max-width: 1280px) {
          width: 380px;
          height: 214px;
        }
        @media screen and (min-width: 1440px) {
          width: 700px;
          height: 392px;
        }
      }

      .empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #9b9b9b;
        border: 1px solid #ddd;
        border-radius: 6px;
      }
      .disease-img {
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
      .disease-img-error {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #9b9b9b;
        border-radius: 8px;
        color: #9b9b9b;
        padding: 0 10px;
        line-height: 1.2;
        @media screen and (max-width: 1280px) {
          font-size: 14px;
        }
      }
      .disease-deal-btns {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        .triangle {
          border: 6px solid transparent;
          position: absolute;
          bottom: -12px;
          left: 50%;
          transform: translateX(-50%);
          border-top-color: #e6a23c;
        }
      }
      .disease-correction {
        // width: 700px;
        @media screen and (max-width: 1440px) {
          width: 500px;
        }
        @media screen and (max-width: 1280px) {
          width: 380px;
        }
        @media screen and (min-width: 1440px) {
          width: 700px;
        }
        word-wrap: break-word;
        word-break: break-all;
        margin-top: 10px;
        .el-tag {
          margin-right: 5px;
          margin-bottom: 5px;
          cursor: pointer;
        }

        .el-button {
          margin-bottom: 10px;
          margin-left: 0;
          margin-right: 10px;
          &:hover {
            color: #e6a23c;
            background: #fdf6ec;
            border-color: #f5dab1;
          }
        }
        .el-button--warning {
          &:hover {
            color: #ffffff;
            background-color: #f90;
            border-color: #f90;
          }
        }
      }
      .disease-opn-btns {
        border-top: 1px dotted #dedede;
        padding-top: 5px;
        margin-top: 5px;
        text-align: right;
      }
    }
  }
  .bm-view_72 {
    width: 72%;
    max-width: calc(100% - 512px);
  }

  .inspection-panel {
    width: var(rightAsideWidth);
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    background: #f6f6f6;
    overflow: auto;
    padding: 10px;

    .card {
      position: relative;
      border-radius: 10px;
      height: calc(100vh - 120px);
      border: none;
      box-shadow: none;

      .title {
        display: flex;
        align-items: center;
        color: #394380;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 16px;
        i {
          color: #394380;
          font-size: 20px;
          margin-right: 10px;
        }
      }
      .info {
        font-size: 14px;
        color: #606266;
        padding-bottom: 12px;
        display: flex;
        .info-item {
          flex: 1;
          &:nth-child(2) {
            display: flex;
            justify-content: flex-end;
          }
        }
        p {
          display: flex;
          align-items: center;
        }

        span {
          width: 12px;
          height: 12px;
          display: inline-block;
          border-radius: 50%;
          margin-left: 10px;
        }
        .using {
          background: rgba(147, 229, 59);
        }
        .no-using {
          background: #909399;
        }
      }
      .el-icon-close {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #394380;
        cursor: pointer;
      }

      .pagination-container {
        padding-bottom: 0;
        padding-top: 20px;
      }
    }
  }

  // 图例
  .legend-cont {
    position: absolute;
    left: 10px;
    bottom: 10px;
    .legeng-btn {
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      border-radius: 50%;
      color: #fff;
      background: #2d8cf0;
      font-size: 14px;
      cursor: pointer;
    }
    .el-icon-question {
      color: #2d8cf0;
      font-size: 20px;
      cursor: pointer;
    }
    .legend-img {
      width: 200px;
      position: absolute;
      left: 0;
      bottom: 35px;
    }
  }

  .select-cont-outer {
    position: absolute;
    bottom: 17px;
    right: 375px;
    z-index: 2;

    ::v-deep.el-input__inner {
      width: 100px;
      height: 36px;
      border-radius: 40px;
      margin-left: 10px;
    }
  }
}

@keyframes slideOutRight {
  from {
    right: 375px;
  }

  to {
    right: 15px;
  }
}
@keyframes slideInRight {
  from {
    right: 15px;
  }

  to {
    right: 375px;
  }
}

.slideOutRight {
  animation-name: slideOutRight;
}
.slideInRight {
  animation-name: slideInRight;
}

</style>
