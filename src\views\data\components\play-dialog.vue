<!--  -->
<template>
  <el-dialog
    class="play-dialog"
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
  >
    <Video
      v-if="dialogVisible"
      ref="video"
      :page-query="pageQuery"
      speed="5"
      :task-data="dataObj"
      @updatePage="handleUpdatePageQuery"
    />
  </el-dialog>
</template>

<script>
import { getRoadPictures } from '@/api/data'
import Video from './video.vue'

export default {
  components: { Video },
  props: {
    dataObj: {
      type: Object,
      default: null,
    },
    diseaseArr: {
      type: Array,
      default: null,
    },
    type: { // 1 是道路回放，2是景观回放
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      pageQuery: {
        page: 0,
        size: 50,
      },
      getFun: null,
    }
  },

  computed: {},
  watch: {
    dialogVisible(val) {
      if (val) {
        this.initTitle()
        this.$nextTick(() => {
          this.$refs.video.initImgs()
        })
      } else {
        this.$refs.video.reset()
      }
    },
  },
  methods: {
    show() {
      this.dialogVisible = true
    },
    initTitle() {
      const { deviceName, startTime, endTime } = this.dataObj
      const parseStartTime = this.$time.parseTime(new Date(startTime), '{yyyy}-{mm}-{dd} {hh}:{ii}')
      const parseEndTime = this.$time.parseTime(new Date(endTime), '{yyyy}-{mm}-{dd} {hh}:{ii}')
      let str = ''
      if (this.type === 1) {
        str = '路段病害'
      } else if (this.type === 2) {
        str = '道路景观'
      }
      if (parseStartTime.split(' ')[0] === parseEndTime.split(' ')[0]) {
        // 同一天
        const day = parseStartTime.split(' ')[0]
        const time = `${parseStartTime.split(' ')[1]}-${parseEndTime.split(' ')[1]}`
        this.title = `${deviceName}在${day} ${time}时间段内的${str}实况`
      } else {
        this.title = `${deviceName}在${parseStartTime}至${parseEndTime}时间段内的${str}实况`
      }
    },
    async getPlaybackPicturesAjax() {
      const { deviceKey, startTime, endTime, id } = this.dataObj
      console.log('getPlaybackPicturesAjax', this.dataObj)
      const { page, size } = this.pageQuery
      const params = {
        deviceId: deviceKey,
        startTime,
        endTime,
        page,
        size,
        taskId: id,
      }
      if (this.type === 2) {
        params.frameType = 1
      }

      return getRoadPictures(params)
    },
    handleUpdatePageQuery(page) {
      console.log('handleUpdatePageQuery', page)
      this.pageQuery.page = page
    },
  },
}

</script>
<style lang='scss' scoped>
  .play-dialog {
    ::v-deep .el-dialog{
      width: 940px;

      @media screen and (max-width: 1280px){
        width: 700px;
      }
    }

  }
  ::v-deep .video-container {
    .video-box {
      height: 507px;

      @media screen and (max-width: 1280px){
        height: 370px;
      }
    }
  }
</style>
