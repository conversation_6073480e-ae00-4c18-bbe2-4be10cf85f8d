export default {
  methods: {
    getLayerTypeTitle(modelIdentifyType) {
      return this.mapLayerType[modelIdentifyType].title
    },

    formatArea(area) {
      return area ? area.toFixed(3) : '-'
    },

    getTitle(modelIdentifyType, type, area, address) {
      const typeMap = this.allIdentifyTypeMap || this.$parent.allIdentifyTypeMap || {}
      const typeName = typeMap[type] || '-'
      const areaText = `面积: ${this.formatArea(area)}㎡`
      return `${this.getLayerTypeTitle(modelIdentifyType)}: ${typeName} ${areaText} 道路: ${address || '-'}`
    },
  },
}
