<template>
  <div class="not-home-container">
    <canvas
      id="canvas"
      :width="canvasWidth"
      :height="canvasHeight"
      style="display: none"
    />
    <Map ref="map" :right-show="rightShow" :left-show="leftShow" />
    <div
      ref="slideLeft"
      class="slide-left animate__animated animate__fast"
      :class="{
        animate__slideOutLeft: !leftShow,
        animate__slideInLeft: leftShow,
      }"
    >
      <div class="daily-inspection">
        <Car id="car1" style="left: -44px;" />
        <div class="title">
          <span
            class="title-left"
          >巡检日报（{{ todayInspectData.startDate }}）</span>
          <span
            class="title-right"
            @click="handelSlideShow('left')"
          >收起 &lt;&lt;</span>
        </div>
        <div class="daily-inspection-main">
          <span>
            <img src="@/assets/home/<USER>" alt="">巡检车辆：{{
              todayInspectData.carNum
            }}
          </span>
          <span>
            <img src="@/assets/home/<USER>" alt="">巡检总里程(km)：{{
              todayInspectData.totalMileage
            }}
          </span>
          <span>
            <img src="@/assets/home/<USER>" alt="">发现重大病害(处)：{{
              todayInspectData.totalDamageCount
            }}
          </span>
          <span>
            <img
              src="@/assets/home/<USER>"
              alt=""
            >发现道路风险点：{{ todayInspectData.totalRiskCount }}
          </span>
        </div>
        <div class="daily-inspection-btns">
          <div style="visibility: hidden;">
            <img src="@/assets/home/<USER>" alt="">
            <span>详情</span>
          </div>
          <div @click="onExportDailyInspection">
            <img src="@/assets/home/<USER>" alt="">
            <span>下载</span>
          </div>
          <div @click="onMoreDailyInspection">
            <img src="@/assets/home/<USER>" alt="">
            <span>更多</span>
          </div>
        </div>
      </div>
      <div class="inspection-list">
        <div class="title">
          <span class="title-left">路面巡查列表</span>
        </div>
        <div
          ref="inspectionList"
          v-infinite-scroll="getQueryRecentOneDayDamagesFun"
          class="inspection-list-main"
          infinite-scroll-disabled="disabled"
          infinite-scroll-immediate="false"
          :infinite-scroll-distance="20"
        >
          <div
            v-for="(item, index) in inspectionData.content"
            :key="item.id"
            class="inspection-list-main-item"
            :class="{ activeCss: activeVar === index }"
            @click="activeFun(item, index)"
          >
            <span v-show="activeVar === index" class="inspection-list-main-item-one" />
            <span class="inspection-list-main-item-left">
              <template v-if="item.picState === 1">
                <img
                  v-show="!item.loading"
                  :src="item.imgSrc"
                  @load="loadImg(item, index)"
                  @error="loadImg(item, index)"
                >
                <div v-if="item.loading" class="loading-container">
                  <i class="el-icon-loading" style="font-size: 20px;"></i>
                </div>
              </template>
              <span
                v-else-if="item.picState === 2"
                class="empty-div"
              >图片已归档，请联系管理员解冻</span>
              <span v-else class="empty-div">
                您查看的图片时间点是：{{
                  item.photoTime ? item.photoTime.replace("T", " ") : "未知"
                }}，图片续传到达时间：{{
                  item.deviceState.schedulerFrameFileTimestamp
                    ? item.deviceState.schedulerFrameFileTimestamp.replace(
                      "T",
                      " "
                    )
                    : "未知"
                }}，还有{{
                  item.deviceState.frameFileFailureNums || "未知"
                }}张图片尚未续传成功，请耐心等待。
              </span>
            </span>
            <span class="inspection-list-main-item-right">
              <span>{{ getLayerTypeTitle(item.modelIdentifyType) || '-' }}：{{
                allIdentifyTypeMap[item.type] || '-'
              }}</span>
              <span>{{ getArea(item) }}</span>
              <span>道路：{{ item.address || '-' }}</span>
            </span>
          </div>
          <p
            v-if="inspectionData.loading"
            class="text-loding"
            style="color: #2d8cf0"
          >
            加载中...
          </p>
          <p v-else-if="noMore" class="text-loding">
            {{ inspectionData.totalElements ? "没有更多了" : "暂无数据" }}
          </p>
        </div>
      </div>
    </div>
    <div
      class="slide-right animate__animated animate__fast"
      :class="{
        animate__slideOutRight: !rightShow,
        animate__slideInRight: rightShow,
      }"
    >
      <span class="slide-right-title">
        <span class="title-left">数据分析</span>
        <span
          class="title-right"
          @click="handelSlideShow('right')"
        >收起 &gt;&gt;</span>
      </span>
      <div class="data-analysis">
        <Car id="car2" style="left: -44px;" />
        <div class="title">
          <span class="title-left">概况</span>
        </div>
        <div class="start-time">巡检起始时间：{{ dataAnalysis.startDate }}</div>
        <div class="total-mileage-time">
          <span class="total-mileage">
            <span class="total-label">总巡检里程</span>
            <span class="total-value">
              <CountTo
                class=""
                :style="{
                  'font-size':
                    dataAnalysis.totalMileage.length > 8 ? '20px' : '26px',
                }"
                :start-val="0"
                :end-val="dataAnalysis.totalMileage"
                :duration="3000"
                :decimals="2"
                separator=""
              />
              <i
                :style="{
                  'line-height':
                    dataAnalysis.totalMileage.length > 8 ? '44px' : '48px',
                }"
              >&nbsp;km</i>
            </span>
          </span>
          <span class="total-time">
            <span class="total-label">总巡检时长</span>
            <span class="total-value">
              <CountTo
                class=""
                :style="{
                  'font-size':
                    dataAnalysis.totalHours.length > 8 ? '20px' : '26px',
                }"
                :start-val="0"
                :end-val="dataAnalysis.totalHours"
                :duration="3000"
                :decimals="2"
                separator=""
              />
              <i
                :style="{
                  'line-height':
                    dataAnalysis.totalHours.length > 8 ? '44px' : '48px',
                }"
              >&nbsp;h</i>
            </span>
          </span>
        </div>
        <!-- <div class="progress">
          <div class="progress-item">
            <span class="progress-item-label">重大病害：</span>
            <span class="progress-item-progress"><el-progress :text-inside="true" :stroke-width="16" :percentage="0"></el-progress></span>
            <span class="progress-item-value">0/{{dataAnalysis.totalDamageCount}}</span>
          </div>
          <div class="progress-item">
            <span class="progress-item-label">沿线设施损坏：</span>
            <span class="progress-item-progress"><el-progress :text-inside="true" :stroke-width="16" :percentage="0"></el-progress></span>
            <span class="progress-item-value">0/{{dataAnalysis.totalRoadsideFacilitiesCount}}</span>
          </div>
          <div class="progress-item">
            <span class="progress-item-label">安全风险：</span>
            <span class="progress-item-progress"><el-progress :text-inside="true" :stroke-width="16" :percentage="0"></el-progress></span>
            <span class="progress-item-value">0/{{dataAnalysis.totalRiskCount}}</span>
          </div>
        </div> -->
      </div>

      <div class="daily-inspect">
        <Car id="car3" style="right: -44px;transform:scale(0.5) rotateY(180deg);" />
        <div class="title">
          <span class="title-left">一周巡检里程变化图</span>

          <!-- <span class="title-right">查看更多 ></span> -->
        </div>
        <div class="echarts">
          <Echarts
            id="dailyInspect"
            ref="dailyInspect"
            :data="mileage_option"
            style="width: 100%; height: 100%"
          />
        </div>
      </div>
      <div class="daily-inspect">
        <Car id="car4" style="left: -44px;" />
        <div class="title">
          <span class="title-left">一周病害、风险发现量变化图</span>
          <!-- <span class="title-right">查看更多 ></span> -->
        </div>
        <div class="echarts">
          <Echarts
            id="discovery"
            ref="discovery"
            :data="discovery_option"
            style="width: 100%; height: 100%"
          />
        </div>
      </div>
      <div class="daily-inspect">
        <div class="title">
          <span class="title-left">病害分类占比 </span>
          <!-- <span class="title-right">查看更多 ></span> -->
          <span class="title-right">
            <el-select
              v-model="diseaseProportionModel"
              placeholder="请选择"
              style="width: 105px"
              @change="handelSwitch"
            >
              <el-option
                v-for="item in diseaseProportionOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </span>
        </div>

        <div class="echarts">
          <Echarts
            id="classification"
            ref="classification"
            :data="classification_option"
            style="width: 100%; height: 100%"
          />
        </div>
      </div>
    </div>

    <span
      class="left-show animate__animated"
      :class="leftClass"
      @click="handelSlideShow('left')"
    >
      <span />
    </span>
    <span
      class="right-show animate__animated"
      :class="rightClass"
      @click="handelSlideShow('right')"
    >
      <span />
    </span>

    <div
      ref="floating"
      class="floating animate__animated animate__fast"
      :class="{ slideOutRight: !rightShow, slideInRight: rightShow }"
    >
      <span class="map-layer-floating">
        <span
          v-for="(item, index) in mapLayerFloating"
          :key="item.label"
          class="map-layer-floating-item"
          @click="handelMapLayer(index, item.modelIdentifyType)"
        >
          <img :src="item.src" :alt="item.label">
          <p :style="{ color: item.color }">{{ item.label }}</p>
        </span>
      </span>
      <span class="setting" @click="handelSettingDialogShow">
        <img :src="settingImg" alt="设置">
        <p>设置</p>
      </span>
    </div>

    <SettingDialog ref="settingDialog" :all-identify-type="allIdentifyType" @settingParams="handelSetTypes" />
  </div>
</template>

<script>
// import Worker from '../../../../public/test.worker'; // this.worker = new Worker(); 方式才需要引入
import {
  getSummaryRecentInspectData,
  getSummaryRecentOneDayInspectData,
  getQueryRecentOneDayDamages,
  cancelRequest,
} from '@/api/home'
import { exportFile } from '@/utils/index'

import CanvasMixin from '@/mixin/canvas'
import CountTo from 'vue-count-to'


import settingImg from '@/assets/home/<USER>'

import Car from '@/components/CarLoading.vue'
import EchartsMixin from '../mixin/echarts'

import Map from './map.vue'
import SettingDialog from './setting.vue'
import Echarts from './echarts.vue'
import { setMarqueeAnimation } from '../utils/factory'

const historyModelIdentifyType = ['all']
let legendTimer; let
  classificationTimer
let once = true

const MAP_LAYER_IMAGES = {
  ALL: {
    default: require('@/assets/home/<USER>'),
    active: require('@/assets/home/<USER>')
  },
  DISEASE: {
    default: require('@/assets/home/<USER>'),
    active: require('@/assets/home/<USER>')
  },
  FACILITY: {
    default: require('@/assets/home/<USER>'), 
    active: require('@/assets/home/<USER>')
  },
  RISK: {
    default: require('@/assets/home/<USER>'),
    active: require('@/assets/home/<USER>')
  },
  ASSET: {
    default: require('@/assets/home/<USER>'),
    active: require('@/assets/home/<USER>')
  }
}

// 添加节流函数
function throttle(fn, delay) {
  let timer = null
  let lastTime = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      fn.apply(this, args)
      lastTime = now
    } else if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, args)
        lastTime = Date.now()
        timer = null
      }, delay)
    }
  }
}

export default {
  name: 'NotAdmin',
  components: {
    Echarts,
    Map,
    SettingDialog,
    CountTo,
    Car,
  },
  mixins: [EchartsMixin, CanvasMixin],
  data() {
    return {
      worker: null,
      settingImg,
      imagePromise: [],
      concretePavementList: [],
      canvas: null,
      ctx: null,
      canvasWidth: 640,
      canvasHeight: 360,
      activeVar: null,
      diseaseProportionModel: 'asphaltRoad',
      diseaseProportionEchartData: { // 路面病害分类图表数据
        asphaltRoad: [],
        concreteRoad: [],
        sandstoneRoad: [], // 砂石
        slowRoad: [], // 慢行
        roadAssets: [],
        roadRisk: [],
        roadsideFacilities: [],
      },
      diseaseProportionOption: [
        {
          label: '沥青路面病害',
          value: 'asphaltRoad',
        },
        {
          label: '水泥路面病害',
          value: 'concreteRoad',
        },
        {
          label: '砂石路面病害',
          value: 'sandstoneRoad',
        },
        {
          label: '慢行病害',
          value: 'slowRoad',
        },
        {
          label: '道路资产',
          value: 'roadAssets',
        },
        {
          label: '路面风险',
          value: 'roadRisk',
        },
        {
          label: '沿线设施损坏',
          value: 'roadsideFacilities',
        },
      ],
      inspectionData: {
        params: {
          modelIdentifyType: 1,
          types: [], // []
          area: null,
          page: -1,
          size: 10,
        },
        content: [],
        loading: false,
        totalPages: null,
        totalElements: null,
      },
      leftShow: true,
      rightShow: true,
      opacity: 0,
      todayInspectData: {},
      animatedClass: 'animation-fangda',
      allIdentifyTypeMap: {},
      dataAnalysis: {
        startDate: '', // 巡检起始日期
        totalMileage: 0, // 总巡检里程
        totalHours: 0, // 总巡检时长
        totalDamageCount: 0, // 重大病害总数
        totalRoadAssetCount: 0, // 道路资产总数
        totalRiskCount: 0, // 安全风险总数
        totalRoadsideFacilitiesCount: 0, // 沿线设施损坏总数
      },
      mapLayerFloating: [
        {
          label: '全部',
          modelIdentifyType: 'all',
          ...this.getLayerConfig(MAP_LAYER_IMAGES.ALL)
        },
        {
          label: '病害',
          modelIdentifyType: 1,
          ...this.getLayerConfig(MAP_LAYER_IMAGES.DISEASE, true) // 默认激活
        },
        {
          label: '设施', 
          modelIdentifyType: 5,
          ...this.getLayerConfig(MAP_LAYER_IMAGES.FACILITY)
        },
        {
          label: '风险',
          modelIdentifyType: 3,
          ...this.getLayerConfig(MAP_LAYER_IMAGES.RISK)
        },
        {
          label: '资产',
          modelIdentifyType: 2,
          ...this.getLayerConfig(MAP_LAYER_IMAGES.ASSET)
        }
      ],
      imageWorker: null,
      isWorkerSupported: false, // 添加标志判断是否支持 Worker
      imageQueue: [], // 添加图片队列
      isProcessingQueue: false, // 是否正在处理队列
    }
  },
  computed: {
    leftClass() {
      if (this.leftShow) return ['animate__slideOutLeft']
      return ['animate__slideInLeft']
    },
    rightClass() {
      if (this.rightShow) return ['animate__slideOutRight']
      return ['animate__slideInRight']
    },
    disabled() {
      return this.inspectionData.loading || this.noMore
    },
    noMore() {
      return (
        this.inspectionData.content.length >= this.inspectionData.totalElements
      )
    },
  },
  watch: {},
  async created() {
    const that = this
    await that.getModelIdentifyTypesAll()

    for (let index = 0; index < that.allIdentifyType.length; index++) {
      const identifyType = that.allIdentifyType[index]
      this.allIdentifyTypeMap[identifyType.engName] = identifyType.chineseName
    }

    this.getQueryRecentOneDayDamagesFun()
    await this.handelGetSummaryRecentInspectData()
    this.echartsAutoScroll()

    const data = await getSummaryRecentOneDayInspectData()
    this.todayInspectData = data.payload

    // 初始化 Worker
    this.initImageWorker()
  },
  mounted() {
    if (document.body.clientHeight < 856) {
      const that = this
      that.mileage_option.grid.height = '50%'
      that.discovery_option.grid.height = '50%'
    }

    this.$nextTick(() => {
      // 初始化页面在左侧slide动画执行完成在执行跑马灯动画
      this.$refs.slideLeft.addEventListener('animationend', () => {
        if (!once) return
        once = false
        setTimeout(() => {
          setMarqueeAnimation()
        }, 500)
      })
    })

    // 添加滚动监听
    this.$refs.inspectionList.addEventListener('scroll', this.handleScroll)
  },
  destroyed() {
    if (classificationTimer && legendTimer) {
      clearInterval(classificationTimer)
      clearInterval(legendTimer)
    }
    // 清理 Worker
    if (this.imageWorker) {
      this.imageWorker.terminate()
    }
  },
  methods: {
    loadImg(item, index) {
      // 强制更新整个对象以确保视图更新
      this.$set(this.inspectionData.content, index, {
        ...item,
        loading: false
      });
    },
    // 当日巡检日报 - 下载
    onExportDailyInspection() {
      const { workUnitId, startDate } = this.todayInspectData
      // 日期格式为'{yyyy}/{mm}/{dd}'，不然会报错
      const analysisDate = startDate.replaceAll('-', '/')
      exportFile(`inspect-data-daily-analyses/excel?workUnitId=${workUnitId}&analysisDate=${analysisDate}`)
    },
    // 当日巡检日报 - 更多
    onMoreDailyInspection() {
      this.$router.push('/inspection-report')
    },
    // 添加工具函数
    formatDate(date) {
      return date.slice(5) // 处理时间格式 2023-10-01 -> 10-01
    },
    // 添加工具函数
    updateChartData(option, data, formatter) {
      option.series[0].data = []
      option.xAxis[0].data = []
      data.forEach(item => {
        option.series[0].data.push(formatter(item))
        option.xAxis[0].data.push(this.formatDate(item.inspectDate))
      })
    },
    async handelGetSummaryRecentInspectData() {
      const {
        payload: {
          dailyInspectDataList,
          // 病害分类数据
          asphaltRoadDamageCountList,
          concreteRoadDamageCountList,
          sandstoneRoadDamageCountList,
          slowRoadDamageCountList,
          roadAssetCountList,
          roadRiskCountList,
          roadsideFacilitiesDamageCountList,
          // 基础数据
          startDate,
          totalMileage,
          totalHours,
          totalDamageCount,
          totalRoadAssetCount, 
          totalRiskCount,
          totalRoadsideFacilitiesCount,
        },
      } = await getSummaryRecentInspectData()

      // 更新基础数据
      Object.assign(this.dataAnalysis, {
        startDate,
        totalMileage,
        totalHours,
        totalDamageCount,
        totalRoadAssetCount,
        totalRiskCount,
        totalRoadsideFacilitiesCount
      })

      // 更新病害分类数据
      const diseaseTypeMap = {
        asphaltRoad: asphaltRoadDamageCountList,
        concreteRoad: concreteRoadDamageCountList,
        sandstoneRoad: sandstoneRoadDamageCountList,
        slowRoad: slowRoadDamageCountList,
        roadAssets: roadAssetCountList,
        roadRisk: roadRiskCountList,
        roadsideFacilities: roadsideFacilitiesDamageCountList
      }

      Object.entries(diseaseTypeMap).forEach(([key, list]) => {
        this.setClassificationSeries(list, key)
      })

      // 更新里程图表
      this.updateChartData(
        this.mileage_option, 
        dailyInspectDataList,
        item => item.totalMileage
      )

      // 更新发现量图表
      this.discovery_option.series[0].data = []
      this.discovery_option.series[1].data = []
      this.discovery_option.xAxis[0].data = []
      
      dailyInspectDataList.forEach(item => {
        this.discovery_option.series[0].data.push(item.totalDamageCount)
        this.discovery_option.series[1].data.push(item.totalRiskCount)
        this.discovery_option.xAxis[0].data.push(this.formatDate(item.inspectDate))
      })
    },
    echartsAutoScroll() {
      const that = this
      let currentIndex = -1
      let currentPage = 0
      let execute = true // 鼠标移入 停止轮播
      const { chartGraph } = this.$refs.classification
      const dataLen = that.classification_option.series[0].data.length
      if (!chartGraph && dataLen === 0) {
        if (classificationTimer && legendTimer) {
          clearInterval(classificationTimer)
          clearInterval(legendTimer)
        }
        return
      }
      classificationTimer = setInterval(() => {
        if (!execute) return
        if (dataLen < 2) clearInterval(classificationTimer)
        // 取消之前高亮的图形
        chartGraph.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
        currentIndex = (currentIndex + 1) % dataLen
        // 高亮当前图形
        chartGraph.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: currentIndex,
        })
      }, 1000)

      legendTimer = setInterval(() => {
        if (!execute) return
        if (dataLen < 5) clearInterval(legendTimer)
        currentPage += 3
        if (currentPage >= dataLen - 1) {
          currentPage = 0
        }
        chartGraph.dispatchAction({
          type: 'legendScroll',
          scrollDataIndex: currentPage,
        })
      }, 3000)

      chartGraph.on('mousemove', (params) => {
        if (params.dataIndex !== currentIndex) {
          chartGraph.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: currentIndex,
          })
        }
      })
      chartGraph.getZr().on('mousemove', () => {
        execute = false
      })
      chartGraph.getZr().on('mouseout', () => {
        execute = true
      })
    },
    getLayerTypeTitle(modelIdentifyType) {
      return this.$refs.map.getLayerTypeTitle(modelIdentifyType) || '病害类型'
    },
    getArea(item) {
      if(item.modelIdentifyType === 1) {
        // 病害显示 ㎡
        return `面积：${item.area.toFixed(3)}㎡`
      } else {
        return `数量：1处`
      }
    },
    setClassificationSeries(array, type) {
      const gatherArr = []
      array.forEach(({ type, damageCount }) => {
        if (damageCount) {
          const tempObj = {
            value: damageCount,
            name: type,
          }
          gatherArr.push(tempObj)
        }
      })
      this.diseaseProportionEchartData[type] = gatherArr
      if (type === 'asphaltRoad') {
        const that = this
        that.classification_option.series[0].data = gatherArr
      }
    },
    handelSwitch(type) {
      const that = this
      const { chartGraph } = this.$refs.classification
      chartGraph.clear()
      that.classification_option.series[0].data = this.diseaseProportionEchartData[type]
      chartGraph.setOption(that.classification_option)
      clearInterval(classificationTimer)
      clearInterval(legendTimer)
      this.echartsAutoScroll()
    },
    handelSlideShow(type) {
      if (type === 'left') {
        this.leftShow = !this.leftShow
        leftSlideShow = this.leftShow
      } else {
        this.rightShow = !this.rightShow
        rightSlideShow = this.rightShow
        this.animatedClass = 'animation-suoxiao'
      }
    },
    async getQueryRecentOneDayDamagesFun() {
      try {
        this.inspectionData.loading = true
        this.inspectionData.params.page += 1
        const params = { ...this.inspectionData.params }
        if (params.modelIdentifyType === 'all') delete params.modelIdentifyType

        const {
          payload: { content, totalPages, totalElements },
        } = await getQueryRecentOneDayDamages(params)

        this.inspectionData.totalElements = totalElements
        this.inspectionData.totalPages = totalPages
        
        if (content.length) {
          const newContent = [...this.inspectionData.content, ...content]
          this.$set(this.inspectionData, 'content', newContent)
          
          const startIndex = this.inspectionData.content.length - content.length
          
          // 将新的图片添加到队列
          content.forEach((item, i) => {
            item.loading = true
            if (item.picState === 1) {
              this.imageQueue.push({
                item,
                index: startIndex + i
              })
            }
          })
          
          // 开始处理队列
          this.processImageQueue()
        } else {
          this.inspectionData.content = []
        }
        
        this.inspectionData.loading = false
      } catch (error) {
        console.error('Failed to fetch damages:', error)
        this.inspectionData.loading = false
      }
    },
    // 修改图片处理方法
    loadAndProcessImage(item) {
      return new Promise((resolve) => {
        const image = new Image()
        image.src = item.pictureUrl
        image.setAttribute('crossOrigin', 'anonymous')
        
        image.onload = async () => {
          try {
            if (this.isWorkerSupported && this.imageWorker) {
              const response = await fetch(item.pictureUrl)
              const imageBlob = await response.blob()
              
              this.imageWorker.postMessage({
                imageData: imageBlob,
                type: item.type,
                coordinate: item.coordinate,
                width: image.width,
                height: image.height
              })
              
              this.imageWorker.onmessage = (e) => {
                const { processedImageUrl, error } = e.data
                if (error) {
                  console.error('Worker processing failed:', error)
                  resolve(null)
                  return
                }
                this.imagePromise.push(image)
                resolve(processedImageUrl)
              }
            } else {
              // 降级处理：使用主线程处理图片
              this.getCtx(image, 'canvas', image.width, image.height)
              if (!this.canvas || !this.ctx) {
                resolve(null)
                return
              }
              
              this.ctx.drawImage(image, 0, 0, image.width, image.height)
              this.ctxDraw(item.type, item.coordinate)
              
              const imgSrc = this.canvas.toDataURL()
              this.imagePromise.push(image)
              resolve(imgSrc)
            }
          } catch (error) {
            console.error('Image processing failed:', error)
            resolve(null)
          }
        }
        
        image.onerror = () => {
          console.error('Image failed to load:', item.pictureUrl)
          resolve(null)
        }
      })
    },
    async processImageQueue() {
      if (this.isProcessingQueue) return
      this.isProcessingQueue = true

      try {
        // 检查队列和内容是否还存在
        if (!this.imageQueue.length || !this.inspectionData.content) {
          this.isProcessingQueue = false
          return
        }

        // 找出所有在预加载区域内的图片
        const visibleItems = this.imageQueue.filter(({ index }) => {
          const element = this.$refs.inspectionList?.childNodes[index]
          return element && this.isElementInViewport(element)
        })

        // 优先处理可见区域的图片
        for (const { item, index } of visibleItems) {
          try {
            // 检查内容是否还存在
            if (!this.inspectionData.content[index]) {
              continue
            }

            const imgSrc = await this.loadAndProcessImage(item)
            if (imgSrc && this.inspectionData.content[index]) {
              this.$set(this.inspectionData.content[index], 'imgSrc', imgSrc)
            }
            
            // 从队列中移除已处理的项
            const queueIndex = this.imageQueue.findIndex(q => q.index === index)
            if (queueIndex > -1) {
              this.imageQueue.splice(queueIndex, 1)
            }
          } catch (error) {
            console.error('Failed to process image:', error)
          }
        }

        // 如果队列中还有项，设置一个定时器继续处理
        if (this.imageQueue.length > 0 && this.inspectionData.content) {
          setTimeout(() => {
            this.isProcessingQueue = false
            this.processImageQueue()
          }, 100)
        } else {
          this.isProcessingQueue = false
        }
      } catch (error) {
        console.error('Queue processing error:', error)
        this.isProcessingQueue = false
      }
    },
    // 修改检查元素是否在视口中的方法，增加预加载区域
    isElementInViewport(el) {
      const rect = el.getBoundingClientRect()
      const buffer = window.innerHeight // 增加一个屏幕高度的缓冲区
      return (
        rect.top >= -buffer && // 向上扩展一个屏幕高度
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight + buffer) && // 向下扩展一个屏幕高度
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      )
    },
    async handelMapLayer(index, modelIdentifyType) {
      // 避免重复点击
      const type = `${modelIdentifyType}`
      console.log('type', type)

      if (historyModelIdentifyType.length >= 2) {
        if (type === historyModelIdentifyType[historyModelIdentifyType.length - 1]) return
        historyModelIdentifyType.shift()
      }
      this.activeVar = null

      historyModelIdentifyType.push(`${modelIdentifyType}`)

      // 取消之前的请求
      cancelRequest()
      
      // 清理之前的图片加载
      this.imagePromise.forEach((image) => {
        image.onload = null
      })
      this.imagePromise = []
      
      // 清空图片处理队列
      this.imageQueue = []
      this.isProcessingQueue = false
      
      // 重置数据
      this.resetinspectionData()

      // 更新地图图层显示状态
      this.mapLayerFloating.forEach((item) => {
        item.src = item.defaultSrc
        item.color = item.defaultColor
      })
      this.mapLayerFloating[index].src = this.mapLayerFloating[index].activeSrc
      this.mapLayerFloating[index].color = this.mapLayerFloating[index].activeColor

      // 更新查询参数并获取新数据
      this.inspectionData.params.modelIdentifyType = modelIdentifyType
      await this.getQueryRecentOneDayDamagesFun()
      this.$refs.map.markerClusterVisibility(modelIdentifyType)
    },
    activeFun(item, index, scrollType = '') {
      this.videoUrl = ''
      this.$refs.map.isIconClick = false
      this.activeVar = index

      if (scrollType && this.$refs.inspectionList) {
        const { top } = this.$refs.inspectionList.childNodes[index].getBoundingClientRect()
        if (scrollType === 'prev' && top < 341) {
          this.$refs.inspectionList.scrollTop -= 108
        } else if (scrollType === 'next' && top > 773) {
          this.$refs.inspectionList.scrollTop += 108
        }
      }

      const {
        imgSrc,
        modelIdentifyType,
        type,
        address,
        picState,
        gpsLongitude,
        gpsLatitude,
      } = item
      
      const data = {
        url: imgSrc || picState,
        title: `${this.getLayerTypeTitle(modelIdentifyType)}: ${
          this.allIdentifyTypeMap[type] || '-'
        }  ${this.getArea(item)}  道路: ${address || '-'}`,
        picState,
        position: {
          lng: gpsLongitude,
          lat: gpsLatitude,
        },
      }

      this.$refs.map.openWindow(data)
    },
    handelSettingDialogShow() {
      this.$refs.settingDialog.dialogVisible = true
    },
    handelSetTypes({ types, area }) {
      this.inspectionData.params.types = types.length ? `${types}` : null
      this.inspectionData.params.area = area
      this.resetinspectionData()
      this.getQueryRecentOneDayDamagesFun()
      const params = {
        types: this.inspectionData.params.types,
        area: this.inspectionData.params.area,
      }
      this.$refs.map.handleGetQueryRecentOneDayDamages(params)
    },
    resetinspectionData() {
      this.inspectionData.content = []
      this.inspectionData.params.page = -1
      this.inspectionData.totalPages = null
    },
    getLayerConfig(images, isActive = false) {
      return {
        src: isActive ? images.active : images.default,
        defaultSrc: images.default,
        activeSrc: images.active,
        color: isActive ? '#333333' : 'rgba(51, 51, 51, .5)',
        defaultColor: 'rgba(51, 51, 51, .5)',
        activeColor: '#333333'
      }
    },
    // 添加 Worker 初始化方法
    initImageWorker() {
      try {
        if (typeof Worker !== 'undefined' && typeof OffscreenCanvas !== 'undefined') {
          this.imageWorker = new Worker('/static/workers/imageProcessor.worker.js')
          this.isWorkerSupported = true
          console.log('Worker initialized successfully')
        } else {
          console.warn('Web Workers or OffscreenCanvas not supported')
          this.isWorkerSupported = false
        }
      } catch (error) {
        console.error('Failed to initialize Worker:', error)
        this.isWorkerSupported = false
      }
    },
    // 修改图片处理方法
    loadAndProcessImage(item) {
      return new Promise((resolve) => {
        const image = new Image()
        image.src = item.pictureUrl
        image.setAttribute('crossOrigin', 'anonymous')
        
        image.onload = async () => {
          try {
            if (this.isWorkerSupported && this.imageWorker) {
              const response = await fetch(item.pictureUrl)
              const imageBlob = await response.blob()
              
              this.imageWorker.postMessage({
                imageData: imageBlob,
                type: item.type,
                coordinate: item.coordinate,
                width: image.width,
                height: image.height,
                allIdentifyType: this.allIdentifyType
              })
              
              this.imageWorker.onmessage = (e) => {
                const { processedImageUrl, error } = e.data
                if (error) {
                  console.error('Worker processing failed:', error)
                  resolve(null)
                  return
                }
                this.imagePromise.push(image)
                resolve(processedImageUrl)
              }
            } else {
              // 降级处理：使用主线程处理图片
              this.getCtx(image, 'canvas', image.width, image.height)
              if (!this.canvas || !this.ctx) {
                resolve(null)
                return
              }
              
              this.ctx.drawImage(image, 0, 0, image.width, image.height)
              this.ctxDraw(item.type, item.coordinate)
              
              const imgSrc = this.canvas.toDataURL()
              this.imagePromise.push(image)
              resolve(imgSrc)
            }
          } catch (error) {
            console.error('Image processing failed:', error)
            resolve(null)
          }
        }
        
        image.onerror = () => {
          console.error('Image failed to load:', item.pictureUrl)
          resolve(null)
        }
      })
    },
    // 修改队列处理方法，优化处理逻辑
    async processImageQueue() {
      if (this.isProcessingQueue) return
      this.isProcessingQueue = true

      try {
        // 检查队列和内容是否还存在
        if (!this.imageQueue.length || !this.inspectionData.content) {
          this.isProcessingQueue = false
          return
        }

        // 找出所有在预加载区域内的图片
        const visibleItems = this.imageQueue.filter(({ index }) => {
          const element = this.$refs.inspectionList?.childNodes[index]
          return element && this.isElementInViewport(element)
        })

        // 优先处理可见区域的图片
        for (const { item, index } of visibleItems) {
          try {
            // 检查内容是否还存在
            if (!this.inspectionData.content[index]) {
              continue
            }

            const imgSrc = await this.loadAndProcessImage(item)
            if (imgSrc && this.inspectionData.content[index]) {
              this.$set(this.inspectionData.content[index], 'imgSrc', imgSrc)
            }
            
            // 从队列中移除已处理的项
            const queueIndex = this.imageQueue.findIndex(q => q.index === index)
            if (queueIndex > -1) {
              this.imageQueue.splice(queueIndex, 1)
            }
          } catch (error) {
            console.error('Failed to process image:', error)
          }
        }

        // 如果队列中还有项，设置一个定时器继续处理
        if (this.imageQueue.length > 0 && this.inspectionData.content) {
          setTimeout(() => {
            this.isProcessingQueue = false
            this.processImageQueue()
          }, 100)
        } else {
          this.isProcessingQueue = false
        }
      } catch (error) {
        console.error('Queue processing error:', error)
        this.isProcessingQueue = false
      }
    },
    // 修改滚动处理方法，优化触发时机
    handleScroll: throttle(function() {
      if (this.imageQueue.length > 0) {
        requestAnimationFrame(() => {
          this.processImageQueue()
        })
      }
    }, 100) // 减少节流时间以提高响应性
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';
$position-top: 16px;
$bottom-box-height: 60px;
$left-right-width: 360px;

@keyframes marqueeAnimationStart {
  from {
    left: -332px;
  }
  to {
    left: 100%;
  }
}
@keyframes marqueeAnimationEnd {
  from {
    right: -332px;
  }
  to {
    right: 100%;
  }
}
@keyframes carStart {
  from {
    left: -44px;
  }
  to {
    left: 102%;
  }
}
@keyframes carEnd {
  from {
    right: -44px;
  }
  to {
    right: 102%;
  }
}

.car_start {
  animation-name: carStart;
  animation-duration: 5s;
  animation-fill-mode: forwards;
  animation-timing-function: linear;
}
.car_end {
  animation-name: carEnd;
  animation-duration: 5s;
  animation-fill-mode: forwards;
  animation-timing-function: linear;
}

@keyframes slideOutRight {
  from {
    right: 375px;
  }
  to {
    right: 15px;
  }
}
@keyframes slideInRight {
  from {
    right: 15px;
  }

  to {
    right: 375px;
  }
}

@keyframes loginLeft {
	0% {
		left: -100%;
	}
	50%,
	100% {
		left: 100%;
	}
}
@keyframes loginTop {
	0% {
		top: -100%;
	}
	50%,
	100% {
		top: 100%;
	}
}
@keyframes loginRight {
	0% {
		right: -100%;
	}
	50%,
	100% {
		right: 100%;
	}
}
@keyframes loginBottom {
	0% {
		bottom: -100%;
	}
	50%,
	100% {
		bottom: 100%;
	}
}

.slideOutRight {
  animation-name: slideOutRight;
}
.slideInRight {
  animation-name: slideInRight;
}

.animation-suoxiao {
  animation-name: slideOutLeft;
  animation-duration: 400ms;
  animation-fill-mode: forwards;
}
.animation-fangda {
  animation-name: slideInLeft;
  animation-duration: 300ms;
  animation-fill-mode: forwards;
}

.not-home-container {
  position: relative;
  width: 100%;
  height: 100%;

  %box-size {
    position: absolute;
    top: $position-top;
    z-index: 3;
    width: 360px;
    height: calc(100% - #{$position-top} - 20px);
    border-radius: 8px;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3);
    background-color: #fff;
  }

  %border-bottom {
    border-bottom: 1px solid rgba(155, 155, 155, 0.3);
  }

  %left-right-show {
    position: absolute;
    top: 50%;
    z-index: 1;
    transform: translateY(-50%);
    width: 44px;
    height: 60px;
    padding-left: 8px;
    background-size: 122% 152%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    cursor: pointer;
    span {
      width: 21px;
      height: 19px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  %marquee {
    position: absolute;
    bottom: -1px;
    height: 3px;
    width: 332px;
    background-color: rgba(45, 140, 240, 1);
    animation-duration: 3s;
    animation-fill-mode: forwards;
    animation-timing-function: linear;
  }
  .marquee_start {
    @extend %marquee;
    animation-name: marqueeAnimationStart;
  }
  .marquee_end {
    @extend %marquee;
    animation-name: marqueeAnimationEnd;
  }

  .left-show {
    @extend %left-right-show;
    width: 44px;
    height: 60px;
    left: 0;
    background-image: url("~@/assets/home/<USER>");
    background-position: 0px -16px;
    span {
      background-image: url("~@/assets/home/<USER>");
    }
  }
  .right-show {
    @extend %left-right-show;
    right: 0;
    background-image: url("~@/assets/home/<USER>");
    background-position: -11px -16px;
    padding-left: 10px;
    span {
      background-image: url("~@/assets/home/<USER>");
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    height: 22px;
    position: relative;
    padding-left: 12px;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 15px;

    ::v-deep .el-select-dropdown__item {
      height: 28px !important;
      line-height: 28px !important;
      padding: 0 !important;
    }

    ::v-deep .el-input--medium {
      .el-input__inner {
        height: 26px;
        line-height: 26px;
        padding-left: 5px;
        padding-right: 25px;
        font-size: 12px;
        color: #333;
      }
      .el-input__suffix {
        display: flex;
        align-items: center;
      }
    }

    .title-left {
      color: #333333;
      font-weight: 700;
      // font-family: PingFangSC-Semibold;
    }
    .title-right {
      color: rgba(45, 140, 240, 1);
      font-size: 16px;
      cursor: pointer;
    }
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 8px;
      height: 18px;
      background-color: rgba(45, 140, 240, 1);
    }
  }

  .slide-left {
    @extend %box-size;
    left: 0;
    padding: 0;

    .daily-inspection {
      @extend %border-bottom;
      position: relative;
      overflow: hidden;
      height: 206px;
      margin: 14px;
      padding-bottom: 10px;
      .title {
        margin-bottom: 10px;
      }
      .daily-inspection-main {
        span {
          display: block;
          height: 22px;
          display: flex;
          align-items: center;
          color: #333333;

          margin-bottom: 10px;
          img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
          }
        }
      }
      .daily-inspection-btns {
        display: flex;
        justify-content: flex-end;
        & > div {
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgb(45, 140, 240);
          border-radius: 20px;
          font-size: 14px;
          cursor: pointer;
          margin: 0 10px;
          opacity: 0.8;
          img {
            width: 16px;
            vertical-align: middle;
            margin-right: 5px;
          }
          &:hover {
            opacity: 1;
          }
        }
      }
    }
    .inspection-list {
      height: calc(100% - 206px);
      .title {
        margin: 14px 14px 8px 14px;
      }
      .inspection-list-main {
        height: calc(100% - 72px);
        overflow-y: auto;
        @include scrollBar;
        .inspection-list-main-item {
          position: relative;
          width: 100%;
          height: 108px;
          padding: 8px 14px;
          display: flex;
          cursor: pointer;
          border-radius: 8px;

          .inspection-list-main-item-one {
            position: absolute;
            z-index: 10;
            top: 0px;
            left: 0;
            display: block;
            width: 100%;
            height: 100%;
            // padding: 8px 14px;
            border-radius: 8px;
            overflow: hidden;
            &::before {
              content: '';
              position: absolute;
              bottom: 0px;
              right: -100%;
              width: 100%;
              height: 3px;
              filter: hue-rotate(120deg);
              background: linear-gradient(270deg, transparent, rgba(45, 140, 240, 1));
              animation: loginRight 3s linear infinite;
              animation-delay: 1.4s;
            }
            &::after {
              content: '';
              position: absolute;
              bottom: -100%;
              left: 1px;
              width: 3px;
              height: 100%;
              filter: hue-rotate(300deg);
              background: linear-gradient(360deg, transparent, rgba(45, 140, 240, 1));
              animation: loginBottom 3s linear infinite;
              animation-delay: 2.1s;
            }
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            &:hover {
              transform: scale(1.05);
            }
          }

          .inspection-list-main-item-left {
            width: 160px;
            height: 90px;
            margin-right: 10px;
            border-radius: 5px;
            overflow: hidden;
            .loading-container {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .empty-div {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              aspect-ratio: 16/9;
              height: auto;
              border-radius: 5px;
              border: 1px solid #ccc;
              color: #9b9b9b;
              padding: 0 5px;
              word-break: break-all;
              font-size: 12px;
              line-height: 1.2;
            }
          }
          .inspection-list-main-item-right {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;

            span {
              height: 20px;
              color: #333333;

              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 14px;
              line-height: 20px;
              &:nth-child(2) {
                margin: 10px 0;
              }
            }
          }
          &:hover {
            background-color: #eff7ff;
          }
        }
        .text-loding {
          margin-top: 5px;
          text-align: center;
          font-size: 14px;
          color: #999999;
        }
        .activeCss {
          background-color: #eff7ff;
          overflow: hidden;
          &::after {
            filter: hue-rotate(60deg);
            content: '';
            position: absolute;
            top: -100%;
            right: 2px;
            width: 3px;
            height: 100%;
            background: linear-gradient(180deg, transparent, rgba(45, 140, 240, 1));
            animation: loginTop 3s linear infinite;
            animation-delay: 0.7s;
          }
          &::before {
            filter: hue-rotate(0deg);
            content: '';
            position: absolute;
            top: 0px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(45, 140, 240, 1));
            animation: loginLeft 3s linear infinite;
          }
        }
        ::v-deep .el-loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
  .slide-right {
    @extend %box-size;
    right: 0;
    padding: 14px 0;
    overflow-y: auto;
    .slide-right-title {
      display: flex;
      justify-content: space-between;
      padding-left: 12px;
      padding-bottom: 10px;
      padding: 0 12px 10px 12px;
      height: 4%;
      .title-left {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
      }
      .title-right {
        color: rgba(45, 140, 240, 1);
        font-size: 16px;
        cursor: pointer;
      }
    }
    .title {
      margin-bottom: 10px;
    }
    .data-analysis {
      @extend %border-bottom;
      position: relative;
      overflow: hidden;
      width: 332px;
      height: 20%;
      margin: 0 auto;
      padding-bottom: 19px;
      .start-time {
        margin-bottom: 10px;
        color: #373737;
      }
      .total-mileage-time {
        display: flex;
        justify-content: space-between;
        height: 67px;
        span {
          height: 100%;
          background-color: #eff7ff;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-content: center;
          .total-label {
            height: 20px;
            color: #333333;
            font-size: 14px;
            text-align: center;
          }
          .total-value {
            height: 37px;
            display: flex;
            flex-direction: row;
            span {
              font-style: normal;
              color: rgba(45, 140, 240, 1);
              font-size: 26px;
              text-align: center;
              font-family: Arial-Black;
            }
            i {
              color: #333333;
              font-size: 14px;
              line-height: 50px;
              font-style: normal;
              // font-family: PingFangSC-Regula
            }
          }
        }
        .total-mileage {
          width: 174px;
        }
        .total-time {
          width: 148px;
        }
      }
      .progress {
        color: #333333;
        font-size: 14px;
        margin-top: 15px;
        .progress-item {
          height: 20px;
          display: flex;
          line-height: 20px;
          span {
            height: 100%;
          }
          .progress-item-label {
            width: 98px;
          }
          .progress-item-progress {
            width: 151px;
            display: flex;
            align-items: center;
            .el-progress {
              width: 100%;
              ::v-deep .el-progress-bar__inner {
                text-align: left;
              }
            }
          }
          .progress-item-value {
            width: calc(100% - 98px - 151px);
            text-align: right;
          }
          &:nth-child(2) {
            margin-top: 10px;
            margin-bottom: 10px;
          }
        }
      }
    }
    .all-information {
      color: #333333;
    }
    .daily-inspect {
      position: relative;
      overflow: hidden;
      width: 332px;
      margin: 0 auto;
      padding-top: 20px;
      padding-bottom: 19px;
      border-bottom: 1px solid rgba(155, 155, 155, 0.3);
      .title {
        margin-bottom: 0;
      }
      .echarts {
        height: calc(100% - 22px);
      }
      &:nth-child(3) {
        height: 23%;
      }
      &:nth-child(4) {
        height: 23%;
      }
      &:nth-child(5) {
        height: 30%;
        border-bottom: 0;
        border: none;
        padding-bottom: 0;
        margin-bottom: 0;
      }
    }
  }

  @keyframes scaleUpDown {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }

  .floating {
    position: absolute;
    top: $position-top;
    right: 375px;
    width: 50px;

    .map-layer-floating {
      display: block;
      width: 100%;
      height: 285px;
      border-radius: 8px;
      padding: 0 5px;
      background-color: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3);
      .map-layer-floating-item {
        @extend %border-bottom;
        display: block;
        height: 20%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: rgba(51, 51, 51, 0.5);
        &:hover img {
          animation: scaleUpDown 0.6s;
        }
        img {
          width: 22px;
          height: 20px;
          margin-bottom: 5px;
        }
        p {
          margin: 0;
          height: 18px;
          font-size: 13px;
        }
        &:last-child {
          border: 0;
        }
      }
    }

    .setting {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 50px;
      height: 57px;
      margin-top: 10px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3);
      cursor: pointer;
      &:hover img {
        animation: scaleUpDown 0.6s;
      }
      img {
        width: 22px;
        height: 20px;
        margin-bottom: 5px;
      }
      p {
        margin: 0;
        height: 18px;
        font-size: 13px;
        color: #333333;
      }
    }
  }
}
</style>
