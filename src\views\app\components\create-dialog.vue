/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-09 15:20:37
 */

<template>
  <el-dialog
    :title="dialogType==='edit'?'修改应用':'添加应用'"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="140px"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入视频应用名称" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="temp.type" placeholder="请选择类型" style="width: 100%">
          <el-option>视频流</el-option>
          <el-option>图集</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="算法" prop="algorithm">
        <el-select v-model="temp.algorithm" placeholder="请选择视频算法" style="width: 100%">
          <el-option>算法1</el-option>
          <el-option>算法2</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="检测间隔" prop="interval">
        <el-select v-model="temp.interval" placeholder="请选择检测间隔" style="width: 100%">
          <el-option>1秒</el-option>
          <el-option>2秒</el-option>
          <el-option>3秒</el-option>
          <el-option>4秒</el-option>
          <el-option>5秒</el-option>
          <el-option>10秒</el-option>
          <el-option>20秒</el-option>
          <el-option>30秒</el-option>
          <el-option>60秒</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="检测时段" prop="version">
        <el-time-picker
          v-model="value1"
          is-range
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          format="HH:mm"
        />
      </el-form-item>
      <el-form-item label="报警频度" prop="frequency">
        <el-select v-model="temp.frequency" placeholder="请选择报警频度" style="width: 100%">
          <el-option>5秒</el-option>
          <el-option>10秒</el-option>
          <el-option>30秒</el-option>
          <el-option>1分钟</el-option>
          <el-option>5分钟</el-option>
          <el-option>10分钟</el-option>
          <el-option>15分钟</el-option>
          <el-option>30分钟</el-option>
          <el-option>1小时</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报警发送至" prop="emil">
        <el-input v-model="temp.emil" placeholder="请输入有相地址" />
      </el-form-item>
      <el-form-item label="事件图保存至服务器" prop="isToServer">
        <el-checkbox v-model="temp.isToServer" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createUser, updateUser } from '@/api/user'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        algorithm: [{ required: true, message: '请选择算法', trigger: 'blur' }],
        interval: [{ required: true, message: '请选择检测间隔', trigger: 'blur' }],
        frequency: [{ required: true, message: '请选择检报警频度', trigger: 'blur' }],
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        if (this.formData.id) {
          this.dialogType = 'edit'
        } else {
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          createUser(tempData).then((res) => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          updateUser(tempData).then(() => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
    handlePwdChange(value) {
      this.$refs.dataForm.clearValidate('confirmPassword')
      this.$set(this.temp, 'confirmPassword', '')
      if (value !== '') {
        this.rules.confirmPassword[0].required = true
      } else {
        this.rules.confirmPassword[0].required = false
      }
    },
  },
}
</script>
