<template>
  <div class="report-container">
    <Echarts id="PCI-echart" :data="PCI_lineObj" style="width: 100%; height: 260px;" />
    <Echarts id="RQI-echart" :data="RQI_lineObj" style="width: 100%; height: 260px;" />
    <Echarts id="PQI-echart" :data="PQI_lineObj" style="width: 100%; height: 260px;" />
  </div>
</template>

<script>
import Echarts from './echarts.vue'

export default {
  components: { Echarts },
  props: {
    pciAllList: {
      type: Array,
      default: () => ([]),
    },
    rqiList: {
      type: Array,
      default: () => ([]),
    },
    pqiList: {
      type: Array,
      default: () => ([]),
    },
    milVal: {
      type: Number,
      default: 18,
    },
  },
  data() {
    return {
      PCI_lineObj: {},
      RQI_lineObj: {},
      PQI_lineObj: {},
    }
  },
  watch: {
    pciAllList() {
      this.getPCILineObj()
    },
    rqiList() {
      this.getRQILineObj()
    },
    pqiList() {
      this.getPQILineObj()
    },
    milVal() {
      this.getPCILineObj()
      this.getRQILineObj()
      this.getPQILineObj()
    },
  },
  methods: {
    getLineObj(tipName, xName, xMilData, showData, yAxisMax, yAxisMin) {
      return {
        tooltip: { trigger: 'axis' },
        dataZoom: [{
          type: 'slider',
          show: true,
          realtime: true,
          xAxisIndex: [0],
          // start: 0,
          // end: 30,
          brushSelect: false,
        }, {
          type: 'slider',
          show: true,
          yAxisIndex: [0],
          start: 0,
          end: 100,
          brushSelect: false,
        }],
        grid: {
          x: 40,
          y: 40,
          x2: 70,
          y2: 80,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          name: xName,
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#A8C0CE', // 左边线的颜色
              width: '2', // 坐标线的宽度
            },
          },
          axisLabel: {
            color: '#394380', // 坐标值得具体的颜色
            fontSize: '14px',
          },
          axisTick: {
            show: false,
          },
          data: xMilData,
        },
        yAxis: {
          max: 100,
          min: 0,
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#545F9E', // 左边线的颜色
              width: '1', // 坐标线的宽度
            },
          },
          axisLabel: {
            fontSize: '14px',
          },
        },
        series: [{
          name: tipName,
          type: 'line',
          symbol: 'circle',
          symbolSize: 9,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(48, 106, 241, 0.24)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(48, 106, 241, 0)', // 100% 处的颜色
              },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
          itemStyle: {
            color: '#306AF1', // 改变折线点的颜色
            borderColor: '#306AF1',
            lineStyle: {
              color: '#306AF1', // 改变折线颜色
            },
          },
          data: showData,
        }],
      }
    },
    getXAxisData(showData) {
      let xName = ''
      const xMilData = []
      let initVal = 0

      for (let i = 0; i < showData.length; i += 1) {
        if (this.milVal === 14) {
          initVal += 1
          xMilData.push(initVal)
          xName = 'KM'
        } else if (this.milVal === 15) {
          initVal += 500
          xMilData.push(initVal)
          xName = 'M'
        } else if (this.milVal === 16) {
          initVal += 200
          xMilData.push(initVal)
          xName = 'M'
        } else if (this.milVal === 17) {
          initVal += 100
          xMilData.push(initVal)
          xName = 'M'
        } else if (this.milVal === 18) {
          initVal += 50
          xMilData.push(initVal)
          xName = 'M'
        }
      }
      return {
        xName,
        xMilData,
      }
    },
    getPCILineObj() {
      const tipName = 'PCI 路面技术状况指数'
      const yAxisMax = 70
      const yAxisMin = 0
      const showData = this.pciAllList

      const { xName, xMilData } = this.getXAxisData(showData)
      const lineObj = this.getLineObj(tipName, xName, xMilData, showData, yAxisMax, yAxisMin)
      this.PCI_lineObj = lineObj
    },
    getRQILineObj() {
      const tipName = 'RQI 路面行驶质量指数'
      const yAxisMax = 3
      const yAxisMin = 0
      const showData = this.rqiList

      const { xName, xMilData } = this.getXAxisData(showData)
      const lineObj = this.getLineObj(tipName, xName, xMilData, showData, yAxisMax, yAxisMin)
      this.RQI_lineObj = lineObj
    },
    getPQILineObj() {
      const tipName = 'PQI 路面技术状况指数'
      const yAxisMax = 100
      const yAxisMin = 50
      const showData = this.pqiList

      const { xName, xMilData } = this.getXAxisData(showData)
      const lineObj = this.getLineObj(tipName, xName, xMilData, showData, yAxisMax, yAxisMin)
      this.PQI_lineObj = lineObj
    },
  },
}
</script>

<style lang="scss" scoped>
    .report-container {
        width: 600px;
    }
</style>
