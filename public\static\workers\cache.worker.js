
onmessage = async event => {
  const {
    key,
    pictureUrl,
    type,
    coordinate,
    allIdentifyType
  } = event.data
  try {
    // 加载图片数据
    const response = await fetch(pictureUrl);
    const image = await response.blob();

    const imageBitmap = await createImageBitmap(image);
    let canvas = new OffscreenCanvas(imageBitmap.width, imageBitmap.height); //  浏览器离屏渲染API（传入参数为宽高）
    let ctx = canvas.getContext('2d'); // 为offscreencanvas对象返回一个渲染画布
    ctx.drawImage(imageBitmap, 0, 0, imageBitmap.width, imageBitmap.height)
    ctxDraw(canvas, ctx, type, coordinate, allIdentifyType)
    const blob = await canvas.convertToBlob({type: 'image/webp'})
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    return new Promise(resolve => {
      reader.onloadend = () => {
      resolve(reader.result);
      };
    }).then((base64) => {
      postMessage({
        image: base64,
        key
      });
    })
  } catch (error) {
    postMessage({
      image: 3,
      key
    });
  }
 
}



function ctxDraw(canvas, ctx, type, coordinate, allIdentifyType) {
  ctx.lineWidth = 3
  if (canvas.width > 640) {
    ctx.font = 'bold 22px Arial'
  } else {
    ctx.font = 'bold 22px Arial'
  }

  let txt = ''
  
  let typeArr = allIdentifyType.filter((item) => item.engName === type)

  // console.log(typeArr);

  if (typeArr.length > 0) {
    ctx.strokeStyle = typeArr[0].color
    ctx.fillStyle = typeArr[0].color
    txt = typeArr[0].chineseName
  } else {
    ctx.strokeStyle = '#9C9C9C'
    ctx.fillStyle = '#9C9C9C'
    txt = '其他'
  }
  const coordinateArr = coordinate.split(' ')
  ctx.strokeRect(coordinateArr[0] * 1, coordinateArr[1] * 1, coordinateArr[2] - coordinateArr[0], coordinateArr[3] - coordinateArr[1])


  // 文字位置：框上或下，对齐：左对齐或右对齐
  const txtW = ctx.measureText(txt).width
  if (coordinateArr[0] * 1 + txtW > canvas.width) {
    ctx.textAlign = 'right'
    if (coordinateArr[1] * 1 < 25) {

      if (canvas.width > 640) {
        ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[3] * 1 + 25)
      } else {
        ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[3] * 1 + 25)
      }
    } else {
      ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[1] * 1 - 6)
    }
  } else {
    ctx.textAlign = 'left'
    if (coordinateArr[1] * 1 < 25) {
      if (canvas.width > 640) {
        ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[3] * 1 + 25)
      } else {
        ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[3] * 1 + 25)
      }
    } else {
      ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[1] * 1 - 6)
    }
  }
}

