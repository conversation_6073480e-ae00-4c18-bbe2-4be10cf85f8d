<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-06-15 15:10:52
 * @LastEditors: <PERSON><PERSON>j
 * @LastEditTime: 2023-06-19 14:18:03
 * @Description:
-->
<template>
  <div :id="id" :data="data" />
</template>

<script>
export default {
  props: ['id', 'data'],
  data() {
    return {
      chartGraph: null,
    }
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.drawGraph(this.id, newVal)
      },
      deep: true,
    },
  },
  mounted() {
    this.drawGraph(this.id, this.data)
  },
  beforeDestroy() {
    if (this.chartGraph) {
      this.chartGraph.clear()
    }
  },
  methods: {
    drawGraph(id, data) {
      const that = this
      const myChart = document.getElementById(id)
      if (myChart) {
        that.chartGraph = that.$echarts.init(myChart)
        that.chartGraph.setOption(data)
        window.addEventListener('resize', () => {
          that.chartGraph.resize()
        })

        // 折线图点击事件
        that.chartGraph.on('click', (params) => {
          that.$emit('echartsClick', params.dataIndex)
        })
      }
    },
  },
}
</script>

<style>
</style>
