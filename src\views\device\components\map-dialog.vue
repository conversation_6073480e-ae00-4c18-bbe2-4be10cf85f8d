<template>
  <el-dialog
    title="设备运行范围框选"
    width="80%"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div>
      <baidu-map
        v-if="dialogVisible"
        class="bm-view"
        :center="center"
        :zoom="zoom"
        :scroll-wheel-zoom="true"
        :double-click-zoom="false"
        @ready="handler"
      >
        <my-overlay v-for="(point, i) in points" :key="i" :index="i" :position="point" @deletePoint="handleDeletPoint" />
        <bm-polygon
          ref="bmPolygonRef"
          :path="points"
          stroke-color="#8CD52C"
          :stroke-opacity="0.8"
          :stroke-weight="4"
          fill-color=""
        />
        <bm-context-menu v-if="contextMenuShow">
          <bm-context-menu-item text="删除区域" :callback="clearPolygon" />
        </bm-context-menu>
      </baidu-map>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handelConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import myOverlay from '@/components/BMap/components/my-overlay.vue'
import { simpleStyleJson } from '@/utils/map-style'

export default {
  components: { myOverlay },

  data() {
    return {
      center: {
        lng: 16.404,
        lat: 39.915,
      },
      zoom: 12,
      dialogVisible: false,
      points: [],
      contextMenuShow: false,
    }
  },
  methods: {
    handler({ BMap, map }) {
      // console.log(BMap, map)
      this.map = map
      this.BMap = BMap
      this.center.lng = 116.404
      this.center.lat = 39.915
      this.zoom = 12

      // map.setMapStyle({
      //   style: 'grayscale',
      // })
      // map.setMapStyleV2({
      //   styleId: '84641241ef00321945d05b9cb13fab3b',
      // })
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
      this.map.addEventListener('click', (e) => {
        this.points.push(e.point)
      })
      // 地图右键点击
      const that = this
      that.map.addEventListener('rightclick', (event) => {
        // 如果鼠标右键位置处于多边形区域内多边形区域内，显示右键菜单
        if (that.points.length === 0) {
          that.contextMenuShow = false
          return
        }
        const bmPolygon = that.$refs.bmPolygonRef
        const pointInPolygon = BMapLib.GeoUtils.isPointInPolygon(event.point, bmPolygon.originInstance)
        console.log('地图右键点击时，鼠标位置是否处于多边形区域内', pointInPolygon)
        that.contextMenuShow = pointInPolygon
      })

      // 地图视野包含所有点
      this.$nextTick(() => {
        const allPoints = []
        this.points.forEach((item) => {
          allPoints.push(new this.BMap.Point(item.lng, item.lat))
        })
        setTimeout(() => {
          this.map.setViewport(allPoints, {
            margins: [10, 10, 10, 10],
          })
        })
      })
    },
    show(points) {
      this.dialogVisible = true
      this.points = points
    },
    handleDeletPoint(i) {
      this.points.splice(i, 1)
    },
    clearPolygon() {
      this.points = []
    },
    handleClose() {
      this.dialogVisible = false
      this.points = []
    },
    handelConfirm() {
      this.$emit('setGpsArea', this.points)
      this.dialogVisible = false
      this.points = []
    },
  },

}
</script>

<style lang="scss">
  .BMap_contextMenu {
    border: 1px solid #ccc;
    background: #fff;
    border-radius: 5px;
    text-align: center;
    div {
      line-height: 20px !important;
    }
  }
</style>
<style lang="scss" scoped>
  .bm-view {
    width: 100%;
    height: 680px;
  }
  .point-overlay {
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: red;
    color: #fff;
    text-align: center;
  }
</style>
