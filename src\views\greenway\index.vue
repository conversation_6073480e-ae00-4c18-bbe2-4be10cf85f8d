<template>
  <div class="greenway-container">
    <div
      ref="slideLeft"
      class="slide-left animate__animated animate__fast"
      :class="{
        animate__slideOutLeft: !leftShow,
        animate__slideInLeft: leftShow,
      }"
    >
      <div class="slide-left-title">
        <span class="title-left">北京绿道</span>
        <span class="title-right" @click="leftShow = !leftShow">
          收起 &lt;&lt;
        </span>
      </div>
      <div>
        <el-select
          v-model="districtQuery"
          style="width: 100%;"
          placeholder="请选择行政区划"
          @change="onDistrictQueryChange"
        >
          <el-option
            v-for="item in districtOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div ref="roadListRef" class="road-list">
        <div class="road">
          <el-link :type="activeRoadId===''?'primary':'info'" @click="onRoadChange()">
            全部绿道{{ districtQuery ? `（${districtQuery}）` : '（北京市）' }}
          </el-link>
        </div>
        <!-- 为每个绿道添加锚点id -->
        <div v-for="item in roads" :id="`road${item.roadId}`" :key="item.roadId" class="road">
          <div>
            <el-link :type="activeRoadId===item.roadId?'primary':'info'" @click="onRoadChange(item)">
              {{ item.roadName }}
            </el-link>
            <el-tag v-if="item.inspected" size="mini" style="margin-left: 5px;">已巡检</el-tag>
          </div>
          <div>{{ item.length || '-' }}km</div>
        </div>
      </div>
    </div>
    <span
      class="slide-left-btn animate__animated"
      :class="leftShowClass"
      @click="leftShow = !leftShow"
    >
      <span />
    </span>

    <baidu-map
      v-car-loading="mapLoading"
      class="bm-view"
      :center="center"
      :zoom="zoom"
      :auto-resize="true"
      :scroll-wheel-zoom="true"
      @ready="initMap"
    >
      <!--
        绘制绿道路线时，轨迹颜色分以下几种情况：
        1、巡检与未巡检的绿道用不同颜色区分，已巡检`lineColor.inspected` 未巡检`lineColor.default`
        2、鼠标点击路线时，且存在activeLineIndex，显示`lineColor.active`
        3、巡检任务轨迹 `lineColor.inspected2`
      -->
      <!-- @mouseout="clickLineIndex = -1" -->
      <template v-if="renderRoadLines.length > 0">
        <bm-polyline
          v-for="(item, $lIndex) in renderRoadLines"
          :key="'roPolyline' + $lIndex"
          :path="item.path"
          :stroke-color="
            (clickLineIndex === $lIndex) ? lineColor.active : (item.inspected) ? lineColor.inspected : lineColor.default
          "
          :stroke-opacity="1"
          :stroke-weight="4"
          :clicking="true"
          @click="onPolylineClick($event, item, $lIndex)"
        />
      </template>
      <!-- 仅在选中xx绿道时绘制其对应路线的起点和终点（两两一组），同一绿道不同路线的起始点采用起x、终x的方式区分标识 -->
      <template v-if="startEndPoints.length > 0">
        <template v-for="(item, $mIndex) in startEndPoints">
          <bm-marker
            :key="'seMarker' + item.id"
            :position="item.position"
            :offset="{ width: 0, height: -17 }"
            :icon="{
              url: item.type === 'start' ? LineStartIcon : LineEndIcon,
              size: { width: 38, height: 38 },
              opts: {
                imageSize: { width: 38, height: 38 },
              }
            }"
            :z-index="item.zIndex"
          />
          <bm-label
            :key="'seLabel' + item.id"
            :position="item.position"
            :content="`${item.type === 'start' ? '起' : '终'}${Math.floor($mIndex/2) + 1}`"
            :offset="{ width: -10, height: -32 }"
            :label-style="{
              width: '20px',
              height: '20px',
              lineHeight: '20px',
              color: item.type === 'start' ? '#39CC1B' : lineColor.active,
              fontSize : '9px',
              border: 'none',
              textAlign: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderRadius: '50%',
            }"
            :z-index="item.zIndex"
          />
        </template>
      </template>

      <!-- 巡检任务实际轨迹 -->
      <template v-if="inspectTrajectoryPath.length > 0">
        <!-- 直接绘制inspecTrajectoryPath会导致point-collection偏移（即点中心不在polyline上） -->
        <bm-polyline
          :path="inspectTrajectoryPath"
          :stroke-color="lineColor.inspected2"
          :stroke-opacity="1"
          :stroke-weight="4"
        />
        <!-- <template v-for="(item, $index) in inspectTrajectoryPath">
          <bm-polyline
            v-if="$index < inspectTrajectoryPath.length - 1"
            :key="'itPolyline' + $index"
            :path="[item, inspectTrajectoryPath[$index + 1]]"
            :stroke-color="lineColor.inspected2"
            :stroke-opacity="1"
            :stroke-weight="4"
          />
        </template> -->
        <bm-point-collection
          :points="inspectTrajectoryPath"
          shape="BMAP_POINT_SHAPE_CIRCLE"
          :color="lineColor.inspected2"
          size="4"
          @click="onInspectTrajectoryPointClick"
        />
        <!-- 巡检任务，轨迹起点与终点marker -->
        <bm-marker
          :position="inspectTrajectoryPath[0]"
          :icon="{
            url: InspectStartIcon,
            size:{width:64,height:64},
            opts:{imageSize:{width:64,height:64}, anchor: {width: 17, height: 53}}
          }"
        />
        <bm-marker
          :position="inspectTrajectoryPath[inspectTrajectoryPath.length - 1]"
          :icon="{
            url: InspectEndIcon,
            size:{width:64,height:64},
            opts:{imageSize:{width:64,height:64},anchor: {width: 17, height:53}}
          }"
        />
      </template>
      <!-- 点击巡检任务轨迹点，打开信息窗体，展示道路图片 -->
      <bm-info-window
        ref="inspectImageWindowRef"
        :position="inspectImageWindow.postition"
        :title="inspectImageWindow.title"
        :show="inspectImageWindow.show"
        :close-on-click="false"
        :auto-pan="true"
        @clickclose="closeInspectImageWindow"
      >
        <div v-cloak v-if="inspectImageWindow.show" class="disease-cont">
          <div class="disease-img-box">
            <img
              :src="inspectImageWindow.imageUrls[inspectImageWindow.imageIndex]"
              class="disease-img"
              alt="鼠标滑轮滚动缩放图片"
              title="点击放大图片"
              style=" object-fit:cover; "
              width="700px"
              height="393px"
              @click="showInspectBigImage"
            >
          </div>
          <div class="disease-deal-btns">
            <div>
              <el-button size="small" :disabled="inspectImageWindow.imageIndex===0" round @click="onPrevInspectImage">
                上一张
              </el-button>
              <el-button size="small" :disabled="inspectImageWindow.imageIndex===inspectImageWindow.imageUrlsLength-1" round @click="onNextInspectImage">
                下一张
              </el-button>
            </div>
          </div>
        </div>
      </bm-info-window>
    </baidu-map>

    <div class="slide-right">
      <!-- 图例 -->
      <div class="legend">
        <div class="legend-btn" title="图例" @click="legendShow = !legendShow">?</div>
        <div v-show="legendShow" class="legend-cont">
          <div class="legend-cont-title">图例</div>
          <div v-for="(color, key) of lineColor" :key="key" class="legend-cont-item">
            <i :style="{ 'backgroundColor': color }" />
            <span>{{ key === 'default' ? '未巡检' : key === 'inspected' ? '已巡检' : key === 'active' ? '激活' : '巡检轨迹' }}</span>
          </div>
        </div>
      </div>
      <div v-show="roadInfoPanelShow" class="road-info-panel">
        <el-card class="card">
          <i class="el-icon-close" @click="onCloseRoadInfoPanel" />
          <div class="title">
            <i class="el-icon-discount" />
            <span>绿道信息</span>
          </div>
          <div class="info">
            <p>名称：{{ roadInfo.roadName }}</p>
            <p>行政区划：{{ roadInfo.district }}</p>
            <p>地址：{{ roadInfo.address }}</p>
            <p>方式：{{ roadInfo.way }}</p>
            <p>公里数：{{ roadInfo.length || '-' }}km</p>
            <p>管理单位：{{ roadInfo.adminCompany }}</p>
            <p>产权单位：{{ roadInfo.copyrightCompany }}</p>
            <p>等级：{{ roadInfo.level }}</p>
            <p>建设年份：{{ roadInfo.createTime }}</p>
          </div>
          <div class="title">
            <i class="el-icon-discount" />
            <span>巡检任务</span>
          </div>
          <el-table v-loading="inspectTask.loading" size="mini" :data="inspectTask.data" border :height="tableHeight">
            <el-table-column align="center" label="开始时间">
              <template slot-scope="{ row }">
                {{ new Date(row.startTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}") }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="结束时间">
              <template slot-scope="{ row }">
                <template>
                  {{ new Date(row.endTime) | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}") }}
                </template>
              </template>
            </el-table-column>
            <el-table-column align="center" label="巡检里程(km)" prop="inspectMileage" width="95" />
            <el-table-column align="center" label="操作" width="70">
              <template slot-scope="{ row }">
                <el-button type="text" :loading="!!row.loading" @click="onInspectDetail(row)">轨迹</el-button>
              </template>
            </el-table-column>
          </el-table>
          <Pagination
            :total="inspectTask.total"
            :page.sync="inspectTask.currentPage"
            :limit.sync="inspectTask.pageSize"
            :pager-count="5"
            layout="total, prev, pager, next"
            @pagination="getInspectionTasks"
          />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { api as viewerApi } from 'v-viewer'
import { simpleStyleJson } from '@/utils/map-style'
import LineStartIcon from '@/assets/greenway/line-start.png'
import LineEndIcon from '@/assets/greenway/line-end.png'
import InspectStartIcon from '@/assets/line-start.png'
import InspectEndIcon from '@/assets/line-end.png'
import pinyin from 'js-pinyin'
import Pagination from '@/components/Pagination/index.vue'
import { getInspectTasks, getInspectedRoadNames, getInspectImageInfer } from '@/api/greenway'
import UtilsMixin from './mixin/utils'
import roadData from './roadData_bd09.json'

export default {
  name: 'Greenway',
  components: {
    Pagination,
  },
  mixins: [UtilsMixin],
  data() {
    return {
      leftShow: true,
      districtQuery: '',
      districtOptions: [
        { label: '北京市（1580公里）', value: '' },
        { label: '通州区（425公里）', value: '通州区' },
        { label: '朝阳区（259公里）', value: '朝阳区' },
        { label: '顺义区（188公里）', value: '顺义区' },
        { label: '海淀区（160公里）', value: '海淀区' },
        { label: '石景山区（111公里）', value: '石景山区' },
        { label: '东城区（97公里）', value: '东城区' },
        { label: '丰台区（77公里）', value: '丰台区' },
        { label: '昌平区（71公里）', value: '昌平区' },
        { label: '房山区（54公里）', value: '房山区' },
        { label: '大兴区（48公里）', value: '大兴区' },
        { label: '延庆区（33公里）', value: '延庆区' },
        { label: '平谷区（31公里）', value: '平谷区' },
        { label: '西城区（27公里）', value: '西城区' },
      ],
      activeRoadId: '', // 左侧选中的绿道id
      // map
      map: null,
      BMap: null,
      convertor: null, // 坐标转换器
      mapLoading: true,
      center: {
        lng: 116.404,
        lat: 39.915,
      },
      zoom: 10,
      allRoads: [], // 全部绿道
      roads: [], // 行政区筛选后绿道
      allRoadLines: [], // 绿道对应的所有路线，一对一或一对多
      renderRoadLines: [], // 地图上渲染的绿道
      lineColor: {
        default: '#8CD52D', // 默认色（未巡检色）
        inspected: '#40D3EA', // 已巡检色
        active: '#FF4A34', // 点击色
        inspected2: '#FFE60D', // 实际巡检
      },
      startEndPoints: [], // 选中的绿道对应的起点与终点markers
      LineStartIcon, // 绿道路线起点图标
      LineEndIcon, // 绿道路线终点图标
      legendShow: true,
      // Panel
      clickLineIndex: -1, // 地图点击的路线下标
      roadInfoPanelShow: false,
      roadInfo: {},
      inspectTask: { // 巡检任务
        data: [],
        loading: false,
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      InspectStartIcon, // 巡检任务轨迹起点图标
      InspectEndIcon, // 巡检任务轨迹终点图标
      inspectTrajectoryPath: [],
      inspectImageWindow: { // 轨迹图片信息窗体
        show: false,
        title: '道路实景图',
        position: {},
        imageUrls: [],
        imageIndex: 0, // imageUrl相对于inspectTaskTrajectory的下标
        imageUrlsLength: 0,
      },
    }
  },
  computed: {
    leftShowClass() {
      if (this.leftShow) return ['animate__slideOutLeft']
      return ['animate__slideInLeft']
    },
    startEndLabels() {
      return this.startEndPoints
    },
  },
  methods: {
    initMap({ BMap, map }) {
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
      map.disableKeyboard()
      const that = this
      that.map = map
      that.BMap = BMap
      that.convertor = new BMap.Convertor()
      // const svgDom = map.getPanes().mapPane.getElementsByTagName('svg')[0] // 解决海量点覆盖(路线)polyline引发的鼠标事件丢失问题
      // if (svgDom) svgDom.style.zIndex = 900
      that.getRoadAndLines()
    },
    resetMapViewport(pointArray, margins = [50, 400, 50, 400]) {
      if (this.map) {
        this.map.setViewport(pointArray, {
          margins,
        })
      }
    },
    // # 获取所有道路 和 路线
    async getRoadAndLines() {
      const { payload } = await getInspectedRoadNames()
      const that = this
      const tempLines = []
      let tempRoads = []
      const { features } = roadData
      if (features && features.length) {
        for (let i = 0; i < features.length; i += 1) {
          const { geometry, properties } = features[i]
          const info = {
            roadId: properties.OBJECTID_1, // "OBJECTID": 6 75 92
            roadName: properties['名称'],
            district: properties['所在区'],
            address: properties['地址'],
            length: properties['长度'],
            way: properties['方式'],
            adminCompany: properties['管理单'],
            copyrightCompany: properties['产权单'],
            level: properties['等级'],
            createTime: properties['建设年'],
            inspected: payload.includes(properties['名称']), // 是否巡检
          }
          tempRoads.push(info)
          // 一条巡检道路对应多条路线
          if (geometry.type === 'MultiLineString') {
            geometry.coordinates.forEach((line, $index) => {
              const path = that.transCoordinate(line)
              tempLines.push({
                path: [...path],
                ...info,
              })
            })
          } else if (geometry.type === 'LineString') {
            // 一条巡检道路对应单条路线
            const path = that.transCoordinate(geometry.coordinates)
            tempLines.push({
              path,
              ...info,
            })
          }
        }
      }
      tempRoads = that.sortByPinyin(tempRoads)
      that.allRoads = tempRoads
      that.roads = tempRoads
      that.allRoadLines = tempLines
      this.reRenderRoadLines('district', '')
      that.mapLoading = false
    },
    transCoordinate(list) {
      return list.map((item) => ({
        lng: item[0],
        lat: item[1],
      }))
    },
    // 绿道名称按拼音字母排序
    sortByPinyin(arr) {
      return arr.sort((a, b) => {
        const pinyinA = pinyin.getFullChars(a.roadName).toLowerCase()
        const pinyinB = pinyin.getFullChars(b.roadName).toLowerCase()
        if (pinyinA < pinyinB) {
          return -1
        }
        if (pinyinA > pinyinB) {
          return 1
        }
        return 0
      })
    },
    // 切换行政区域，绘制当前行政区的绿道
    onDistrictQueryChange(value) {
      if (value) {
        this.roads = this.allRoads.filter((item) => item.district.includes(value))
      } else {
        this.roads = this.allRoads
      }
      this.reRenderRoadLines('district', value)
      this.$nextTick(() => {
        this.$refs.roadListRef.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth',
        })
      })
    },
    // 切换绿道
    onRoadChange(roadInfo) {
      if (roadInfo && roadInfo.roadId) {
        this.reRenderRoadLines('roadId', roadInfo.roadId)
        this.onOpenRoadInfoPanel(roadInfo)
      } else {
        this.reRenderRoadLines('district', this.districtQuery)
      }
    },
    /**
     * 从所有绿道路线（allRoadLines）中筛选出符合的数据，重新渲染路线及起点与终点
     * @param renderType <string> 筛选条件，`roadId`（绿道id）`district`（行政区）
     * @param renderValue <string> 筛选条件的期望值；若为空表示渲染全部路线
     */
    reRenderRoadLines(renderType, renderValue) {
      this.onCloseRoadInfoPanel()
      this.renderRoadLines = []
      this.startEndPoints = []
      const lines = []
      const points = []
      const startEndPoints = []
      if (renderType === 'roadId') {
        this.activeRoadId = renderValue
      } else {
        this.activeRoadId = ''
      }
      for (let i = 0; i < this.allRoadLines.length; i += 1) {
        const line = this.allRoadLines[i]
        if (!renderValue || line[`${renderType}`] === renderValue) {
          lines.push(line)
          points.push(...line.path)
          if (renderType === 'roadId') {
            startEndPoints.push({
              id: Math.random() * 10000,
              type: 'start',
              position: line.path[0],
              roadName: line.roadName,
              zIndex: 100 + i,
            }, {
              id: Math.random() * 10000,
              type: 'end',
              position: line.path[line.path.length - 1],
              roadName: line.roadName,
              zIndex: 100 + i,
            })
          }
        }
      }
      this.renderRoadLines = lines
      this.startEndPoints = startEndPoints
      this.resetMapViewport(points)
    },
    // 点击地图上的绿道路线，展示绿道详情
    onPolylineClick($event, roadInfo, $lineIndex) {
      if (!this.activeRoadId) {
        this.clickLineIndex = $lineIndex
      }
      // 滚动到指定绿道
      // const roadElement = document.getElementById(`road${roadInfo.roadId}`)
      // if (roadElement) {
      //   roadElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      // }
      this.onOpenRoadInfoPanel(roadInfo)
    },
    /**
     * 点击绿道或路线，展示绿道详情
     * @param roadInfo 绿道详情
     */
    onOpenRoadInfoPanel(roadInfo) {
      this.roadInfoPanelShow = true
      this.roadInfo = roadInfo
      // 已巡检绿道获取巡检任务
      if (roadInfo.inspected) {
        this.getInspectionTasks()
      } else {
      // 否则清空巡检任务
        this.inspectTask.currentPage = 1
        this.inspectTask.data = []
        this.inspectTask.total = 0
      }
    },
    onCloseRoadInfoPanel() {
      this.roadInfoPanelShow = false
      this.clickLineIndex = -1
      this.roadInfo = {}
      this.inspectTrajectoryPath = []
      this.inspectImageWindow.imageUrls = []
      this.closeInspectImageWindow()
    },
    // 获取巡检任务
    async getInspectionTasks() {
      const params = {
        roadName: this.roadInfo.roadName,
        page: this.inspectTask.currentPage - 1,
        size: this.inspectTask.pageSize,
      }
      this.inspectTask.loading = true
      const { payload } = await getInspectTasks(params)
      this.inspectTask.loading = false
      this.inspectTask.data = payload.content.map((item) => ({
        ...item,
        loading: false,
      }))
      this.inspectTask.total = payload.totalElements
    },
    // 获取巡检任务对应的具体轨迹
    async onInspectDetail(row) {
      const params = {
        taskId: row.id,
      }
      row.loading = true
      try {
        const { payload } = await getInspectImageInfer(params)
        row.loading = false
        const path = [] // 轨迹路线
        const imageUrls = [] // 轨迹图片
        for (let i = 0; i < payload.length; i += 1) {
          const {
            gpsLongitude, gpsLatitude, objectStorageUrlPrefix, originalImagePath,
          } = payload[i]
          path.push({
            id: i,
            lng: gpsLongitude,
            lat: gpsLatitude,
          })
          imageUrls.push((objectStorageUrlPrefix + originalImagePath))
        }
        this.resetMapViewport(path)
        this.inspectTrajectoryPath = path
        this.inspectImageWindow.imageUrls = imageUrls
        this.inspectImageWindow.imageUrlsLength = imageUrls.length
      } catch (e) {
        row.loading = false
      }
    },
    // 点击巡检任务轨迹点，显示信息窗体
    onInspectTrajectoryPointClick({ point }) {
      this.inspectImageWindow.postition = {
        lng: point.lng,
        lat: point.lat,
      }
      this.inspectImageWindow.imageIndex = this.inspectTrajectoryPath.findIndex((pt) => (pt.lng === point.lng && pt.lat === point.lat))
      this.inspectImageWindow.show = true
    },
    closeInspectImageWindow() {
      this.inspectImageWindow.show = false
      this.inspectImageWindow.postition = {}
      this.inspectImageWindow.imageIndex = 0
    },
    onPrevInspectImage() {
      const { imageIndex } = this.inspectImageWindow
      if (imageIndex !== 0) {
        this.inspectImageWindow.imageIndex -= 1
        this.inspectImageWindow.postition = {
          lng: this.inspectTrajectoryPath[this.inspectImageWindow.imageIndex].lng,
          lat: this.inspectTrajectoryPath[this.inspectImageWindow.imageIndex].lat,
        }
      }
    },
    onNextInspectImage() {
      const { imageIndex, imageUrlsLength } = this.inspectImageWindow
      if (imageIndex !== imageUrlsLength - 1) {
        this.inspectImageWindow.imageIndex += 1
        this.inspectImageWindow.postition = {
          lng: this.inspectTrajectoryPath[this.inspectImageWindow.imageIndex].lng,
          lat: this.inspectTrajectoryPath[this.inspectImageWindow.imageIndex].lat,
        }
      }
    },
    showInspectBigImage() {
      const that = this
      that.viewer = viewerApi({
        images: this.inspectImageWindow.imageUrl,
        options: {
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: false, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: false, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: false, // 使用 CSS3 过度
          fullscreen: true, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';
$position-top: 16px;
%box-size {
  position: absolute;
  top: $position-top;
  z-index: 3;
  width: 360px;
  height: calc(100% - #{$position-top} - 20px);
  border-radius: 8px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3);
  background-color: #fff;
}

%left-right-show {
  position: absolute;
  top: 50%;
  z-index: 1;
  transform: translateY(-50%);
  width: 44px;
  height: 60px;
  padding-left: 8px;
  background-size: 122% 152%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  cursor: pointer;
  span {
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.greenway-container {
  width: 100%;
  height: calc(100vh - 94px);
  position: relative;
  display: flex;
  .slide-left {
    @extend %box-size;
    left: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    &-title {
      display: flex;
      justify-content: space-between;
      position: relative;
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 15px;
      .title-left {
        color: #333333;
        font-weight: 700;
        &::before {
          display: inline-block;
          content: "";
          width: 8px;
          height: 18px;
          background-color: rgba(45, 140, 240, 1);
          border-radius: 2px;
          position: relative;
          top: calc((22px - 18px) / 2);
          margin-right: 5px;
        }
      }
      .title-right {
        color: rgba(45, 140, 240, 1);
        font-size: 14px;
        cursor: pointer;
      }
    }
    .road-list {
      width: calc(100% + 40px);
      position: relative;
      left: -20px;
      flex: 1;
      overflow-y: auto;
      @include scrollBar;
      padding-top: 5px;
      .road {
        display: flex;
        justify-content: space-between;
        margin: 15px 0;
        padding: 0 20px;
        .el-link {
          font-size: 16px;
          cursor: pointer;
        }
        a + div {
          margin-left: 10px;
        }
      }
    }
  }
  .slide-left-btn {
    @extend %left-right-show;
    width: 44px;
    height: 60px;
    left: 0;
    background-image: url("~@/assets/home/<USER>");
    background-position: 0px -16px;
    span {
      margin-bottom: 4px;
      background-image: url("~@/assets/greenway/road.png");
    }
  }
  .bm-view {
    width: 100%;
    height: 100%;
  }
  .slide-right {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    align-items: flex-end;
  }
  .road-info-panel {
    width: 400px;
    background: #f6f6f6;
    overflow: auto;
    padding: 10px;

    .card {
      position: relative;
      border-radius: 10px;
      height: calc(100vh - 120px);
      border: none;
      box-shadow: none;

      .title {
        display: flex;
        align-items: center;
        color: #394380;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        i {
          color: #394380;
          font-size: 20px;
          margin-right: 10px;
        }
      }
      .info {
        font-size: 14px;
        color: #606266;
        padding-bottom: 20px;
        p {
          display: flex;
          align-items: center;
        }
      }
      .el-icon-close {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #394380;
        cursor: pointer;
      }
    }
  }
  .legend {
    margin: 0 10px 10px 0;
    position: relative;
    .legend-btn {
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      border-radius: 50%;
      color: #fff;
      background: #2d8cf0;
      font-size: 14px;
      cursor: pointer;
    }
    .legend-cont {
      position: absolute;
      bottom: 0;
      right: 40px;
      width: 200px;
      height: max-content;
      border-radius: 8px;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3);
      background-color: #fff;
      color: #232323;
      .legend-cont-title {
        text-align: center;
        line-height: 40px;
        border-bottom: 1px dashed #E5E5E5;
        letter-spacing: 1px;
      }
      .legend-cont-item {
        padding: 10px 0 10px 50px;
        font-size: 14px;
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          width: 30px;
          height: 15px;
          border-radius: 8px;
          margin-right: 20px;
        }
      }
      .legend-cont-item:nth-of-type(2) {
        padding-top: 15px;
      }
      .legend-cont-item:nth-of-type(5) {
        padding-bottom: 15px;
      }
    }
  }
}
.disease-cont {
  width: auto;
  padding-top: 10px;
  .disease-img-box {
    border-radius: 6px;
    overflow: hidden;
    @media screen and (max-width: 1440px) {
      width: 500px;
      height: 281px
    }
    @media screen and (max-width: 1280px) {
      width: 380px;
      height: 214px
    }
    @media screen and (min-width: 1440px) {
      width: 700px;
      height: 393px
    }
  }
  .disease-img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .disease-deal-btns{
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
}
</style>
