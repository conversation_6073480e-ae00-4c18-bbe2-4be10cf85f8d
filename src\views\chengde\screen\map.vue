<template>
  <div class="map-container" ref="targetContainer">
    <!-- 高德地图组件 -->
    <el-amap
      ref="map"
      :zoom="zoom"
      :center="center"
      view-mode="2D"
      map-style="amap://styles/darkblue"
      @init="handleMapInit"
    >
      <!-- 添加边界多边形 -->
      <el-amap-polygon
        v-for="(polygon, index) in polygons"
        :key="'polygon-' + index"
        :path="polygon"
        :stroke-color="boundaryStyle.strokeColor"
        :stroke-weight="boundaryStyle.strokeWeight"
        :stroke-opacity="boundaryStyle.strokeOpacity"
        :fill-color="boundaryStyle.fillColor"
        :fill-opacity="boundaryStyle.fillOpacity"
        :zIndex="5"
        @init="handlePolygonInit"
      ></el-amap-polygon>

      <el-amap-marker
        v-for="(marker, index) in diseaseData"
        :key="index"
        top
        :zIndex="getMarkerZIndex(marker, index)"
        :position="[marker.gpsLongitude || 0, marker.gpsLatitude || 0]"
        anchor="bottom-center"
        @click="handleMarkerClick(marker)"
        @dblclick="handleMarkerDblClick(marker)"
      >
        <div :class="{ 'marker-wrapper': true, 'bounce-animation': isNewDisease(marker.id) }">
          <img
            :src="getMarkerImage(marker)"
            alt="marker-disease"
            class="custom-marker"
            :class="{ 'active-marker': activeId === marker.id }"
            :title="marker.damageName"
          />
          <div v-if="isNewDisease(marker.id)" class="new-indicator"></div>
        </div>
      </el-amap-marker>
      <!-- 样式修改需要同步 下方  隐藏的测量元素（简化版）用来获取地图信息窗口的宽度 -->
      <el-amap-info-window
        v-if="infoWindowState.visible"
        :position="infoWindowState.position"
        :visible="infoWindowState.visible"
        :isCustom="true"
        :autoMove="true"
        anchor="top-left"
        :offset="[25, -50]"
        :avoid="infoWindowState.avoid"
        ref="infoWindow"
      >
        <transition name="info-window-transition">
          <div class="info-window-content" v-if="infoWindowState.visible">
            <div class="info-window-header">
              <span>{{infoWindowState.data && infoWindowState.data.damageName ? infoWindowState.data.damageName : '病害详情'}}</span>
              <span class="info-window-close" @click="closeInfoWindow"></span>
            </div>
            <div class="info-window-body">
              <!-- 修改轮播图部分，正确显示图片 -->
              <div class="info-window-carousel">
                <el-carousel
                  ref="carouselRef"
                  v-if="infoWindowState.data && infoWindowState.data.images && infoWindowState.data.images.length > 0"
                  height="199px"
                  trigger="click"
                  :autoplay="false"
                  @change="handleCarouselChange"
                >
                  <el-carousel-item v-for="(image, index) in infoWindowState.data.images" :key="index">
                    <el-image
                      :src="image.objectStorageUrlPrefix + image.originalImagePath"
                      fit="contain"
                      @click="handleImageClick(index)"
                    >
                      <template #error>
                        <div class="item-image-error"></div>
                      </template>
                      <template #placeholder>
                        <i class="el-icon-loading" style="font-size: 14px;"></i>
                      </template>
                    </el-image>
                  </el-carousel-item>
                </el-carousel>
                <div
                  class="info-window-carousel-image-count"
                  v-if="infoWindowState.data && infoWindowState.data.images && infoWindowState.data.images.length > 0"
                >{{this.currentImageIndex + 1}}/{{infoWindowState.data.images.length}}</div>
                <div class="info-window-carousel-no-image" v-else>暂无图片</div>
              </div>
              <div class="disease-info" v-if="infoWindowState.data">
                <div class="info-window-description">
                  <div v-if="descriptionText">{{ descriptionText }}</div>
                </div>
                <div class="line"></div>
                <div class="disease-detail" v-if="infoWindowState.data">
                  <span>道路: {{infoWindowState.data.roadName}}</span>
                  <span>桩号: {{infoWindowState.data.startPileStr}}</span>
                  <span>时间: {{formatTime(infoWindowState.data.createTime)}}</span>
                  <span>人员: {{infoWindowState.data.realName}}</span>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </el-amap-info-window>
    </el-amap>
    <!-- 隐藏的测量元素（简化版）用来获取地图信息窗口的宽度 -->
    <div ref="hiddenElement" style="position: absolute; left: -9999px; opacity: 0;">
      <!-- 简单复制信息窗口的内容结构 -->
      <div class="info-window-content">
        <div class="info-window-header">
          <span>病害详情</span>
        </div>
        <div class="info-window-body">
          <div class="info-window-carousel" style="height: 199px;"></div>
          <div class="disease-info">
            <div class="info-window-description">
              <div>{{ descriptionText || '描述文本' }}</div>
            </div>
            <div class="line"></div>
            <div class="disease-detail">
              <span>道路:</span>
              <span>桩号:</span>
              <span>时间:</span>
              <span>人员:</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
// 导入边界数据
import boundaryData from './boundary.json'
// 导入统一配置的getMarkerIcon函数
import { getMarkerIcon } from '@/utils/cd_constants'

// 导入地图marker-病害类型图标
import mainMarker from '@/assets/cheng-de-screen/main-marker.png'


let center = [117.939152, 40.976204]
let infoWindowWidth = 355
let infoWindowHeight = 400
export default {
  name: 'main-stem',
  props: {
    // 头部标题
    headerTitle: {
      type: String,
      default: '病害分布',
    },
    // 里程
    mileage: {
      type: Number,
      default: 0,
    },
    // 病害数据
    diseaseData: {
      type: Array,
      default: () => [],
    },
    // 新病害ID
    newDiseaseIds: {
      type: Array,
      default: () => [],
    },
    // 是否全屏
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      zoom: 15,
      center: center,
      markerImg: mainMarker,
      activeId: '',
      infoWindowState: {
        visible: false,
        position: center,
        data: null,
        avoid: [100, 180, 100, 100],
      },
      currentImageIndex: 0,
      viewer: null,
      // 添加边界样式配置
      boundaryStyle: {
        strokeColor: '#1989fa', // 边界线颜色
        strokeWeight: 2, // 边界线宽度
        strokeOpacity: 0.8, // 边界线透明度
        fillColor: '#1989fa', // 填充颜色
        fillOpacity: 0.1, // 填充透明度
      },
      // 存储解析后的多边形路径
      polygons: boundaryData,
      amapInstance: null,
      clickTimeout: null,
      infoWindowRefreshTimer: null, // 添加一个变量保存刷新计时器
      resizeTimer: null, // 添加防抖计时器
      hasAdjustedViewToMarkers: false, // 标记是否已经调整过视野
    }
  },
  watch: {
    // 监听新病害数据变化，在控制台打印日志
    newDiseaseIds: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('新病害ID:', newVal)
        }
      },
      deep: true,
    },
    diseaseData: {
      handler(newVal) {
        console.log('病害数据:', newVal)
        if (newVal && newVal.length > 0) {
          // 只有在尚未调整过视野的情况下才执行调整
          if (!this.hasAdjustedViewToMarkers) {
            this.$nextTick(() => {
              this.adjustMapViewToMarkers()
              // 标记已经调整过视野
              this.hasAdjustedViewToMarkers = true
            })
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getInfoWindowWidth()
    window.addEventListener('resize', this.handleResize)
  },
  computed: {
    previewSrcList() {
      if (!this.infoWindowState.data || !this.infoWindowState.data.images) {
        return []
      }
      return this.infoWindowState.data.images.map(
        (image) => image.objectStorageUrlPrefix + image.originalImagePath
      )
    },
    descriptionText() {
      const data = this.infoWindowState.data
      if (!data) return ''

      const addressPart = data.addressDescription
        ? `${data.addressDescription}`
        : ''
      const damagePart = data.damageDescription
        ? `${data.damageDescription}`
        : ''

      if (addressPart && damagePart) {
        return `${addressPart}，${damagePart}`
      }

      return addressPart || damagePart
    },
  },
  methods: {
    getMarkerZIndex(marker, index) {
      // 如果是新发现的也设置为顶部
      if (this.isNewDisease(marker.id)) {
        return 100 + index
      }
      return this.activeId === marker.id ? 100 : 10
    },
    // 获取marker图标
    getMarkerImage(marker) {
      // 使用统一配置的getMarkerIcon函数，但始终传入false表示非激活状态
      return getMarkerIcon(marker.belongTo, false)
    },
    // 检查病害ID是否为新数据
    isNewDisease(id) {
      return this.newDiseaseIds && this.newDiseaseIds.includes(id)
    },
    handleMapInit(map) {
      this.amapInstance = map
      console.log('地图初始化完成')
      this.setInfoWindowAvoid()
    },
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }
      this.resizeTimer = setTimeout(() => {
        this.getInfoWindowWidth()
        this.setInfoWindowAvoid()
      }, 200) // 200ms延迟
    },
    /**
     * 获取信息窗口的宽度
     */
    getInfoWindowWidth() {
      const { offsetWidth, offsetHeight } = this.$refs.hiddenElement
      infoWindowWidth = offsetWidth
      infoWindowHeight = offsetHeight
    },
    /**
     * 设置信息窗口的偏移量 在地图中心
     */
    async setInfoWindowAvoid() {
      if (!this.amapInstance) return
      
      const { offsetWidth, offsetHeight } = this.amapInstance.getContainer()
      if (!offsetWidth || !offsetHeight) return
      
      // 计算偏移量
      const offsetX = Math.floor((offsetWidth - infoWindowWidth) / 2)
      const offsetY = Math.floor((offsetHeight - infoWindowHeight) / 2)
      
      // 检查偏移量是否有变化，避免不必要的更新
      const currentAvoid = this.infoWindowState.avoid
      const newAvoid = [offsetY, offsetX, offsetY, offsetX]
      
      if (JSON.stringify(currentAvoid) !== JSON.stringify(newAvoid)) {
        // 更新避让区域
        this.infoWindowState.avoid = newAvoid
        
        // 如果窗口可见并且处于全屏状态，需要刷新位置
        this.refreshInfoWindowIfNeeded()
      }
    },
    handlePolygonInit(polygon) {
      console.log('多边形初始化完成', this.amapInstance)
      this.$nextTick(() => {
        this.amapInstance.setFitView(undefined, true, [10, 10, 10, 10])
      })
    },
    handleCarouselChange(index) {
      this.currentImageIndex = index
    },
    // 显示病害详情
    showDiseaseDetail(disease, isDblClick = false) {
      if (!disease || !this.amapInstance) return
      
      const lng = disease.gpsLongitude || 0
      const lat = disease.gpsLatitude || 0

      
      // 判断是否在视野内
      const isInView = this.amapInstance
      .getBounds()
      .contains(new AMap.LngLat(lng, lat))
      // 如果不在视野内，则调整地图使marker可见
      if (!isInView) {
        // 使用centerMapOnMarker方法将地图居中到marker位置
        this.centerMapOnMarker(disease, false)
      } else {
        // 根据情况设置动画
        this.amapInstance.setStatus({
          animateEnable: isDblClick ? false : true,
        })
      }
      
      const isNewDisease = disease.id !== this.activeId
      
      // 一次性更新所有信息窗口状态，减少DOM重绘次数
      this.$nextTick(() => {
        // 更新信息窗口状态
        this.activeId = disease.id
        this.infoWindowState.position = [lng, lat]
        this.infoWindowState.data = disease
        this.infoWindowState.visible = true
        
        // 如果轮播图存在且是新病害，则设置为第一张图片
        if (this.$refs.carouselRef && isNewDisease) {
          this.$refs.carouselRef.setActiveItem(0)
        }
      })
    },
    closeInfoWindow() {
      if (!this.infoWindowState.visible) {
        return
      }
      
      // 简化动画处理逻辑，直接隐藏窗口
      this.activeId = ''
      this.currentImageIndex = 0
      this.infoWindowState.visible = false
      
      // 发送关闭事件
      this.$emit('infoWindowClosed', this.headerTitle)
    },
    handleImageClick(index) {
      if (index !== null && index !== undefined) {
        this.currentImageIndex = index
        this.initViewer()
      }
    },
    initViewer() {
      const that = this
      if (that.viewer) {
        that.viewer.destroy()
      }

      that.viewer = viewerApi({
        images: this.previewSrcList,
        options: {
          initialViewIndex: this.currentImageIndex,
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: true, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: true, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: true, // 使用 CSS3 过度
          fullscreen: false, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
          zoomRatio: 0.5,
          zIndex: 9999,
          hidden(e) {
            console.log('hidden')
            that.viewer = null
          },
          ready(e) {
            console.log('ready')
            const isFullscreen = that.isElementFullscreen(
              document.getElementById('chengde-screen')
            )
            if (isFullscreen) {
              const container =
                document.getElementsByClassName('viewer-container')[0]
              console.log('container', container)
              that.$refs.targetContainer.appendChild(container)
            }
          },
          viewed(e) {
            console.log('viewed', e)
          },
        },
      })
    },
    formatTime(time) {
      if (!time) return ''
      return dayjs(time).format('YYYY.MM.DD HH:mm:ss')
    },
    /**
     * 检查特定元素是否处于全屏状态
     * @param {HTMLElement} element - 要检查的元素
     * @returns {boolean} 元素是否全屏
     */
    isElementFullscreen(element) {
      const fullscreenElement =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement

      return fullscreenElement === element
    },
    // 添加双击图标处理方法
    markerDblClick(disease) {
      console.log('双击', disease)
      const maxZoom = 20
      if (this.amapInstance) {
        // 使用centerMapOnMarker方法，设置最大缩放级别，并禁用动画
        this.centerMapOnMarker(disease, false, maxZoom)
        this.$nextTick(() => {
          this.showDiseaseDetail(disease, true)
        })
      }
    },
    handleMarkerClick(marker) {
      this.clickTimeout = setTimeout(() => {
        // 如果在定时器触发前没有双击事件发生，则处理单击事件
        this.showDiseaseDetail(marker)
      }, 300)
    },
    handleMarkerDblClick(marker) {
      // 如果双击事件发生，清除单击事件的定时器
      clearTimeout(this.clickTimeout)
      // 处理双击事件
      this.markerDblClick(marker)
    },
    refreshInfoWindowIfNeeded() {
      // 清除之前的计时器
      if (this.infoWindowRefreshTimer) {
        clearTimeout(this.infoWindowRefreshTimer)
        this.infoWindowRefreshTimer = null
      }
      
      // 只在窗口可见时执行刷新
      if (this.infoWindowState.visible) {
        // 使用计时器确保不会频繁刷新
        this.infoWindowRefreshTimer = setTimeout(() => {
          // 禁用动画以避免闪烁
          this.amapInstance.setStatus({animateEnable: false})
          
          // 保存当前位置和数据
          const position = this.infoWindowState.position
          const data = this.infoWindowState.data
          
          // 先隐藏窗口
          this.infoWindowState.visible = false
          
          // 等待DOM更新后再显示窗口
          this.$nextTick(() => {
            this.infoWindowState.position = position
            this.infoWindowState.data = data
            this.infoWindowState.visible = true
            
            // 重置计时器
            this.infoWindowRefreshTimer = null
          })
        }, 50) // 短暂延迟，避免连续刷新
      }
    },
    /**
     * 根据所有marker自动调整地图视野
     * @param {Array} markers - 可选，指定要包含在视野内的marker数组，默认为所有病害点
     * @param {Array} paddings - 可选，视野边缘的内边距，上、下、左、右 默认100、50、50、50
     */
    adjustMapViewToMarkers(markers = null, paddings = [100, 50, 50, 50]) {
      if (!this.amapInstance) return
      
      // 如果没有指定markers，则使用所有病害数据
      if (!markers) {
        // 检查是否有病害数据
        if (!this.diseaseData || this.diseaseData.length === 0) return
        
        // 创建临时标记点数组用于计算视野
        const tempMarkers = this.diseaseData.map(disease => {
          const position = [disease.gpsLongitude || 0, disease.gpsLatitude || 0]
          // 创建一个临时标记点，不添加到地图上，仅用于计算视野
          return new AMap.Marker({
            position: position
          })
        })
        
        // 调整视野以包含所有标记点
        this.amapInstance.setFitView(tempMarkers, true, paddings)
      } else {
        // 使用指定的markers调整视野
        this.amapInstance.setFitView(markers, true, paddings)
      }
    },
    /**
     * 将地图视野居中到指定marker
     * @param {Object} disease - 病害数据对象，包含经纬度信息
     * @param {Boolean} animation - 是否使用动画效果，默认为true
     * @param {Number} zoom - 可选，设置缩放级别，不设置则保持当前缩放级别
     */
    centerMapOnMarker(disease, animation = true, zoom = null) {
      if (!this.amapInstance || !disease) return
      
      const lng = disease.gpsLongitude || 0
      const lat = disease.gpsLatitude || 0
      
      // 设置动画状态
      this.amapInstance.setStatus({animateEnable: animation})
      
      // 如果指定了缩放级别，则设置缩放
      if (zoom !== null) {
        this.amapInstance.setZoomAndCenter(zoom, [lng, lat])
      } else {
        // 仅设置中心点
        this.amapInstance.setCenter([lng, lat])
      }
    },
  },
  beforeDestroy() {
    if (this.viewer) {
      this.viewer.destroy()
    }
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
    
    // 清理所有计时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
    if (this.infoWindowRefreshTimer) {
      clearTimeout(this.infoWindowRefreshTimer)
    }
    if (this.clickTimeout) {
      clearTimeout(this.clickTimeout)
    }
  },
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  &::before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;

    background: url('~@/assets/cheng-de-screen/map-top-bg.png') top no-repeat,
      url('~@/assets/cheng-de-screen/map-right-bg.png') right no-repeat,
      url('~@/assets/cheng-de-screen/map-bottom-bg.png') bottom no-repeat,
      url('~@/assets/cheng-de-screen/map-left-bg.png') left no-repeat;
    background-size: 100% 80px, 80px 100%, 100% 80px, 80px 100%;
    pointer-events: none;
  }
  .animate__fadeIn,
  .animate__fadeOut {
    animation-duration: 0.05s;
  }

  /* 添加更简单的过渡效果 */
  .info-window-transition-enter-active,
  .info-window-transition-leave-active {
    transition: opacity 0.05s ease;
  }
  .info-window-transition-enter,
  .info-window-transition-leave-to {
    opacity: 0;
  }

  .info-window-content {
    width: 355px;
    background-color: rgba(9, 28, 48, 1);

    .info-window-header {
      height: 36px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 30px;
      font-size: 16px;
      background: url('~@/assets/cheng-de-screen/map-header.png') no-repeat;
      background-size: 100% 100%;
      color: white;
    }

    .info-window-body {
      height: calc(100% - 36px);
      overflow-y: auto;
      .info-window-carousel {
        position: relative;
        width: 100%;
        height: 199px;
        background-color: rgba(0, 0, 0, 0.2);
        &-no-image {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #999;
        }
        &-image-count {
          position: absolute;
          bottom: 5px;
          right: 5px;
          background-color: rgba(0, 0, 0, 0.3);
          color: #fff;
          z-index: 100;
          padding: 0 10px;
          font-size: 18px;
          line-height: 27px;
          border-radius: 13px;
        }
      }
      .line {
        width: 100%;
        height: 1px;
        opacity: 0.3;
        border: 1px solid rgba(255, 255, 255, 1);
        margin: 10px 0;
      }
      .disease-info {
        padding: 10px;
        font-size: 14px;
        line-height: 22px;
        .disease-detail {
          display: flex;
          flex-direction: column;
        }
      }

      ::v-deep.el-carousel {
        width: 100%;
        height: 100%;
        margin-bottom: 10px;
        .el-carousel__item {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .el-carousel__indicators {
          bottom: 0px;
        }
        .el-carousel__arrow {
          &:hover {
            background-color: rgba(0, 0, 0, 0.3);
          }
        }
      }
    }

    .info-window-close {
      width: 36px;
      height: 100%;
      cursor: pointer;
      background: url('~@/assets/cheng-de-screen/close.png') no-repeat center center;
      background-size: 14px 14px;
      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

/* 标记容器 */
.marker-wrapper {
  position: relative;
  display: inline-block;
}

/* 新病害指示器 */
.new-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  background-color: #ff5252;
  border-radius: 50%;
  box-shadow: 0 0 5px 2px rgba(255, 82, 82, 0.7);
  animation: pulse 1.5s infinite;
}

/* 跳动动画 */
.bounce-animation {
  animation: bounce 0.5s ease infinite alternate;
  transform-origin: bottom center;
}

@keyframes bounce {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.05);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

.custom-marker {
  width: 39px;
  height: 56px;
  object-fit: cover;
}

/* 激活状态的标记图标样式 */
.active-marker {
  transform: scale(1.1);
  transform-origin: bottom center;
}
</style>
