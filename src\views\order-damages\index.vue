<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <UnitSelect v-model="listQuery.unitId" placeholder="单位名称" useNodeId />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <RoadTypeCascader
            @change="handleRoadTypeCascaderChange"
            placeholder="病害归属/道路"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DiseaseTypeSelect placeholder="病害类型" @change="handleDiseaseTypeSelection" />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="开始时间"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="结束时间"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input v-model.trim="listQuery.filter" placeholder="道路名称、病害描述、行政编码" />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
        <el-button :disabled="listLoading" class="filter-item ml-5">
          <el-checkbox
            :disabled="listLoading"
            class="filter-item ml-5"
            v-model="isSelectAll"
            @change="handleSelectAllChange"
          >选择全部 {{ downloadAllCount }}</el-checkbox>
        </el-button>
        <el-button
          type="success"
          :disabled="listLoading"
          class="filter-item ml-5"
          icon="el-icon-download"
          @click="handleBatchDownload"
        >批量下载</el-button>
      </el-row>
    </div>

    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
      @select="handleSelectionChange"
      @select-all="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="40" />
      <el-table-column align="center" label="病害ID" prop="id" width="80"/>
      <el-table-column align="center" label="单位名称" prop="workUnitName" />
      <el-table-column align="center" label="病害归属" prop="belongToStr" width="100">
        <template slot-scope="{row}">
          <el-tag v-if="row.belongToStr" size="mini" :color="getBelongToColor(row.belongTo)">{{row.belongToStr}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="道路名称" prop="roadName" />
      <el-table-column align="center" label="道路编号" prop="roadCode" width="100" />
      <el-table-column align="center" label="位置描述" prop="addressDescription" />
      <el-table-column align="center" label="病害名称" prop="damageName" width="150" />
      <el-table-column align="center" label="病害图片" width="200">
        <template slot-scope="{ row }">
          <div class="image-container">
            <el-carousel
              v-if="row.images && row.images.length > 0"
              height="100px"
              trigger="click"
              :autoplay="false"
              @change="handleCarouselChange($event, row)"
            >
              <el-carousel-item v-for="(image, index) in row.images" :key="index">
                <el-image
                  :src="image.objectStorageUrlPrefix + image.originalImagePath"
                  fit="contain"
                  @click="handleImageClick(index, row)"
                >
                  <template #error>
                    <div class="item-image-error"></div>
                  </template>
                  <template #placeholder>
                    <i class="el-icon-loading" style="font-size: 14px;"></i>
                  </template>
                </el-image>
              </el-carousel-item>
            </el-carousel>
            <div
              class="count"
              v-if="row.images && row.images.length > 0"
            >{{ row.currentImageIndex + 1 }}/{{ row.images.length }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="病害描述" prop="damageDescription" />
      <el-table-column align="center" label="巡查时间" prop="createTime" width="140">
        <template slot-scope="{row}">{{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</template>
      </el-table-column>
      <el-table-column align="center" label="巡查人员" prop="realName" width="120" />
      <el-table-column align="center" label="操作" width="150">
        <template slot-scope="{row}">
          <el-button type="text" @click="handleDetail(row)">详情</el-button>
          <el-button type="text" @click="handleExport(row.id)">下载</el-button>
          <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="paginationParams.page"
      :limit.sync="paginationParams.size"
      @pagination="getList"
    />
    <damage-detail ref="detailDialog" />
  </div>
</template>

<script>
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination/index.vue'
import {
  getOrderDamageList,
  deleteOrderDamage,
  exportOrderDamage,
} from '@/api/order-damages'
import { exportFile, parseContentDisposition } from '@/utils/index'
import { ROAD_TYPE_OPTIONS, BELONG_TO_COLOR_MAP, getBelongToColor } from '@/utils/cd_constants'
import _ from 'lodash'
import DamageDetail from './detail.vue'
import DateTimePicker from '@/components/DateTimePicker/index.vue'
import DiseaseTypeSelect from '@/components/DiseaseTypeSelect'
import RoadTypeCascader from '@/components/RoadTypeCascader'
import datePickerOptions from '@/mixin/datePickerOptions'

export default {
  name: 'OrderDamages',
  components: {
    Pagination,
    DamageDetail,
    DiseaseTypeSelect,
    RoadTypeCascader,
    DateTimePicker,
  },
  mixins: [datePickerOptions],
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        unitId: null,
        startTime: null,
        endTime: null,
        filter: null,
        belongTo: '',
        roadName: '',
        type: null,
        damageType: null,
        roadType: null,
      },
      cloneListQuery: {},
      paginationParams: {
        page: 1,
        size: 10,
      },
      selectedItems: [],
      isSelectAll: false,
      ignoredIds: [],
      ROAD_TYPE_OPTIONS,
      BELONG_TO_COLOR_MAP,
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
    // 下载全部病害数量
    downloadAllCount() {
      return this.total - this.ignoredIds.length > 0
        ? '(' + (this.total - this.ignoredIds.length) + '条)'
        : ''
    },
  },
  created() {
    console.log('created', BELONG_TO_COLOR_MAP)
    this.getList()
  },

  methods: {
    getList() {
      this.listLoading = true
      let query = {
        ...this.paginationParams,
        ...this.listQuery,
      }
      query.page -= 1
      // 删除空值
      query = _.pickBy(
        query,
        (value) => value !== undefined && value !== null && value !== ''
      )
      getOrderDamageList(query)
        .then((response) => {
          this.list = response.payload.content.map((item) => ({
            ...item,
            currentImageIndex: 0, // 初始化图片索引
          }))
          this.total = response.payload.totalElements

          this.$nextTick(() => {
            if (this.isSelectAll) {
              console.log('getList-忽略的ID', this.ignoredIds)
              // 先清除所有选择
              this.$refs.tableRef.clearSelection()

              // 然后根据ignoredIds选择应该选中的行
              this.list.forEach((row) => {
                // 如果ID不在忽略列表中，则选中该行
                if (!this.ignoredIds.includes(row.id)) {
                  this.$refs.tableRef.toggleRowSelection(row, true)
                }
              })
            } else if (this.selectedItems.length > 0) {
              // 对于非全选模式，根据保存的selectedItems恢复选中状态
              this.$refs.tableRef.clearSelection()

              // 获取当前页面所有项目的ID
              const currentPageIds = this.list.map((item) => item.id)

              // 为当前页面中已选中的项目恢复选中状态
              this.list.forEach((row) => {
                if (this.selectedItems.some((item) => item.id === row.id)) {
                  this.$refs.tableRef.toggleRowSelection(row, true)
                }
              })
            }
          })
        })
        .catch((error) => {
          this.$message.error(error.message)
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    // 病害类型选择
    handleDiseaseTypeSelection({ type, damageType, roadType }) {
      this.listQuery.type = type
      this.listQuery.damageType = damageType
      this.listQuery.roadType = roadType
    },
    handleSelectAllChange() {
      if (this.total <= 0) {
        this.$message.warning('暂无数据，无法选择')
        this.isSelectAll = false
        return
      }
      this.ignoredIds = []
      if (this.isSelectAll) {
        this.$nextTick(() => {
          this.list.forEach((row) => {
            this.$refs.tableRef.toggleRowSelection(row, true)
          })
        })
      } else {
        this.$refs.tableRef.clearSelection()
        this.selectedItems = []
      }
    },
    handleSelectionChange(selection) {
      if (this.isSelectAll) {
        const currentPageIds = this.list.map((item) => item.id)

        const selectedIds = selection.map((item) => item.id)

        this.ignoredIds = this.ignoredIds.filter(
          (id) => !currentPageIds.includes(id)
        )

        const currentPageIgnoredIds = currentPageIds.filter(
          (id) => !selectedIds.includes(id)
        )

        this.ignoredIds = [...this.ignoredIds, ...currentPageIgnoredIds]

        this.ignoredIds = [...new Set(this.ignoredIds)]
        console.log('所有忽略的ID', this.ignoredIds)
      } else {
        // 更新已选项，处理选中和取消选中的情况
        // 1. 当前页面的所有项目ID
        const currentPageIds = this.list.map((item) => item.id)

        // 2. 保留原有的不在当前页面的已选项
        const nonCurrentPageSelected = this.selectedItems.filter(
          (item) => !currentPageIds.includes(item.id)
        )

        // 3. 合并当前页面的选中项和其他页面已选项
        this.selectedItems = [...nonCurrentPageSelected, ...selection]

        console.log('非全部选择 - 保存的选中项', this.selectedItems.length)
        this.ignoredIds = []
      }
    },
    handleBatchDownload() {
      if (!this.selectedItems.length && !this.isSelectAll) {
        this.$message.warning('请至少选择一项进行下载')
        return
      }

      let params = {}
      if (this.isSelectAll) {
        params = {
          allSelected: true,
          ignoredIds: this.ignoredIds,
          ...this.cloneListQuery,
        }
      } else {
        params = {
          selectedIds: this.selectedItems.map((item) => item.id),
        }
      }
      params = _.pickBy(params, (value) => {
        if (value === undefined || value === null || value === '') return false
        if (Array.isArray(value) && value.length === 0) return false
        return true
      })
      const downloadCount = this.isSelectAll
        ? this.total - this.ignoredIds.length
        : this.selectedItems.length

      const notificationMessage = `正在下载${downloadCount}条病害数据，请耐心等待...`
      this.downloadOrderDamage(params, notificationMessage, 'download-batch')
    },
    handleDetail(row) {
      this.$refs.detailDialog.open(row)
    },
    handleExport(id) {
      console.log('handleExport', id)
      
      const params = {
        selectedIds: [id]
      }
      
      const notificationMessage = '正在下载病害数据，请耐心等待...'
      this.downloadOrderDamage(params, notificationMessage, 'download-single')
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteOrderDamage(id)
          .then(() => {
            this.$message.success('删除成功')
            this.getList()
          })
          .catch(() => {
            this.$message.error('删除失败')
          })
      })
    },
    initViewer() {
      const that = this
      if (that.viewer) {
        that.viewer.destroy()
      }
      that.viewer = viewerApi({
        images: this.previewSrcList,
        options: {
          initialViewIndex: this.currentImageIndex,
          inline: true,
          button: true,
          navbar: true,
          title: true,
          toolbar: true,
          tooltip: true,
          movable: true,
          zoomable: true,
          rotatable: false,
          scalable: false,
          transition: false,
          fullscreen: false,
          keyboard: true,
          minZoomRatio: 0.1,
          zIndex: 9999,
          hidden(e) {
            console.log('hidden')
            that.viewer = null
          },
          ready(e) {
            console.log('ready')
          },
          viewed(e) {
            console.log('viewed', e)
          },
        },
      })
    },
    handleImageClick(index, row) {
      console.log('handleImageClick', index, row)
      this.previewSrcList = row.images.map(
        (item) => item.objectStorageUrlPrefix + item.originalImagePath
      )
      this.currentImageIndex = index
      this.initViewer()
    },
    handleCarouselChange($index, row) {
      const index = this.list.findIndex((item) => item.id === row.id)
      this.$set(this.list[index], 'currentImageIndex', $index)
    },
    handleFilter() {
      this.paginationParams.page = 1
      this.isSelectAll = false
      // 重置选中项和忽略项
      this.selectedItems = []
      this.ignoredIds = []

      this.cloneListQuery = _.cloneDeep(this.listQuery)
      this.getList()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleRoadTypeCascaderChange(val) {
      // val: { belongTo, roadName }
      this.listQuery.belongTo = val.belongTo
      this.listQuery.roadName = val.roadName
      // 如有需要可自动触发查询
      // this.getList()
    },
    getBelongToColor(belongTo) {
      return getBelongToColor(belongTo)
    },
    // 下载病害数据公共方法
    downloadOrderDamage(params, message, prefix = 'download') {
      // 创建唯一的下载任务ID
      const taskId = `${prefix}-${Date.now()}-${Math.floor(
        Math.random() * 1000
      )}`

      // 创建下载通知
      const notification = this.$notify({
        title: '下载进行中',
        message: message || '正在下载病害数据，请耐心等待...',
        duration: 0, // 不自动关闭
        position: 'top-right',
        iconClass: 'el-icon-loading',
        id: taskId, // 使用唯一ID
        offset: 0, // 使用动态偏移让通知不会重叠
      })

      return exportOrderDamage(params)
        .then(async (res) => {
          const url = URL.createObjectURL(res.payload)
          const contentDisposition = res.headers['content-disposition']
          const fileName =
            parseContentDisposition(contentDisposition) || '病害数据.xlsx'

          await exportFile(url, fileName)

          // 关闭之前的通知
          notification.close()

          // 显示下载成功通知，使用相同的ID
          this.$notify({
            title: '下载成功',
            message: `文件：${fileName}已下载完成`,
            type: 'success',
            duration: 5000,
            position: 'top-right',
            id: taskId, // 使用相同的ID以替代原通知位置
          })

          return { success: true, fileName }
        })
        .catch((error) => {
          console.error('下载失败', error)

          // 关闭之前的通知
          notification.close()

          // 显示下载失败通知，使用相同的ID
          this.$notify({
            title: '下载失败',
            message: error.statusText || '请稍后重试',
            type: 'error',
            duration: 5000,
            position: 'top-right',
            id: taskId, // 使用相同的ID以替代原通知位置
          })

          return { success: false, error }
        })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-tag {
  color: #333333;
  border: none !important;
  line-height: 20px
}
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  .count {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    z-index: 2;
    padding: 0 10px;
    font-size: 14px;
    line-height: 20px;
    border-radius: 13px;
  }
  ::v-deep .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
  }
}

/* 自适应轮播图指示器样式 */
::v-deep .el-carousel__indicators {
  /* 指示器按钮样式 */
  .el-carousel__button {
    /* 调整指示器大小 */
    width: 8px !important;
    height: 8px !important;
    /* 圆形指示器 */
    border-radius: 50%;
    /* 确保最小尺寸 */
    min-width: 8px;
    min-height: 8px;
  }
}

/* 如果需要指示器水平排列 */
::v-deep .el-carousel__indicators--horizontal {
  display: flex;
  justify-content: center;
  /* 调整指示器间距 */
  .el-carousel__indicator {
    margin: 0 3px !important;
  }
}

</style>
