/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:54
 * @LastEditors: guowy
 * @LastEditTime: 2021-02-19 17:53:45
 */
import Vue from 'vue'
import Router from 'vue-router'

import Layout from '@/layout/index.vue'

Vue.use(Router)
/**
 * 注意: 子菜单仅在路由children.length> = 1时出现
 * 查看详情: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   当设置 true 的时候该路由  现 如401，login等页面，或者如一些编辑页面/edit/1(默认 false)
 * alwaysShow: true               当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式
 *                                只有一个时，会将那个子路由当做根路由显示在侧边栏
 *                                若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    title: 'title'               设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'/'el-icon-x'             设置该路由的图标
    breadcrumb: false            如果设置为false，则不会在breadcrumb面包屑中显示，默认true
    noCache: true                如果设置为true，该页面将不会被缓存
    affix: true                  如果设置为true，该标签页将固定展示
    activeMenu: '/example/list'  菜单高亮
    onlyOneTag:true              如果设置为true，则相同路由名字的的标签页只能有一个
  }
 */
/**
 * constantRoutes
 * 没有权限要求的基本页面
 * 所有角色都可以访问
 */

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    hidden: true, // 不在侧边栏展示
  },
  {
    path: '/password',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'change',
        component: () => import('@/views/user/password.vue'),
        name: 'ChangePassword',
        meta: {
          title: '修改密码',
          noCache: true,
        },
      },
    ],
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401.vue'),
    hidden: true,
  },
  {
    path: '/atlas',
    component: Layout,
    hidden: false,
    children: [
      {
        path: 'pictures',
        component: () => import('@/views/data/pictures.vue'),
        name: 'DataPictures',
        meta: {
          title: '道路图集',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/atlas',
    component: Layout,
    hidden: false,
    children: [
      {
        path: 'landscape',
        component: () => import('@/views/data/landscape.vue'),
        name: 'DataLandscape',
        meta: {
          title: '景观图集',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/road',
    component: Layout,
    hidden: false,
    children: [
      {
        path: '',
        component: () => import('@/views/road/index.vue'),
        name: 'Road',
        meta: {
          title: '道路管理',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/data/detail/:taskId',
    component: Layout,
    hidden: false,
    children: [
      {
        path: '',
        component: () => import('@/views/data/detail.vue'),
        name: 'DataDetail',
        meta: {
          title: '数据详情',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/order-records/detail/:taskId',
    component: Layout,
    hidden: false,
    children: [
      {
        path: '',
        component: () => import('@/views/order-records/detail.vue'),
        name: 'OrderRecordsDetail',
        meta: {
          title: '巡查详情',
          noCache: false,
        },
        props: (route) => ({ id: route.params.id, data: route.query.data }),
      },
    ],
  },
  {
    path: '/inspection',
    component: Layout,
    hidden: false,
    children: [
      {
        path: 'detail/:taskId',
        component: () => import('@/views/data/detail.vue'),
        name: 'DataDetail',
        meta: {
          title: '巡检详情',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/system/detail',
    component: Layout,
    hidden: false,
    children: [
      {
        path: '',
        component: () => import('@/views/system/detail.vue'),
        name: 'SystemDetail',
        meta: {
          title: '系统详情',
          noCache: false,
        },
      },
    ],
  },
  {
    path: '/agreement',
    component: () => import('@/views/agreement/index.vue'),
    hidden: true, // 不在侧边栏展示
  },

]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
})

const router = createRouter()
// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
export default router
