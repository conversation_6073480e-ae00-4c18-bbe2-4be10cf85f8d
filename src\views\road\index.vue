<template>
  <!-- scale-to-original -->
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-input v-model="listQuery.roadCode" placeholder="道路编号" clearable />
      <el-input v-model="listQuery.roadName" placeholder="道路名称" clearable />
      <el-select v-model="listQuery.roadLevel" clearable filterable placeholder="道路等级">
        <el-option v-for="(leve, index) in roadLevelOptions" :key="index" :value="leve.value" :label="leve.label" />
      </el-select>
      <el-select v-model="listQuery.roadType" clearable filterable placeholder="道路类型">
        <el-option v-for="(type, index) in roadTypeOptions" :key="index" :value="type.value" :label="type.label" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
      <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">添加道路</el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border highlight-current-row style="width: 100%;">
      <el-table-column align="center" label="道路编号" prop="roadCode" width="150" />
      <el-table-column align="center" label="行政区划编码" prop="adCode" width="150" />
      <el-table-column align="center" label="道路名称" prop="roadName" width="200" />
      <el-table-column align="center" label="道路等级" prop="roadLevel" width="150">
        <template slot-scope="scope">{{ levelMap[scope.row.roadLevel] }}</template>
      </el-table-column>
      <el-table-column align="center" label="道路类型" prop="roadType" width="200">
        <template slot-scope="scope">{{ typeMap[scope.row.roadType] }}</template>
      </el-table-column>
      <el-table-column align="center" label="检测方向" prop="detectDirection" width="150">
        <template slot-scope="scope">{{ directionMap[scope.row.detectDirection] }}</template>
      </el-table-column>
      <el-table-column align="center" label="起点桩号" prop="startPileNum" width="200" />
      <el-table-column align="center" label="止点桩号" prop="endPileNum" width="200" />
      <el-table-column align="center" label="操作">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleUpdate(row, $index)">修改</el-button>
          <el-button type="text" @click="handleUpload(row, $index)">导入公里桩</el-button>
          <el-button type="text" @click="handleUpload(row, $index)">导入百米桩</el-button>
          <el-button type="text" @click="handleDelete(row, $index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.currentPage" :limit.sync="listQuery.pageSize" />
    <CreateDialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" @refreshData="onRefreshData" />
    <UploadDialog ref="uploadDialog" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { mapState } from 'vuex'
import axios from 'axios'
import CreateDialog from './components/create-dialog.vue'
import UploadDialog from './components/upload-dialog.vue'

export default {
  name: 'Road',
  components: {
    Pagination,
    CreateDialog,
    UploadDialog,
  },
  data() {
    return {
      // list: null,
      list: [
        {
          id: 11,
          adCode: 222,
          roadName: '大屯东路',
          roadCode: 1,
          roadType: 2,
          roadLevel: 1,
          pavementWidth: 1,
          detectDirection: 1,
          startPileNum: 1,
          endPileNum: 1,
        },
      ],
      total: 0,
      temp: {},
      listLoading: false,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnit: null,
        roadName: null,
        roadCode: null,
        roadType: null,
        roadLevel: null,
        pavementWidth: null,
        startTime: null,
        endTime: null,
      },

      directionMap: {
        1: '上行',
        2: '下行',
      },
      levelMap: {
        1: '一级公路',
        2: '二级公路',
        3: '三级公路',
        4: '四级及以下公路',
        5: '高速公路',
      },
      typeMap: {
        1: '沥青路面',
        2: '水泥混凝土路面',
      },

      roadTypeOptions: [
        {
          label: '沥青路面',
          value: '1',
        },
        {
          label: '水泥混凝土路面',
          value: '2',
        },
      ],
      roadLevelOptions: [
        {
          label: '高速公路',
          value: '5',
        },
        {
          label: '一级公路',
          value: '1',
        },
        {
          label: '二级公路',
          value: '2',
        },
        {
          label: '三级公路',
          value: '3',
        },
        {
          label: '四级及以下公路',
          value: '4',
        },
      ],
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      workUnit: (state) => state.account.workUnit,
    }),
  },
  created() {
    axios.get('road/getRoad').then((res) => {
      console.log('//getRoad', res)
    })
  },
  methods: {
    handleUpdate(row) {
      console.log(row)
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
    handleDelete(row) {
      this.$confirm('确定要删除该道路吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        // await deleteDevice(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.list = []
        // this.getList()
      })
    },
    handleFilter() {
      this.listQuery.currentPage = 1
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    handleUpload() {
      this.$refs.uploadDialog.show()
    },
    getList() {
      this.listLoading = true
      const { pageSize, currentPage, workUnit } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        workUnit: workUnit === '' ? null : workUnit,
      }
      // getDevices(query).then((response) => {
      //   this.list = response.payload.content
      //   this.total = response.payload.totalElements
      //   this.listLoading = false
      // })
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    onRefreshData(row) {
      // this.handleDataRefresh(row)
    },
  },
}
</script>

<style lang='' scoped>
/* @import url(); 引入css类 */
</style>
