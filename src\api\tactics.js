/*
 * @Author: guowy
 * @Date: 2020-05-27 11:03:57
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-14 17:02:08
 */

import request from '@/utils/request'

export function getTactics(query) {
  return request({
    url: '/data-strategies',
    method: 'get',
    params: query,
  })
}

export function getTactic(id) {
  return request({
    url: `/data-strategies/${id}`,
    method: 'get',
  })
}

export function createTactic(data) {
  return request({
    url: '/data-strategies',
    method: 'post',
    data,
  })
}

export function updateTactic(data) {
  return request({
    url: `/data-strategies/${data.id}`,
    method: 'put',
    data,
  })
}

export function deleteTactic(id) {
  return request({
    url: `/data-strategies/${id}`,
    method: 'delete',
  })
}
