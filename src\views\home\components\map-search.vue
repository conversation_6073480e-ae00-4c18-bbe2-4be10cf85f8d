<template>
  <div class="map-search" :class="{ 'is-expanded': isExpanded }">
    <div class="search-icon" v-if="!isExpanded" @click="toggleSearch">
      <i class="el-icon-search"></i>
    </div>
    <div v-if="isExpanded" class="search-container">
      <el-input
        ref="searchInput"
        v-model="searchText"
        placeholder="输入设备名称"
        class="search-input"
        clearable
        @input="debounceSearch"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-d-arrow-left" class="arrow-left" @click="toggleSearch"></el-button>
      </el-input>

      <div v-if="searchText.trim()" class="search-results">
        <template v-if="searchResults.length">
          <div
            v-for="(item, index) in searchResults"
            :key="index"
            class="search-result-item"
            @click="handleSelectResult(item)"
          >
            <span>{{ item.deviceName }}</span>
          </div>
        </template>
        <div v-else class="search-result-empty">
          <i class="el-icon-info-circle"></i>
          <span>暂无匹配设备</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapSearch',
  props: {
    devices: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchText: '',
      searchResults: [],
      maxZoom: 19,
      isExpanded: false,
      searchTimer: null,
    }
  },
  methods: {
    toggleSearch() {
      this.isExpanded = !this.isExpanded
      if (this.isExpanded) {
        this.$nextTick(() => {
          if (this.$refs.searchInput) {
            this.$refs.searchInput.focus()
          }
        })
      } else {
        // 关闭时清空搜索内容和结果
        this.searchText = ''
        this.searchResults = []
      }
    },
    debounceSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.handleSearch()
      }, 300)
    },
    handleSearch() {
      this.searchResults = []

      if (!this.searchText.trim()) return

      const keyword = this.searchText.toLowerCase()
      const matchedDevices = this.devices.filter(
        (device) =>
          device.deviceName && device.deviceName.toLowerCase().includes(keyword)
      )

      this.searchResults = matchedDevices.map((device) => ({
        type: 'device',
        deviceKey: device.deviceKey,
        deviceName: device.deviceName,
        lat: device.gpsLatitude,
        lng: device.gpsLongitude,
      }))
    },

    handleSelectResult(item) {
      this.$emit('select-location', item)
      this.searchResults = []
      this.searchText = ''
      this.isExpanded = false
    },
  },
}
</script>

<style lang="scss" scoped>
.map-search {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  transition: all 0.3s ease;

  &.is-expanded {
    width: 300px;
  }

  .search-icon {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    cursor: pointer;

    i {
      font-size: 20px;
      color: #409eff;
    }
  }
  .arrow-left {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-icon-d-arrow-left {
      font-size: 20px;
      color: #409eff;
    }
  }

  .search-container {
    position: relative;
    width: 100%;
  }

  .search-input {
    width: 100%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .search-results {
    margin-top: 5px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;

    .search-result-item {
      padding: 10px 15px;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: #f5f7fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }

    .search-result-empty {
      padding: 15px;
      text-align: center;
      color: #909399;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        margin-right: 5px;
        font-size: 16px;
      }
    }
  }
}
</style>