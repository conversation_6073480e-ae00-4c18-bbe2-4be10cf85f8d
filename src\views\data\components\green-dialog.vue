/*
 * @Author: wangyj
 * @Date: 2022-11-24 13:34:47
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-24 16:46:20
 */

<template>
  <el-dialog
    title="绿视率"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="90%"
  >
    <div style="height: 100%">
      <div class="operation-container">
        <el-button v-if="!isApplying" @click="handleStartModelCall">开启模型调用</el-button>
        <el-button v-if="isApplying" @click="handlePauseModelCall">暂停模型调用</el-button>
      </div>
      <el-row :gutter="25" class="picture-row">
        <el-col v-for="(picture, i) in pictures" :key="i" :span="25">
          <div class="picture-container">
            <img v-if="picture.picture" :src="picture.picture">
            <div v-if="!picture.picture" class="empty-div">暂无图片</div>
          </div>
          <div class="desc">{{ picture.distance }}m &nbsp;&nbsp;&nbsp;&nbsp;绿视率: {{ (picture.greenRate * 100).toFixed(1) }} %</div>
        </el-col>
      </el-row>
      <Pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.currentPage"
        :limit.sync="listQuery.pageSize"
        layout="total, prev, pager, next, jumper"
        @pagination="getList"
      />
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getGLR, applyGreenRateModal } from '@/api/data'
import { startModelCall, pauseModelCall, getModelCallStatus } from '@/api/model'

export default {
  components: { Pagination },
  data() {
    return {
      taskId: null,
      dialogVisible: false,
      total: 0,
      listQuery: {
        currentPage: 1,
        pageSize: 15,
      },
      pictures: [],
      photoAlbumType: null, // 图集类型
      modelId: null, // 模型id
      isApplying: false,
      timer: null,
    }
  },
  watch: {
    dialogVisible: {
      async handler(val, oldVal) {
        if (val) {
          if (await this.getModelCallStatusAjax()) {
            this.isApplying = true
          } else {
            this.isApplying = false
            this.handleStartModelCall()
          }
          this.getList()
        }
      },
    },
  },
  methods: {
    show(taskId, photoAlbumType, modelId) {
      this.taskId = taskId
      this.photoAlbumType = photoAlbumType
      this.modelId = modelId
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      clearTimeout(this.timer)
    },
    async getList() {
      const { currentPage, pageSize } = this.listQuery
      const params = {
        taskId: this.taskId,
        photoAlbumType: this.photoAlbumType,
        modelId: this.modelId,
        page: currentPage - 1,
        size: pageSize,
      }

      const { payload } = await getGLR(params)
      this.pictures = payload.content
      this.total = payload.totalElements

      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getList()
      }, 10000)
    },
    async handleCallModel() {
      this.$message({
        message: '模型调用中，请勿关闭当前窗口',
        type: 'warning',
      })
      const { status } = await applyGreenRateModal({ taskId: this.taskId })
      if (status === 200) {
        this.$message({
          message: '模型调用成功',
          type: 'success',
        })
        this.getList()
      }
    },
    handleStartModelCall() {
      this.isApplying = true
      const postData = {
        taskId: this.taskId,
        photoAlbumType: this.photoAlbumType,
        modelId: this.modelId,
      }
      startModelCall(postData)
    },
    handlePauseModelCall() {
      this.isApplying = false
      const postData = {
        taskId: this.taskId,
        photoAlbumType: this.photoAlbumType,
        modelId: this.modelId,
      }
      pauseModelCall(postData)
    },
    async getModelCallStatusAjax() {
      const params = {
        taskId: this.taskId,
        photoAlbumType: this.photoAlbumType,
        modelId: this.modelId,
      }
      const { payload } = await getModelCallStatus(params)
      return payload
    },
  },
}

</script>
<style lang='scss' scoped>
    @import '@/styles/mixin.scss';

    ::v-deep .el-dialog {
        margin-top: 5vh!important;
        height: 90vh;

        .el-dialog__body {
            height: calc(100% - 54px);
            padding: 10px 20px 30px;
        }
    }

    .operation-container {
       margin-bottom: 10px;
       text-align: right;
    }
    .picture-row {
      height: calc(100% - 90px);
      overflow-y: auto;
      @include scrollBar;
    }

    ::v-deep .el-col-25 {
        width: 20%;
        margin-bottom: 10px;

        img {
            display: block;
            width: 100%;
            aspect-ratio: 16/9;
            height: auto;
            object-fit: cover;
            border-radius: 6px;
        }

         .empty-div {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            aspect-ratio: 16/9;
            height: auto;
            border-radius: 6px;
            border: 1px solid #ccc;
            color: #9b9b9b;

        }

        .desc {
            padding: 12px 0;
        }

        &:nth-of-type(12),
        &:nth-of-type(13),
        &:nth-of-type(14),
        &:nth-of-type(15) {
          margin-bottom: 0;
        }
    }

    .pagination-container {
      padding: 18px 0 0;
    }
</style>
