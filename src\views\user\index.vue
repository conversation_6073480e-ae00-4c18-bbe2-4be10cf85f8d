/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:13
 * @Last Modified by:   wangyj
 * @Last Modified time: 2022-10-25 18:02:13
 */

<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <UnitSelect v-model="listQuery.workUnitId" useNodeId />
        </el-col> 
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-input
            v-model="listQuery.username"
            clearable
            placeholder="请输入账号"
            class="filter-item"
          />
        </el-col> 
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item ml-10" type="success" icon="el-icon-plus" @click="handleCreate">
          添加账号
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="单位名称" prop="workUnit" />
      <el-table-column align="center" label="所属区域" prop="regionProvince">
        <template slot-scope="{row}">
          {{ setRegion(row) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="账号" prop="username" />
      <el-table-column align="center" label="账号权限">
        <template slot-scope="{row}">
          {{ row.role.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间">
        <template slot-scope="{row}">
          {{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备（台）">
        <template slot-scope="{row}">
          <el-button type="text" @click="handleToDevicePage(row)">{{ row.deviceAmount }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleUpdate(row)">修改</el-button>
          <el-button type="text" @click="handleDelete(row,$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <create-dialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" />
  </div>
</template>

<script>

import { getUsers, deleteUser } from '@/api/user'
import Pagination from '@/components/Pagination/index.vue'
import CreateDialog from './components/create-dialog.vue'

export default {
  name: 'User',
  components: { Pagination, CreateDialog },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        username: '',
        workUnitId: null,
      },
      temp: {},
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, username, workUnitId,
      } = this.listQuery

      const query = {
        page: currentPage - 1,
        size: pageSize,
        username: username.trim() === '' ? null : username,
        workUnitId
      }
      getUsers(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该账号吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteUser(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getList()
      })
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleToDevicePage(row) {
      this.$router.push({
        path: 'device',
        query: {
          workUnitId: row.workUnitId,
        },
      })
    },
    setRegion(row) {
      const { regionProvince, regionCity, regionDistrict } = row
      return !regionProvince ? '-' : `${regionProvince}${regionCity || ''}${regionDistrict || ''}`
    },
  },
}
</script>
