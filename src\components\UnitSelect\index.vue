<template>
  <treeselect
    class="el-select el-select--medium"
    zIndex="99999"
    :options="unitList"
    :value="value"
    :normalizer="normalizer"
    :placeholder="placeholder"
    :disabled="disabled"
    :no-results-text="noResultsText"
    @input="updateValue"
  />
</template>

<script>
import { mapState } from 'vuex'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  components: { Treeselect },
  props: {
    value: {
      type: [String, Number],
      default: null,
    },
    useNodeId: {
      type: <PERSON>olean,
      default: false,
    },
    showOnlyParents: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '请选择所属单位'
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "200px",
    },
    noResultsText: {
      type: String,
      default: '暂无数据',
    },
  },
  computed: {
    ...mapState('unit', ['unitList']),
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val === '' || val === undefined) {
          this.$emit('input', null);
        }
      },
    },
  },
  methods: {
    normalizer(node) {
      if (!node) return {};
      
      return {
        id: this.useNodeId ? node.id : node.unitName,
        label: node.unitName,
        children: this.showOnlyParents ? undefined : 
          (node.childWorkUnits && node.childWorkUnits.length ? node.childWorkUnits : undefined),
      };
    },
    updateValue(newValue) {
      this.$emit('input', newValue);
    },
  },
  async created() {
    await this.$store.dispatch('unit/fetchUnitList')
  },
};
</script>

<style scoped lang="scss">
::v-deep .vue-treeselect__single-value { 
  font-size: 14px !important;
  color: #606266 !important;
}

::v-deep .vue-treeselect__placeholder {
  font-size: 14px !important;
  color: #c0c4d6 !important;
}

::v-deep .vue-treeselect__label { 
  font-size: 14px !important;
  font-weight: normal;
  color: #606266;
}

/* 修改聚焦样式 */
::v-deep .vue-treeselect__control:focus{
  border-color: #409EFF !important;
  border-width: 2px !important;
  box-shadow: none !important;
}


/* 也可以修改悬停样式 */
::v-deep .vue-treeselect__control:hover {
  border-color: #C0C4CC !important;
}
</style>
