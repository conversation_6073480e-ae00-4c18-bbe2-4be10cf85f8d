<!--
 * @Author: guowy
 * @Date: 2020-11-11 09:48:45
 * @LastEditors: guowy
 * @LastEditTime: 2020-11-11 13:32:46
-->
<template>
  <div>
    <el-col :span="8" class="item-container">
      <div class="label">{{ label?`${label}：`:'' }}</div>
      <div class="input">
        <slot />
      </div>
    </el-col>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: '',
    },
  },
}
</script>

<style lang="scss">
  .item-container{
    display: flex;
    flex-direction: row;
    .input{
      flex: 1;
      .el-select{
        width: 100%;
      }
    }
    .label{
      font-size: 14px;
      color: #515a6e;
      flex-shrink: 0;
      line-height: 32px;
      padding-right: 12px;
    }
  }
</style>
