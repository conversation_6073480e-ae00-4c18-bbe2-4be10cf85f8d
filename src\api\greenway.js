import request from '@/utils/request'

// 获取所有巡检过的绿道名称列表
export function getInspectedRoadNames() {
  return request({
    url: '/inspect-tasks/lvDao/roadNames',
    method: 'get',
  })
}

// 根据roadName查询绿道存在的巡检列表
export function getInspectTasks(params) {
  return request({
    url: '/inspect-tasks/lvDao/inspectList',
    method: 'get',
    params,
  })
}

// 根据taskId查询巡检任务的具体轨迹
export function getInspectImageInfer(params) {
  return request({
    url: '/inspect-tasks/lvDao/inspectImageInfer',
    method: 'get',
    params,
  })
}
