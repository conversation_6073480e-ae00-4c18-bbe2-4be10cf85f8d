<template>
  <!-- 标注Dialog 开始-->
  <el-dialog
    v-loading="loading"
    element-loading-text="数据加载中"
    element-loading-background="rgba(0, 0, 0, 0.5)"
    :visible.sync="dialogVisible"
    :width="(cW + 30 + 300) + 'px'"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template slot="title">
      <div class="dialog-t">
        <div>{{ title }}</div>
        <div />
      </div>
    </template>
    <div ref="dialogContainer" class="dialog-container" tabindex="0">
      <div class="dialog-left" :style="{height: `${cH}px`}">
        <el-form ref="dataForm" :model="form" :rules="rules" size="small">
          <el-form-item label="图像宽（像素）：">
            <div>{{ img && img.width }}</div>
          </el-form-item>
          <el-form-item label="图像高（像素）：">
            <div>{{ img && img.height }}</div>
          </el-form-item>
          <el-form-item label="标定点1和点2的距离：" prop="widthActual">
            <el-input v-model="form.widthActual" />
          </el-form-item>
          <el-form-item label="标定点1和2连线距画面底部距离：" prop="heightActual">
            <el-input v-model="form.heightActual" />
          </el-form-item>
          <el-form-item label="标定点1坐标（x,y）：">
            <div v-if="origTrapezoidData.length">（{{ origTrapezoidData[0].x.toFixed(1) }}, {{ origTrapezoidData[0].y.toFixed(1) }}）</div>
          </el-form-item>
          <el-form-item label="标定点2坐标（x,y）：">
            <div v-if="origTrapezoidData.length">（{{ origTrapezoidData[3].x.toFixed(1) }}, {{ origTrapezoidData[3].y.toFixed(1) }}）</div>
          </el-form-item>
          <el-form-item label="标定点3坐标（x,y）：">
            <div v-if="origTrapezoidData.length">（{{ origTrapezoidData[2].x.toFixed(1) }}, {{ origTrapezoidData[2].y.toFixed(1) }}）</div>
          </el-form-item>
          <el-form-item label="标定点4坐标（x,y）：">
            <div v-if="origTrapezoidData.length">（{{ origTrapezoidData[1].x.toFixed(1) }}, {{ origTrapezoidData[1].y.toFixed(1) }}）</div>
          </el-form-item>
        </el-form>
      </div>
      <canvas
        id="calibrateCanvas"
        ref="calibrateCanvas"
        tabindex="0"
        :width="cW"
        :height="cH"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </span>
  </el-dialog>
  <!-- 标注Dialog 结束-->
</template>

<script>
// import { draw, sendThis } from '@/utils/draw'
import CanvasMixin from '@/mixin/canvas'
import { calibration, getPhotoMarkConfig, setPhotoMarkConfig } from '@/api/data'

export default {
  mixins: [CanvasMixin],
  props: {
    deviceKey: {
      type: String,
    },
  },
  data() {
    return {
      title: 'R模型面积计算标定',
      dialogVisible: false,
      dialogDataObj: {},
      calibrateCanvas: null,
      calibrateCtx: null,
      flag: false,
      img: null,
      cW: '640',
      cH: '360',
      pageImage: {
        imgScale: 1, // canvas实际默认为1
      },
      beforePos: {},
      afterPos: {},
      lockInit: true,
      loading: false,
      trapezoidData: null, // 标定梯形数据(转换后的),map对象
      origTrapezoidData: [], // 原始标定梯形数据,
      form: {},
      rules: {
        widthActual: [{ required: true, message: '请输入距离', trigger: 'blur' }],
        heightActual: [{ required: true, message: '请输入距离', trigger: 'blur' }],
      },
    }
  },
  methods: {
    calcImage(img) {
      this.pageImage.imgScale = this.cH / img.height
    },
    loadImage() {
      this.img = new Image()
      this.updateImageUrl()
      return this.initImage()
    },
    updateImageUrl() {
      this.img.setAttribute('crossOrigin', 'anonymous')
      this.img.crossOrigin = 'anonymous'
      this.img.src = this.dialogDataObj.pictureUrl
      // this.img.setAttribute('crossOrigin', 'anonymous')
    },
    initImage(type = true) {
      const that = this
      const img = new Image()
      img.src = this.img.src
      return new Promise((resolve, reject) => {
        img.onload = function () {
          if (type) {
            that.calcImage(img)
          }
          resolve()
        }
      })
    },
    async drawImage() {
      const that = this
      that.calibrateCtx.clearRect(0, 0, this.cW, this.cH)
      that.calibrateCtx.drawImage(
        that.img,
        0, 0,
        that.img.width, that.img.height,
        0, 0,
        that.cW, that.cH,
      )

      const configs = await that.getCalibrateConfigs()
      if (configs && configs.photoWidth === that.img.width && configs.photoHeight === that.img.height) {
        const {
          widthActual, heightActual, leftTopPointX, leftTopPointY, leftBottomPointX, leftBottomPointY, rightBottomPointX, rightBottomPointY, rightTopPointX, rightTopPointY,
        } = configs

        that.form = { widthActual, heightActual }
        // 初始化梯形数据（逆时针点）
        that.trapezoidData = new Map([
          [1, { x: leftTopPointX * that.pageImage.imgScale, y: leftTopPointY * that.pageImage.imgScale }],
          [2, { x: leftBottomPointX * that.pageImage.imgScale, y: leftBottomPointY * that.pageImage.imgScale }],
          [3, { x: rightBottomPointX * that.pageImage.imgScale, y: rightBottomPointY * that.pageImage.imgScale }],
          [4, { x: rightTopPointX * that.pageImage.imgScale, y: rightTopPointY * that.pageImage.imgScale }],
        ])
        that.origTrapezoidData = [{
          x: leftTopPointX, y: leftTopPointY,
        }, {
          x: leftBottomPointX, y: leftBottomPointY,
        }, {
          x: rightBottomPointX, y: rightBottomPointY,
        }, {
          x: rightTopPointX, y: rightTopPointY,
        }]
      } else {
        if (configs && configs.widthActual && configs.heightActual) {
          that.form = { widthActual: configs.widthActual, heightActual: configs.heightActual }
          that.$alert('图像分辨率发生改变，请重新标定', '重要提示', {
            confirmButtonText: '确定',
            showClose: false,
            closeOnPressEscape: false,
            type: 'warning',
          })
        }
        // 初始化梯形数据（逆时针点）
        that.trapezoidData = new Map([
          [1, { x: 0, y: (that.img.height / 2) * that.pageImage.imgScale }],
          [2, { x: 0, y: that.img.height * that.pageImage.imgScale }],
          [3, { x: that.img.width * that.pageImage.imgScale, y: that.img.height * that.pageImage.imgScale }],
          [4, { x: that.img.width * that.pageImage.imgScale, y: (that.img.height / 2) * that.pageImage.imgScale }],
        ])
        that.origTrapezoidData = [{
          x: 0, y: (that.img.height / 2),
        }, {
          x: 0, y: that.img.height,
        }, {
          x: that.img.width, y: that.img.height,
        }, {
          x: that.img.width, y: (that.img.height / 2),
        }]
      }

      // 画梯形
      that.drawTrapezoid()

      that.onMouseMove()
    },
    async show(picture) {
      const that = this

      await that.clearDialogData()
      this.dialogVisible = true
      this.dialogDataObj = picture

      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
        that.$refs.dialogContainer.focus()
        const lH = document.body.clientHeight * 0.9 - 70 - 66
        this.cW = parseInt((lH * 16) / 9, 10)
        this.cH = parseInt(lH, 10)

        this.calibrateCanvas = document.getElementById('calibrateCanvas')
        this.calibrateCtx = this.calibrateCanvas.getContext('2d')

        if (picture.picState === 1) {
          this.loadImage().then(() => {
            that.drawImage()
          })
        } else {
          this.calibrateCtx.clearRect(0, 0, this.cW, this.cH)
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
      // 恢复初始值
      this.clearDialogData()
      this.calibrateCanvas.onmousedown = null
      this.calibrateCanvas.onmousemove = null
      this.calibrateCanvas.onmouseup = null
      this.calibrateCanvas.onmouseout = null
      this.calibrateCanvas.onkeydown = null
      this.calibrateCtx.clearRect(0, 0, this.calibrateCanvas.width, this.calibrateCanvas.height)
    },
    async clearDialogData() {
      this.pageImage = {
        imgScale: 1, // canvas实际默认为1
      }
      this.beforePos = {}
      this.afterPos = {}
      this.trapezoidData = null
      this.origTrapezoidData = []
      this.form = {}
    },
    async handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '标定数据提交中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          const that = this
          const tempData = { ...this.form }
          tempData.widthActual = parseFloat(tempData.widthActual)
          tempData.heightActual = parseFloat(tempData.heightActual)
          tempData.deviceKey = that.deviceKey
          tempData.photoWidth = that.img.width
          tempData.photoHeight = that.img.height
          tempData.leftTopPointX = parseFloat(that.origTrapezoidData[0].x.toFixed(1))
          tempData.leftTopPointY = parseFloat(that.origTrapezoidData[0].y.toFixed(1))
          tempData.rightTopPointX = parseFloat(that.origTrapezoidData[3].x.toFixed(1))
          tempData.rightTopPointY = parseFloat(that.origTrapezoidData[3].y.toFixed(1))
          tempData.rightBottomPointX = parseFloat(that.origTrapezoidData[2].x.toFixed(1))
          tempData.rightBottomPointY = parseFloat(that.origTrapezoidData[2].y.toFixed(1))
          tempData.leftBottomPointX = parseFloat(that.origTrapezoidData[1].x.toFixed(1))
          tempData.leftBottomPointY = parseFloat(that.origTrapezoidData[1].y.toFixed(1))
          tempData.modelSource = 1

          setPhotoMarkConfig(tempData).then((res) => {
            if (res.status === 200) {
              loading.close()
              this.$message({
                message: '标定成功',
                type: 'success',
              })
            } else {
              loading.close()
              this.$message({
                message: '标定失败',
                type: 'error',
              })
            }
          }, (res) => {
            loading.close()
          })
        }
      })

      // 提交数据
    },
    onMouseMove() {
      const that = this
      const point1 = that.trapezoidData.get(1)
      const point2 = that.trapezoidData.get(2)
      const point3 = that.trapezoidData.get(3)
      const point4 = that.trapezoidData.get(4)
      // 变量初始化
      let sX = 0 // 鼠标X坐标
      let sY = 0 // 鼠标Y坐标

      that.calibrateCanvas.onmousemove = function (em) {
        sX = em.offsetX
        sY = em.offsetY

        if (sX < point1.x + 6 && sX > point1.x - 6 && sY < point1.y + 6 && sY > point1.y - 6) {
        // ***  鼠标在起点角  ***
          that.changeDraw(1)
        } else if (sX < point4.x + 6 && sX > point4.x - 6 && sY < point4.y + 6 && sY > point4.y - 6) {
        // ***  鼠标在起点横向角  ***
          that.changeDraw(4)
        } else if (sX < point2.x + 6 && sX > point2.x - 6 && sY < point2.y + 6 && sY > point2.y - 6) {
        // ***  鼠标在起点纵向角  ***
          that.changeDraw(2)
        } else if (sX < point3.x + 6 && sX > point3.x - 6 && sY < point3.y + 6 && sY > point3.y - 6) {
        // ***  鼠标在终点角  ***
          that.changeDraw(3)
        } else {
          // that.drawTrapezoid()
          that.calibrateCanvas.style.cursor = 'default'
          that.onMouseMove()
        }
      }
      /* 鼠标移出画布区域时保存选中矩形下标(如有) */
      that.calibrateCanvas.onmouseout = function (eo) {
        that.calibrateCanvas.style.cursor = 'default'
        that.onMouseMove()
      }
    },
    /* 编辑矩形四个角 */
    changeDraw(site) {
      const that = this
      that.calibrateCanvas.style.cursor = 'pointer'
      // site: 操作矩形角的位置（逆时针）, 1-起点 2-起点纵向 3-起点对角点 4-起点横向

      that.calibrateCanvas.onmousedown = function (ed) {
        // 保存鼠标落下位置的X, Y坐标, firefox中鼠标移动后ed.offsetX ed.offsetY会变成 0, 需要使用临时参数存储起来
        const sX = ed.offsetX // 起点X坐标
        const sY = ed.offsetY // 起点Y坐标

        /* 移动鼠标 */
        that.calibrateCanvas.onmousemove = function (em) {
          let mX = em.offsetX // 移动后的X坐标
          let mY = em.offsetY // 移动后的Y坐标

          if (em.offsetX < 0) {
            mX = 0
          } else if (em.offsetX > that.cW) {
            mX = that.cW
          }

          if (em.offsetY > that.cH) {
            em.offsetY = that.cH
          }
          // 计算绘制数据
          // eslint-disable-next-line default-case
          switch (site) {
          case 1:
            if (mX >= that.trapezoidData.get(4).x) {
              mX = that.trapezoidData.get(4).x - 10
            }
            that.trapezoidData.set(1, { x: mX, y: that.trapezoidData.get(1).y })
            that.origTrapezoidData[0].x = mX / that.pageImage.imgScale
            break
          case 2:
            // if (mX >= that.trapezoidData.get(3).x) {
            //   mX = that.trapezoidData.get(3).x - 10
            // }
            if (mY <= that.trapezoidData.get(1).y) {
              mY = that.trapezoidData.get(1).y + 10
            }
            that.trapezoidData.set(2, { x: that.trapezoidData.get(2).x, y: mY })
            // that.origTrapezoidData[1].x = mX / that.pageImage.imgScale
            that.origTrapezoidData[1].y = mY / that.pageImage.imgScale
            break
          case 3:
            // if (mX <= that.trapezoidData.get(2).x) {
            //   mX = that.trapezoidData.get(2).x + 10
            // }
            if (mY <= that.trapezoidData.get(1).y) {
              mY = that.trapezoidData.get(1).y + 10
            }
            that.trapezoidData.set(3, { x: that.trapezoidData.get(3).x, y: mY })
            // that.origTrapezoidData[2].x = mX / that.pageImage.imgScale
            that.origTrapezoidData[2].y = mY / that.pageImage.imgScale
            break
          case 4:
            if (mX <= that.trapezoidData.get(1).x) {
              mX = that.trapezoidData.get(1).x + 10
            }
            that.trapezoidData.set(4, { x: mX, y: that.trapezoidData.get(4).y })
            that.origTrapezoidData[3].x = mX / that.pageImage.imgScale
            break
          }

          // 重新绘制
          that.drawTrapezoid()

          that.calibrateCanvas.onmouseup = function (eu) {
            that.calibrateCanvas.style.cursor = 'default'
            that.calibrateCanvas.onmousedown = null
            that.calibrateCanvas.onmousemove = null
            that.onMouseMove()
          }
        }

        /* 鼠标离开矩形区 */
        that.calibrateCanvas.onmouseout = function (eo) {
          let mX = eo.offsetX // 移动后的X坐标
          let mY = eo.offsetY // 移动后的Y坐标

          if (eo.offsetX < 0) {
            mX = 0
          } else if (eo.offsetX > that.cW) {
            mX = that.cW
          }

          if (eo.offsetY > that.cH) {
            eo.offsetY = that.cH
          }
          // 计算绘制数据
          // eslint-disable-next-line default-case
          switch (site) {
          case 1:
            if (mX >= that.trapezoidData.get(4).x) {
              mX = that.trapezoidData.get(4).x - 10
            }
            that.trapezoidData.set(1, { x: mX, y: that.trapezoidData.get(1).y })
            that.origTrapezoidData[0].x = mX / that.pageImage.imgScale
            break
          case 2:
            // if (mX >= that.trapezoidData.get(3).x) {
            //   mX = that.trapezoidData.get(3).x - 10
            // }
            if (mY <= that.trapezoidData.get(1).y) {
              mY = that.trapezoidData.get(1).y + 10
            }
            that.trapezoidData.set(2, { x: that.trapezoidData.get(2).x, y: mY })
            // that.origTrapezoidData[1].x = mX / that.pageImage.imgScale
            that.origTrapezoidData[1].y = mY / that.pageImage.imgScale
            break
          case 3:
            // if (mX <= that.trapezoidData.get(2).x) {
            //   mX = that.trapezoidData.get(2).x + 10
            // }
            if (mY <= that.trapezoidData.get(1).y) {
              mY = that.trapezoidData.get(1).y + 10
            }
            that.trapezoidData.set(3, { x: that.trapezoidData.get(3).x, y: mY })
            // that.origTrapezoidData[2].x = mX / that.pageImage.imgScale
            that.origTrapezoidData[2].y = mY / that.pageImage.imgScale
            break
          case 4:
            if (mX <= that.trapezoidData.get(1).x) {
              mX = that.trapezoidData.get(1).x + 10
            }
            that.trapezoidData.set(4, { x: mX, y: that.trapezoidData.get(4).y })
            that.origTrapezoidData[3].x = mX / that.pageImage.imgScale
            break
          }
          that.drawTrapezoid()
          that.onMouseMove()
        }
      }
    },
    drawTrapezoid() {
      const that = this

      that.calibrateCtx.clearRect(0, 0, that.calibrateCanvas.width, that.calibrateCanvas.height)
      that.calibrateCtx.drawImage(
        that.img,
        0, 0,
        that.img.width, that.img.height,
        0, 0,
        that.cW, that.cH,
      )

      // 绘制梯形（默认是矩形）
      const point1 = that.trapezoidData.get(1)
      const point2 = that.trapezoidData.get(2)
      const point3 = that.trapezoidData.get(3)
      const point4 = that.trapezoidData.get(4)

      // that.calibrateCtx.beginPath()
      that.calibrateCtx.lineWidth = 2
      that.calibrateCtx.setLineDash([20, 20])
      that.calibrateCtx.strokeStyle = '#fff'
      // 设置路径起点坐标
      that.calibrateCtx.moveTo(point1.x, point1.y)
      // 定义中间点坐标1
      that.calibrateCtx.lineTo(point2.x, point2.y)
      // 定义中间点坐标2
      that.calibrateCtx.lineTo(point3.x, point3.y)
      // 定义中间点坐标3(这是最后一个中间点，也就是终点)
      that.calibrateCtx.lineTo(point4.x, point4.y)
      // 关闭绘制路径
      that.calibrateCtx.closePath()
      // 按照绘制路径顺序连接各个坐标点
      that.calibrateCtx.stroke()

      // 绘制四个角的圆圈
      that.calibrateCtx.beginPath()
      that.calibrateCtx.setLineDash([])
      that.calibrateCtx.arc(
        point1.x,
        point1.y,
        6, 0, Math.PI * 2,
      )
      that.calibrateCtx.fillStyle = '#fff'
      that.calibrateCtx.fill()// 画起点实心圆
      that.calibrateCtx.stroke()
      that.calibrateCtx.beginPath()
      that.calibrateCtx.arc(
        point2.x,
        point2.y,
        6, 0, Math.PI * 2,
      )
      that.calibrateCtx.fill()// 画起点纵向实心圆
      that.calibrateCtx.stroke()
      that.calibrateCtx.beginPath()
      that.calibrateCtx.arc(
        point3.x,
        point3.y,
        6, 0, Math.PI * 2,
      )
      that.calibrateCtx.fill()// 画起点横向实心圆
      that.calibrateCtx.stroke()
      that.calibrateCtx.beginPath()
      that.calibrateCtx.arc(
        point4.x,
        point4.y,
        6, 0, Math.PI * 2,
      )
      that.calibrateCtx.fill()// 画终点实心圆
      that.calibrateCtx.stroke()

      // 绘制文字 --- 后台是顺时针顺序
      that.calibrateCtx.font = 'bold 16px "微软雅黑"'
      that.calibrateCtx.textAlign = 'left'
      that.calibrateCtx.fillText(`点1`, point1.x, point1.y - 16)

      that.calibrateCtx.textAlign = 'left'
      that.calibrateCtx.fillText(`点4`, point2.x, point2.y - 16)

      that.calibrateCtx.textAlign = 'right'
      that.calibrateCtx.fillText(`点3`, point3.x, point3.y - 16)

      that.calibrateCtx.textAlign = 'right'
      that.calibrateCtx.fillText(`点2`, point4.x, point4.y - 16)
    },
    async getCalibrateConfigs() {
      const params = {
        deviceKey: this.deviceKey,
        modelSource: 1,
      }
      const { payload } = await getPhotoMarkConfig(params)
      if (payload) {
        return payload
      }
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';
::v-deep .el-dialog {
    margin-top: 3vh!important;

    .el-dialog__body {
        padding: 0px 15px 15px;
    }
    .el-dialog__headerbtn {
      top: 16px!important;
      font-size: 24px!important;
    }
    .el-dialog__footer {
      padding: 15px;
    }
}

.dialog-t {
    display: flex;
    justify-content: space-between;
    padding-right: 30px;
    align-items: flex-end;

   div:nth-child(2){
        font-size: 12px;
        color: #9b9b9b;
    }
}

.dialog-container {
  display: flex;

  .dialog-left {
    width: 300px;
    padding-right: 10px;
    overflow-y: auto;
    @include scrollBar;
  }

}

</style>
