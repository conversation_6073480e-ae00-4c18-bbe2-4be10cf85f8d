/*
 * @Author: wangyj
 * @Date: 2023-03-02 17:30:14
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-03-03 09:45:51
 */

<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">
        添加app版本
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="定制版">
        <template slot-scope="{row}">
          <span>{{ row.isCustomized ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="定制单位" prop="customizedUnit" />
      <el-table-column align="center" label="最新版本号" prop="latestVersionName" />
      <el-table-column align="center" label="下载地址" prop="updateUrl" />
      <el-table-column align="center" label="更新内容" prop="description" />
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleUpdate(row)">修改</el-button>
          <el-button type="text" @click="handleDelete(row,$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <CreateDialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" />
  </div>
</template>

<script>
import { getVersionList, deleteAppVersion } from '@/api/system'
import Pagination from '@/components/Pagination/index.vue'
import CreateDialog from './components/create-dialog.vue'

export default {
  name: 'AppVersion',
  components: { Pagination, CreateDialog },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
      },
      temp: {},
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const { pageSize, currentPage } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
      }
      getVersionList(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该版本吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteAppVersion(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getList()
      })
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleToDevicePage(row) {
      this.$router.push({
        path: 'device',
        query: {
          workUnit: row.workUnit,
        },
      })
    },
  },
}
</script>
