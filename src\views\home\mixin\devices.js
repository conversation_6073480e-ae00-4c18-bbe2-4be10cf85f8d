import { getDeviceLatestLocation, getDevicelatestRoute } from '@/api/home'
import { getDataList, getDevicesDetail } from '@/api/data'
import { mapState } from 'vuex'

const icons = {
  patrol: {
    on: {
      left: require('@/assets/patrol_on_left.png'),
      right: require('@/assets/patrol_on.png')
    },
    off: {
      left: require('@/assets/patrol_off_left.png'),
      right: require('@/assets/patrol_off.png')
    }
  },
  car: {
    on: require('@/assets/car_on.png'),
    off: require('@/assets/car_off.png'),
  },
}
// 首页-非管理员、管理员所使用的混入
const DevicesLatest = {
  data() {
    return {
      map: null,
      BMap: null,
      center: { lng: 0, lat: 0 },
      zoom: 3,
      curTab: 1, // 1 设备， 2 路线
      oldDevices: [],
      rightAsideWidth: '620px',
      timer: null,
      lines: [],
      tableHeight: 600,
      hasClass: false,
      devices: [],
      curDevice: {},
      styles: [{
        url: require('@/assets/test.gif'),
        size: { width: 53, height: 53 },
        textColor: '#6fc034',
      }],
      styles1: [{
        url: require('@/assets/test1.gif'),
        size: { width: 53, height: 53 },
        textColor: '#6fc034',
      }],
      secondCenter: {
        x: 0,
        y: 0,
      },
      listQuery: {
        currentPage: 1,
        pageSize: 10,
      },
      isShow: false, // 设备巡检历史信息是否显示
      loading: false,
      inspectionListData: [],
      total: 0,
      videoUrl: '',
      diseaseWindow: {
        // 病害信息窗体
        position: {},
        title: '病害',
        show: false,
        url: '',
        notUpload: false,
        scale: 1,
        btnsType: 1, // 1病害，2道路资产
        errorTxt: '',
        photoTime: null,
      },
      activeTab: 1,
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      homeNum: (state) => state.account.homeNum,
      workUnit: (state) => state.account.workUnit,
      owningRegion: (state) => state.account.owningRegion,
    }),
  },
  async created() {
    if (this.curTab === 1) {
      this.getDevicesLatestAjax(true)

      this.timer = setInterval(() => {
        this.getDevicesLatestAjax(false)
      }, 3000)
    } else {
      this.getRouteLatestAjax()
    }
  },
  mounted() {
    this.initTableHeight()
    window.addEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  activated() {
    this.getDevicesLatestAjax(false)
    clearInterval(this.timer)
    this.timer = setInterval(() => {
      this.getDevicesLatestAjax(false)
    }, 1000)
  },
  deactivated() {
    clearInterval(this.timer)
    this.timer = null
    window.removeEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  destroyed() {
    this.map.removeOverlay()
    // clearTimeout(this.timer)
    clearInterval(this.timer)
    this.timer = null

    window.removeEventListener('resize', () => {
      this.initTableHeight()
    })
  },
  methods: {
    initTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = document.getElementsByClassName('app-main')[0].clientHeight - 404
      })
    },
    handleInfoWindowOpen() {
      this.diseaseWindow.show = true
      this.$refs.diseaseWindow.redraw()
    },
    handleInfoWindowClose() {
      this.diseaseWindow.show = false
      this.$refs.diseaseWindow.redraw()
    },
    handleInfoWindowClickClose() {
      this.videoUrl = ''
      this.diseaseWindow.show = false
      this.diseaseWindow.position = {}
      this.$refs.diseaseWindow.redraw()
    },
    async getDevicesLatestAjax(isFirst) {
      const that = this
      const { payload } = await getDeviceLatestLocation()
      this.oldDevices = [...this.devices]
      this.devices = payload
      if (isFirst) { // isFirst 代表初次加载
        if (this.admin) {
          this.map.centerAndZoom(new BMap.Point(104.40797, 37.80331), 6)
        }
      } else {
        this.setRotation()
      }

      // 更新面板中的信息
      if (this.isShow) {
        const curDeviceArr = payload.filter((item) => item.deviceKey === that.curDevice.deviceKey)

        if (curDeviceArr.length) {
          that.$set(that.curDevice, 'using', curDeviceArr[0].using)
        }
      }

      if (!that.admin) {
        that.setRunningLineData(payload)
      }
    },
    // 点击设备marker 显示设备信息
    async handleShow(device) {
      this.curDevice = device
      this.$set(this.curDevice, 'show', true)
      this.videoUrl = ''

      this.getVideoUrlAjax(device.deviceKey)
      const centerPixel = this.map.pointToPixel(this.map.getCenter())
      this.secondCenter.x = centerPixel.x - 500 / 2
      this.secondCenter.y = centerPixel.y
      const curPixel = this.map.pointToPixel(new this.BMap.Point(device.gpsLongitude, device.gpsLatitude))

      this.listQuery.currentPage = 1
      await this.getInspectionListAjax()
      this.activeTab = 1
      this.isShow = true
      this.map.panBy(-(curPixel.x - this.secondCenter.x), -(curPixel.y - this.secondCenter.y))
    },
    handleHide() {
      this.isShow = false
      this.curDevice = {}
    },
    async handleDetail(row, index) {
      await this.$store.dispatch('detail/setTaskData', row.id)
      this.$router.push(`/inspection/detail/${row.id}`)
    },
    async getRouteLatestAjax() {
      // const bounds = this.map.getBounds()
      // const sw = bounds.getSouthWest() // 西南角
      // const ne = bounds.getNorthEast() // 东北角
      clearTimeout(this.timer)
      const { payload } = await getDevicelatestRoute()

      this.lines = []
      const allPoints = []

      payload.forEach((item, index) => {
        item.route.forEach((r) => {
          let color = ''
          if (r.pqi >= 90) {
            color = this.GLOBLE_COLORS.colorA
          } else if (r.pqi >= 80 && r.pqi < 90) {
            color = this.GLOBLE_COLORS.colorB
          } else if (r.pqi >= 70 && r.pqi < 80) {
            color = this.GLOBLE_COLORS.colorC
          } else if (r.pqi >= 60 && r.pqi < 70) {
            color = this.GLOBLE_COLORS.colorD
          } else {
            color = this.GLOBLE_COLORS.colorE
          }
          this.lines.push({
            line: [{
              lng: r.lngStart,
              lat: r.latStart,
            }, {
              lng: r.lngEnd,
              lat: r.latEnd,
            }],
            color,
          })

          allPoints.push(new this.BMap.Point(r.lngStart, r.latStart), new this.BMap.Point(r.lngEnd, r.latEnd))
        })
      })

      // 地图视野包含所有点
      this.$nextTick(() => {
        this.map.setViewport(allPoints, {
          margins: [20, 20, 20, 20],
        })
      })

      this.timer = setTimeout(() => {
        this.getRouteLatestAjax()
      }, 1000)
    },

    async getInspectionListAjax() {
      this.loading = true
      const {
        pageSize, currentPage,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        deviceKey: this.curDevice.deviceKey,
      }
      const { payload } = await getDataList(query)
      this.inspectionListData = payload.content
      this.total = payload.totalElements
      this.loading = false
    },
    async getVideoUrlAjax(deviceKey) {
      const { payload } = await getDevicesDetail({ deviceKey })
      this.videoUrl = payload?.videoUrl
    },
    // 设置小车旋转角度
    setRotation() {
      const that = this
      // 这个阈值主要是为了避免 GPS 数据微小抖动导致方向频繁变化 单位：像素
      const MOVEMENT_THRESHOLD = 3;

      // 计算车载设备的旋转角度
      function calculateCarRotation(dx, dy) {
        let deg = 0;
        if (dx !== 0) {
          const tan = dy / dx;
          const atan = Math.atan(tan);
          deg = (atan * 360) / (2 * Math.PI);
          if (dx < 0) {
            deg = -deg + 180; 
          } else {
            deg = -deg;
          }
        } else {
          // x坐标相同，垂直方向移动
          deg = dy > 0 ? 90 : -90;
        }
        // 加上180度，使车头朝左
        return (deg + 180) % 360;
      }

      this.devices.forEach((newD) => {
        // 如果设备是车载设备(deviceModel != 1)且有gpsHeading属性，直接使用它
        if (newD.deviceModel != 1 && newD.gpsHeading !== undefined && newD.gpsHeading !== null) {
          // 使用gpsHeading值，并加上180度使车头朝左
          const deg = (newD.gpsHeading + 180) % 360;
          that.$set(newD, 'rotation', -deg);
          return;
        }

        const oldD = that.oldDevices.find((item) => newD.deviceKey === item.deviceKey);
        if (!oldD) return; 

        const curPosP = {
          lng: oldD.gpsLongitude,
          lat: oldD.gpsLatitude,
        };
        const targetPosP = {
          lng: newD.gpsLongitude,
          lat: newD.gpsLatitude,
        };
        
        const curPoint = new BMap.Point(curPosP.lng, curPosP.lat);
        const curPos = that.map.pointToPixel(curPoint);
        const tarPoint = new BMap.Point(targetPosP.lng, targetPosP.lat);
        const targetPos = that.map.pointToPixel(tarPoint);
        
        // 计算移动向量和距离 单位：像素
        const dx = targetPos.x - curPos.x;
        const dy = targetPos.y - curPos.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // 如果移动距离小于阈值，保持原角度不变
        console.log(distance, MOVEMENT_THRESHOLD)
        if (distance < MOVEMENT_THRESHOLD) return;
        
        if (newD.deviceModel == 1) {
          // 电动车（慢行设备）设置方向属性，用于选择不同图标
          // 判断水平方向是向左还是向右
          if (Math.abs(dx) > Math.abs(dy)) {
            // 主要是水平运动，根据水平方向设置朝向
            that.$set(newD, 'direction', dx >= 0 ? 'right' : 'left');
          } else {
            // 主要是垂直运动，保持上一次的方向
            // 如果没有上一次的方向，则使用微小的水平分量
            if (oldD.direction) {
              that.$set(newD, 'direction', oldD.direction);
            } else {
              that.$set(newD, 'direction', dx >= 0 ? 'right' : 'left');
            }
          }
          // 不设置rotation，保持不旋转
          that.$set(newD, 'rotation', 0);
        } else {
          // 车载设备，使用连续旋转角度
          const deg = calculateCarRotation(dx, dy);
          that.$set(newD, 'rotation', -deg);
        }
      });
    },
    getDeviceIcon(device) {
      const isPatrol = device.deviceModel == 1
      const iconType = isPatrol ? 'patrol' : 'car'
      const status = device.using ? 'on' : 'off'
      
      let url;
      if (isPatrol) {
        // 电动车根据方向选择不同图标
        const direction = device.direction === 'left' ? 'left' : 'right';
        url = icons[iconType][status][direction];
      } else {
        // 车载设备使用原来的图标
        url = icons[iconType][status];
      }
      
      const icon = {
        url: url,
        size: { width: isPatrol ? 62 : 69, height: isPatrol ? 46 : 51 },
        opts: { imageSize: { width: isPatrol ? 62 : 69, height: isPatrol ? 46 : 51 } },
      }
      return icon
    },
  },
}

export default DevicesLatest
