import store from '@/store'

/**
 * 用户权限指令
 * @directive el-table-column 目前必须使用v-if="$auth"，不能使用v-auth
 * @directive 单个权限验证（v-auth="xxx"）
 * @directive 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
 * @directive 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
 */

// 单个权限验证
export const auth = {
  inserted(el, binding) {
    if (!AUTH(binding.value)) el.parentNode.removeChild(el)
  },
}

// 多个权限验证，满足一个则显示
export const auths = {
  inserted(el, binding) {
    if (!AUTHS(binding.value)) el.parentNode.removeChild(el)
  },
}

// 多个权限验证，全部满足则显示
export const authAll = {
  inserted(el, binding) {
    if (!AUTH_ALL(binding.value)) el.parentNode.removeChild(el)
  },
}

/**
 * 用户权限方法
 * @method AUTH 单个权限验证
 * @method AUTHS 多个权限验证，满足一个则返回true
 * @method AUTH_ALL 多个权限验证，全部满足则返回true
 */

// 单个权限验证
export function AUTH(permission) {
  const userPermissions = store.getters.permissions || []
  return userPermissions.includes(permission)
}

// 多个权限验证，满足一个则返回true
export function AUTHS(permissions) {
  // console.log('AUTHS-permissions', permissions)
  const userPermissions = store.getters.permissions || []
  return permissions.some((p) => userPermissions.includes(p))
}

// 多个权限验证，全部满足则返回true
export function AUTH_ALL(permissions) {
  const userPermissions = store.getters.permissions || []
  return permissions.every((p) => userPermissions.includes(p))
}

// 将权限方法注入全局
export default {
  install(Vue) {
    Vue.prototype.$auth = AUTH
    Vue.prototype.$auths = AUTHS
    Vue.prototype.$authAll = AUTH_ALL
  },
}
