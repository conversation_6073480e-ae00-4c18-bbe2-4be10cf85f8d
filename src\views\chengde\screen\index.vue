<template>
  <div class="chengde-screen" id="chengde-screen">
    <div class="chengde-screen-container">
      <Header @fullscreenChange="handleFullscreenChange" />
      <div class="maps-container">
        <div class="disease-list-section">
          <DiseaseList
            ref="diseaseListRef"
            :diseaseData="diseaseData"
            :newDiseaseIds="newDiseaseIds"
            @clickDiseaseListImage="handleClickDiseaseListImage"
            @handletimeRangeChange="handleTimeRangeChange"
            @handleDiseaseTypeChange="handleDiseaseTypeChange"
            @handleRoadTypeChange="handleRoadTypeChange"
            @diseaseDataUpdated="handleDiseaseDataUpdated"
            @filterChangeComplete="restartAutoRefresh"
          />
        </div>
        <div class="right-section">
          <div class="total-inspection">
            <div class="total-inspection-item" v-for="item in totalInspection" :key="item.type" :style="{ backgroundImage: `url(${item.icon})` }">
              <span class="label">{{ item.label }}累计巡查</span>
              <span class="km">{{ item.mileage }} <i class="unit">km</i></span>
            </div>
          </div>
          <div class="map-section" id="main-road">
            <Map
              ref="roadRef"
              headerTitle="主干道病害分布"
              :isFullscreen="isFullscreen"
              :diseaseData="diseaseData.content"
              :newDiseaseIds="newDiseaseIds"
              @infoWindowClosed="cancelHighlight"
            />
          </div>
        </div>
    
      </div>
    </div>
  </div>
</template>

<script>
import Header from './header.vue'
import Map from './map.vue'
import DiseaseList from './disease-list.vue'
import _ from 'lodash'
import { getOrderStatises } from '@/api/order-statises'
import autofit from '@/utils/autofit.js'
import { getBelongToByStatisType, STATIS_TYPE_MAP } from '@/utils/cd_constants'
import EventBus from '@/utils/eventBus'

// 导入里程图标
import mainKm from '@/assets/cheng-de-screen/main-km.png'
import subKm from '@/assets/cheng-de-screen/sub-km.png'
import slKm from '@/assets/cheng-de-screen/sl-km.png'
import gxKm from '@/assets/cheng-de-screen/gx-km.png'
import dsKm from '@/assets/cheng-de-screen/ds-km.png'


export default {
  components: {
    Header,
    Map,
    DiseaseList,
  },
  data() {
    return {
      diseaseData: {
        content: [], // 病害数据
        totalElements: 0,
        totalPages: 0,
        loading: false,
      },
      imageIndex: 0,
      isFullscreen: false,
      // 定时器ID、刷新间隔和新数据标记
      refreshTimer: null,
      refreshInterval: 10000, // 10秒钟刷新一次
      newDiseaseIds: [], // 存储新出现的病害ID
      screenScale: 1,
      // token刷新定时器
      tokenRefreshTimer: null,
      tokenRefreshInterval: 20 * 60 * 1000, // 20分钟刷新一次token
      totalInspection: [
        {
          // 使用STATIS_TYPE_MAP中的键作为statisType，确保与映射一致
          statisTarget: STATIS_TYPE_MAP.main_road, // STATIS_TYPE_MAP.main_road
          icon: mainKm,
          label: '主干道',
          mileage: 0,
        },
        {
          statisTarget: STATIS_TYPE_MAP.sec_road, // STATIS_TYPE_MAP.sec_road
          icon: subKm,
          label: '次干道',
          mileage: 0,
        },
        {
          statisTarget: STATIS_TYPE_MAP.ad_code_130803, // STATIS_TYPE_MAP.ad_code_130803
          icon: slKm,
          label: '双滦区',
          mileage: 0,
        },
        {
          statisTarget: STATIS_TYPE_MAP.ad_code_130871, // STATIS_TYPE_MAP.ad_code_130871
          icon: gxKm,
          label: '高新区',
          mileage: 0,
        },
        {
          statisTarget: STATIS_TYPE_MAP.dongshan_ppp, // STATIS_TYPE_MAP.dongshan_ppp
          icon: dsKm,
          label: '东山ppp项目',
          mileage: 0,
        },
      ],
    }
  },
  async mounted() {
    this.initAutofit()
    
    // 获取巡查统计数据
    this.handleGetOrderStatises()
    
    // 设置定时刷新
    this.startAutoRefresh()
    // 设置token刷新
    this.startTokenRefresh()
    
    // 监听里程数据更新事件
    EventBus.$on('orderStatiseUpdated', this.handleGetOrderStatises)
  },
  activated() {
    // console.log('缓存')
    this.startAutoRefresh()
    this.startTokenRefresh()
  },
  deactivated() {
    // console.log('离开缓存')
    this.stopAutoRefresh()
    this.stopTokenRefresh()
  },
  // 缓存离开时清除定时器
  beforeDestroy() {
    console.log('销毁')
    // 清除定时器
    this.stopAutoRefresh()
    this.stopTokenRefresh()
    this.autofitDestroy()
    
    // 移除事件监听
    EventBus.$off('orderStatiseUpdated', this.handleGetOrderStatises)
  },
  methods: {
    initAutofit() {
      autofit.init(
        {
          el: '.chengde-screen-container',
          parentEl: '.chengde-screen',
          allowScroll: true, // 解决其他页面不滚动问题
          resize: true,
        },
        false
      )
      this.screenScale = autofit.scale
      window.addEventListener('resize', () => {
        console.log('窗口大小变化', autofit.scale)
        this.screenScale = autofit.scale
      })
    },
    autofitDestroy() {
      if (autofit) {
        autofit.off()
      }
    },
    // 启动自动刷新
    startAutoRefresh() {
      // 清除可能存在的定时器
      this.stopAutoRefresh()
      // 设置新的定时器
      this.refreshTimer = setInterval(() => {
        // 通知disease-list组件刷新数据
        if (this.$refs.diseaseListRef) {
          this.$refs.diseaseListRef.fetchDiseaseList(true)
        }
      }, this.refreshInterval)
    },
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    // 重新启动自动刷新（在筛选完成后调用）
    restartAutoRefresh() {
      console.log('筛选完成，重新启动自动刷新')
      // 清除可能存在的定时器
      this.stopAutoRefresh()
      // 设置新的定时器，延迟一段时间后再开始自动刷新，确保不会与筛选操作冲突
      setTimeout(() => {
        this.startAutoRefresh()
      }, 1000) // 延迟1秒后重新启动
    },
    // 启动token刷新
    startTokenRefresh() {
      // 清除可能存在的定时器
      this.stopTokenRefresh()
      // 设置新的定时器，每20分钟刷新一次token
      this.tokenRefreshTimer = setInterval(async () => {
        console.log('刷新token')
        // 使用refresh_token刷新token
        if (this.$user && this.$user.api.refreshToken) {
          const refreshToken = localStorage.getItem('refresh_token')
          if (refreshToken) {
            const res = await this.$user.api.refreshToken({ refreshToken })
            this.$user.setToken(res.payload.accessToken)
            localStorage.setItem('refresh_token', res.payload.refreshToken)
          }
        }
      }, this.tokenRefreshInterval)
    },
    // 停止token刷新
    stopTokenRefresh() {
      if (this.tokenRefreshTimer) {
        clearInterval(this.tokenRefreshTimer)
        this.tokenRefreshTimer = null
      }
    },
    handleClickDiseaseListImage(data) {
      const { disease, imageIndex } = data
      this.imageIndex = imageIndex
      this.$refs.roadRef.showDiseaseDetail(disease)
    },
    handleTimeRangeChange() {
      // 关闭信息窗口
      this.handleInfoWindowClosed()
    },
    handleDiseaseTypeChange() {
      // 关闭信息窗口
      this.handleInfoWindowClosed()
    },
    handleRoadTypeChange() {
      // 关闭信息窗口
      this.handleInfoWindowClosed()
    },
    handleInfoWindowClosed() {
      this.$refs.roadRef.closeInfoWindow()
    },
    // 获取巡查统计数据
    handleGetOrderStatises() {
      getOrderStatises().then((res) => {
        if (res.payload.length) {
          res.payload.forEach(item => {
            const mappedType = getBelongToByStatisType(item.statisTarget);
            
            // 直接更新totalInspection数组中对应项的mileage值
            const inspectionItem = this.totalInspection.find(
              inspItem => inspItem.statisTarget === mappedType
            )
            
            if (inspectionItem) {
              inspectionItem.mileage = item.inspectMileage || 0
              console.log('更新里程:', inspectionItem.label, inspectionItem.mileage)
            }
          })
        }
        console.log('this.totalInspection', this.totalInspection)
      })
    },
    handleFullscreenChange(isFullscreen) {
      this.isFullscreen = isFullscreen
    },
    // 取消病害列表高亮
    cancelHighlight(headerTitle) {
      const roadType = headerTitle === '主干道病害分布' ? 'main' : 'sub'
      this.$refs.diseaseListRef.clearHighlight(roadType)
    },
    handleDiseaseDataUpdated(updatedData) {
      // 更新疾病数据
      this.diseaseData.content = updatedData.data.content
      this.diseaseData.totalElements = updatedData.data.totalElements
      this.diseaseData.totalPages = updatedData.data.totalPages
      
      // 更新新病害ID
      if (updatedData.newDiseaseIds && updatedData.newDiseaseIds.length > 0) {
        this.newDiseaseIds = updatedData.newDiseaseIds
        
        // 5秒后清除新数据标记
        setTimeout(() => {
          this.newDiseaseIds = []
        }, 5000)
      }
    },
  },
}
</script>

<style lang="scss">
.chengde-screen {
  width: 100%;
  height: calc(100vh - 94px);
  background-color: #040d2e;
  color: #fff;
  .chengde-screen-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.maps-container {
  background-color: #040d2e;
  flex: 1;
  min-height: 0;
  display: flex;
  gap: 20px;
  padding: 20px;
  .disease-list-section {
    width: 384px;
    height: 100%;
  }
  .right-section {
    width: calc(100% - 384px);
    height: 100%;
    display: flex;
    flex-direction: column;
    .total-inspection {
      width: 100%;
      height: 70px;
      background-color: #040d2e;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      .total-inspection-item {
        width: 355px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        color: #fff;
        padding-left: 44px;
        padding-right: 10px;
        .label {
          font-size: 16px;
        }
        .km {
          font-size: 28px;
          
        }
        .unit {
          font-size: 14px;
          font-style: normal;
        }
      }
    }
    .map-section {
      flex: 1;
    }
  }
}

.el-image {
  width: 100%;
  height: 100%;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 去掉loading的背景色
.el-loading-mask {
  background-color: transparent !important;
}

.amap-container {
  background-color: transparent !important;
  background-position: center;
}
</style>
