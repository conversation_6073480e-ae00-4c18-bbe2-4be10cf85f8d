/*
 * @Author: wangyj
 * @Date: 2022-11-16 18:00:44
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-17 11:05:32
 */

<template>
  <el-dialog title="编辑巡检信息" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="5vh">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
      mar
      style="max-height: 70vh;overflow-y: auto;"
    >
      <el-form-item label="设备单位" prop="workUnit">
        <el-input v-model="temp.workUnit" placeholder="请输入设备单位" disabled />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="temp.deviceName" placeholder="请输入设备名称" disabled />
      </el-form-item>
      <el-form-item label="巡检开始时间" prop="startTime">
        <el-input v-model="temp.startTime" placeholder="请输入巡检开始时间" disabled />
      </el-form-item>
      <el-form-item label="巡检结束时间" prop="endTime">
        <el-input v-model="temp.endTime" placeholder="请输入巡检结束时间" disabled />
      </el-form-item>
      <el-form-item label="巡检里程（km）" prop="inspectMileage">
        <el-input v-model="temp.inspectMileage" placeholder="请输入巡检里程" disabled />
      </el-form-item>
      <el-form-item label="行政区划编码" prop="adCode">
        <el-input v-model="temp.adCode" placeholder="请输入行政区划编码" />
      </el-form-item>
      <el-form-item label="路线编号" prop="roadCode">
        <el-input v-model="temp.roadCode" placeholder="请输入路线编号" />
      </el-form-item>
      <el-form-item label="路线名称" prop="roadName">
        <el-input v-model="temp.roadName" placeholder="请输入路线名称" />
      </el-form-item>
      <el-form-item label="检测宽度（m）" prop="pavementWidth">
        <el-input v-model="temp.pavementWidth" placeholder="请输入路面宽度" />
      </el-form-item>
      <el-form-item label="道路等级" prop="roadLevel">
        <el-radio-group v-model="temp.roadLevel">
          <el-radio :label="6">城市道路</el-radio>
          <el-radio :label="5">高速公路</el-radio>
          <el-radio :label="1">一级公路</el-radio>
          <el-radio :label="2">二级公路</el-radio>
          <el-radio :label="3">三级公路</el-radio>
          <el-radio :label="4">四级及以下公路</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="路面类型" prop="roadType">
        <el-radio-group v-model="temp.roadType">
          <el-radio :label="1">沥青路面</el-radio>
          <el-radio :label="2">水泥混凝土路面</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="检测方向" prop="detectDirection">
        <el-radio-group v-model="temp.detectDirection">
          <el-radio :label="1">上行</el-radio>
          <el-radio :label="2">下行</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="起点桩号" prop="startPileNum">
        <el-input v-model="temp.startPileNum" placeholder="请输入起点桩号" />
      </el-form-item>
      <el-form-item label="止点桩号" prop="endPileNum">
        <el-input v-model="temp.endPileNum" placeholder="请输入止点桩号" disabled />
      </el-form-item>
      <el-form-item label="检测单位" prop="detectUnit">
        <el-input v-model="temp.detectUnit" placeholder="请输入检测单位" />
      </el-form-item>
      <el-form-item label="委托单位" prop="delegateUnit">
        <el-input v-model="temp.delegateUnit" placeholder="请输入委托单位" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="updateData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateDetection } from '@/api/data'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogFormVisible: false,
      rules: {
        detectDirection: [{ required: true, message: '请选择检测方向', trigger: 'blur' }],
        roadName: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
        pavementWidth: [{ required: true, message: '请输入路面宽度', trigger: 'blur' }],
        roadLevel: [{ required: true, message: '请选择道路级别', trigger: 'blur' }],
        roadType: [{ required: true, message: '请选择路面类型', trigger: 'blur' }],
        startPileNum: [
          { required: true, message: '请输入起点桩号', trigger: 'blur' },
          { pattern: /^(?:[1-9]\d*|0)(?:\.\d{1,3})?$/, message: '请输入大于等于0的数字, 小数最多保留三位' },
        ],
        // adCode: [{ pattern: /^[1-8][0-7]\d{4}$/, message: '行政区划编码格式不正确' }],
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        // 格式化数据
        if (this.temp.startTime) {
          this.temp.startTime = this.$time.parseTime(new Date(this.temp.startTime), '{yyyy}-{mm}-{dd} {hh}:{ii}')
        }
        if (this.temp.endTime) {
          this.temp.endTime = this.$time.parseTime(new Date(this.temp.endTime), '{yyyy}-{mm}-{dd} {hh}:{ii}')
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const {
            id, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          } = this.temp

          const tempData = {
            id, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          }

          if (detectDirection !== this.formData.detectDirection || startPileNum !== this.formData.startPileNum || roadType !== this.formData.roadType || roadLevel !== this.formData.roadLevel || pavementWidth !== this.formData.pavementWidth) {
            this.$confirm('修改路面宽度、道路等级、路面类型、检测方向和起点桩号后将自动刷新数据，请确认是否修改?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }).then(() => {
              updateDetection(tempData).then(async () => {
                console.log('111111111111111')
                this.dialogFormVisible = false
                this.$emit('refreshData', tempData)
              })
            }).catch(() => {

            })
          } else {
            updateDetection(tempData).then(() => {
              console.log('2222222222222')

              this.dialogFormVisible = false
              this.$emit('refreshTable')
            })
          }
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
  },
}
</script>
