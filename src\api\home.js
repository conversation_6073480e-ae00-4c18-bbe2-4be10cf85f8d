/*
 * @Author: wangyj
 * @Date: 2022-07-14 16:51:14
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-07-15 16:59:28
 */
import request from '@/utils/request'
import axios from 'axios'

export function getDeviceLatestLocation() {
  return request({
    url: `/devices/queryDevicesLatestLocation`,
    method: 'get',
  })
}

export function getDevicelatestRoute() {
  return request({
    url: `/devices/queryDevicesLatestRoute`,
    method: 'get',
  })
}

/**
 * 首页非管理员查询某个路段内的病害图列表
 * @param {
 *  id 线段或者折线的id number 必传
 *  gpsLongitude 经度 number 必传
 *  gpsLatitude 纬度 number 必传
 * } params
 */
export function getQueryDamagePictures(params) {
  const { id } = params
  delete params.id
  return request({
    url: `/road-statises/${id}/queryDamagePictures`,
    method: 'get',
    params,
  })
}

/**
 * 首页非管理员查询某个路段内的单张病害图
 * @param {
 *  id 线段或者折线的id number 必传
 *  picTime 拍照时间 String 必传
 * } params
 */
export function getQueryOneDamagePicture(params) {
  const { id, picTime } = params
  return request({
    url: `/road-statises/${id}/queryOneDamagePicture`,
    method: 'get',
    params: { picTime },
  })
}

/**
 * 查询当前用户一周内巡检数据汇总情况
 * @param {*} params
 * @returns
 */
export function getSummaryRecentInspectData() {
  return request({
    url: `/inspect-tasks/summaryRecentInspectData`,
    method: 'get',
  })
}
/**
 * 查询当前用户今日巡检数据汇总情况
 * @param {*} params
 * @returns
 */
export function getSummaryRecentOneDayInspectData() {
  return request({
    url: `/inspect-tasks/summaryRecentOneDayInspectData`,
    method: 'get',
  })
}

/**
 * 查询路面巡查列表（分页）
 * @param {*} params {
 *  字段: 说明 ---是否必填
 *  types: 用户选择的病害类型（多个） ---否
    modelIdentifyType : 模型识别类型 1：病害 2： 道路资产 3：安全事件 5：沿线设施 ---否
    area: 过滤最小的病害面积  ---否
    page: 当前页码，默认从0开始  ---是
    size: 每次请求的数据数  ---是
 *
 * }
 */
let sources = []
export function getQueryRecentOneDayDamages(params) {
  const { CancelToken } = axios
  const source = CancelToken.source()
  sources.push(source)
  return request({
    url: `/damages/queryRecentOneDayDamages`,
    method: 'get',
    cancelToken: source.token,
    params,
  })
}

export function cancelRequest() {
  sources.forEach((source) => {
    source.cancel('取消请求')
  })
  sources = []
}
