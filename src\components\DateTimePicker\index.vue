<!--
 * 自定义日期时间选择器组件
 * 支持自动将结束时间设置为23:59:59
-->
<template>
  <el-date-picker
    v-model="innerValue"
    :type="type"
    :placeholder="placeholder"
    :format="format"
    :value-format="valueFormat"
    :picker-options="mergedPickerOptions"
    :editable="editable"
    :clearable="clearable"
    :disabled="disabled"
    @change="handleChange"
  />
</template>

<script>

export default {
  name: 'DateTimePicker',
  props: {
    // v-model绑定值
    value: {
      type: [String, Date, null],
      default: null
    },
    // 选择器类型
    type: {
      type: String,
      default: 'datetime'
    },
    // 输入框提示文字
    placeholder: {
      type: String,
      default: '请选择日期时间'
    },
    // 显示格式
    format: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    // 值格式
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    // 是否是结束时间选择器
    isEndTime: {
      type: Boolean,
      default: false
    },
    // 禁用日期函数或选择器配置
    pickerOptions: {
      type: Object,
      default: () => ({})
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      innerValue: this.value
    }
  },
  computed: {
    // 合并pickerOptions，添加默认时间设置
    mergedPickerOptions() {
      // 创建基础配置的副本
      const options = { ...this.pickerOptions }
      
      // 如果是结束时间选择器，添加defaultTime设置
      if (this.isEndTime) {
        options.defaultTime = '23:59:59'
      }
      
      return options
    }
  },
  watch: {
    value(val) {
      this.innerValue = val
    }
  },
  methods: {
    handleChange(date) {
      // 发出原始值的改变事件
      this.$emit('input', date)
      this.$emit('change', date)
    }
  }
}
</script> 