/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:53
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-09-18 11:07:03
 */

import Vue from 'vue'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'

// 引入BaiduMap
import BaiduMap from 'vue-baidu-map-v3'

// 引入Echarts
import * as echarts from 'echarts'

import animated from 'animate.css'

// element-ui默认主题
// import 'element-ui/lib/theme-chalk/index.css'
// 自定义element-ui 主题色
import './styles/element-variables.scss'
import '@/styles/loading.scss' // global css
import '@/styles/index.scss' // global css
import '@/icons' // icon
import '@/permission' // permission control
import Layout from '@/layout/index.vue'

// 引入外部字体
import '@/assets/font/font.css'
import '@/styles/iconfont.css'

import UnitSelect from '@/components/UnitSelect/index.vue'

import robuUser from '@robu/user'
import robuFormatdate from '@robu/formatdate'
import robuMenuShow from '@robu/menu-show'
import robuTagsView from '@robu/tagsview'
// import Viewer from 'v-viewer'
import directives from '@/directive'
import VueAMap from '@vuemap/vue-amap'
import * as filters from './filters' // global filters
import router, * as routerJs from './router'
import store from './store'
import App from './App.vue'

import '@vuemap/vue-amap/dist/style.css'

// import 'viewerjs/dist/viewer.css'

import { GLOBAL_VARIATE } from './utils'

import AuthPlugin from '@/directive/auth'

// 解决dialog遮罩层遮挡弹框内容问题（dialog的append-to-body属性不可以设置为true，否则将会不缩放且存在遮罩层遮盖弹框内容问题）
ElementUI.Dialog.props.modalAppendToBody.default = false
ElementUI.Drawer.props.modalAppendToBody.default = false

Vue.prototype.$echarts = echarts

Vue.use(BaiduMap, {
  // ak: '96c1I9X8Ej8KcDaiYo5tQa30zE0s7B9y',
  ak: 'Pg1zQn4bFY7u2PXVrgtojiESW4AHtnrg', // 晓轩
})
Vue.use(animated)

// 高德地图的key：
// key：8d90786bcdaf8bd55c49c8d9491bcd29
// 密钥：96f6b2e31bc2525516910d9513c068d0
VueAMap.initAMapApiLoader({
  key: '8d90786bcdaf8bd55c49c8d9491bcd29',
})
Vue.use(VueAMap)

Vue.use(ElementUI, { size: 'medium' })
// 公共库
Vue.use(robuFormatdate)
Vue.use(robuUser, { store, routerJs })
Vue.use(robuMenuShow, { store, routerJs, Layout })
Vue.use(robuTagsView, { store })
// register global utility filters
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

Vue.component('UnitSelect', UnitSelect)

// Vue.use(Viewer)
// Viewer.setDefaults({
//   inline: false, // 启用 inline 模式
//   button: true, // 显示右上角关闭按钮
//   navbar: false, // 显示缩略图导航
//   title: true, // 显示当前图片的标题
//   toolbar: false, // 显示工具栏
//   tooltip: true, // 显示缩放百分比
//   movable: true, // 图片是否可移动
//   zoomable: true, // 图片是否可缩放
//   rotatable: false, // 图片是否可旋转
//   scalable: false, // 图片是否可翻转
//   transition: true, // 使用 CSS3 过度
//   fullscreen: true, // 播放时是否全屏
//   keyboard: true, // 是否支持键盘
// })
Vue.use(directives)
Vue.config.productionTip = false
Vue.use(AuthPlugin)
const app = new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App),
})
Vue.use({ app })

Vue.prototype.GLOBAL_VARIATE = GLOBAL_VARIATE

Vue.prototype.$bus = new Vue()
