{
  "editor.renameOnType": true,

  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": false,
  "[javascript]": {
    "editor.formatOnSave": false
  },
  "[vue]": {
    "editor.formatOnSave": false,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "eslint.enable": true,
  "eslint.alwaysShowStatus": true,
  "eslint.options": {
    "extensions": ["html", ".js", ".ts", "jsx", "tsx", "vue"]
  },

  "eslint.validate": ["javascript", "typescript", "vue"],

  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.tslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },

  "json.schemaDownload.enable": false,

  "vetur.validation.template": false,
  "vetur.format.options.tabSize": 2,
  "vetur.format.options.useTabs": false,

  "vetur.format.defaultFormatterOptions": {
    "prettyhtml": {
      "printWidth": 120,   // No line exceeds this-length characters
      "singleQuote": false // Prefer double quotes over single quotes
    }
  },

  "vetur.format.defaultFormatter.html": "prettyhtml",
  "vetur.format.defaultFormatter.pug": "prettier",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.postcss": "prettier",
  "vetur.format.defaultFormatter.scss": "prettier",
  "vetur.format.defaultFormatter.less": "prettier",
  "vetur.format.defaultFormatter.stylus": "stylus-supremacy",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.format.defaultFormatter.ts": "prettier",
  "vetur.format.defaultFormatter.sass": "sass-formatter",
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  }
}
