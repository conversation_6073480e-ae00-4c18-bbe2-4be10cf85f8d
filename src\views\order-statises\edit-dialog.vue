<template>
  <el-dialog 
    :title="`编辑${form.label}里程(km)`" 
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" :label-width="labelWidth">
      <el-form-item 
        :label="`${form.label}里程(km)`" 
        prop="inspectMileage"
        :rules="rules.inspectMileage"
      >
        <el-input-number 
          v-model="form.inspectMileage" 
          :step="0.1" 
          :min="0" 
          style="width: 100%;"
          @change="formatValue"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { editOrderStatise } from '@/api/order-statises'
import { getBelongToLabel } from '@/utils/cd_constants'
import EventBus from '@/utils/eventBus'

export default {
  name: 'EditDialog',
  props: {
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: null,
        statisTarget: '',
        label: '',
        inspectMileage: 0
      },
      loading: false, 
      rules: {
        inspectMileage: [
          { required: true, message: '请输入里程数据', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    labelWidth() {
      return this.form.statisTarget === 'dongshan_ppp' ? '170px' : '130px'
    }
  },
  methods: {
    open(row) {
      this.dialogVisible = true
      
      // 处理传入的数据
      if (row) {
        this.form = {
          id: row.id,
          statisTarget: row.statisTarget,
          label: getBelongToLabel(row.statisTarget),
          inspectMileage: this.formatNumberValue(row.inspectMileage || 0)
        }
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    // 格式化值
    formatValue(value) {
      this.form.inspectMileage = this.formatNumberValue(value)
    },
    // 格式化数字：整数不显示小数点，小数最多保留2位，不自动补0
    formatNumberValue(value) {
      if (value !== undefined && value !== null) {
        const num = Number(value)
        
        // 如果是整数，直接返回整数
        if (Number.isInteger(num)) {
          return parseInt(num)
        } else {
          // 对于小数，限制最多2位小数但不补0
          // 先将数字转为字符串，截取到小数点后2位
          const strNum = num.toString()
          const parts = strNum.split('.')
          
          if (parts.length === 1) {
            return parseInt(num) // 没有小数部分
          } else {
            const intPart = parts[0]
            let decPart = parts[1]
            
            // 如果小数部分超过2位，截取前2位
            if (decPart.length > 2) {
              decPart = decPart.substring(0, 2)
            }
            
            // 组合整数部分和小数部分
            return parseFloat(intPart + '.' + decPart)
          }
        }
      }
      return value
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          
          // 准备提交的数据
          const data = {
            id: this.form.id,
            inspectMileage: this.form.inspectMileage
          }
          
          editOrderStatise(data)
            .then(() => {
              this.dialogVisible = false
              this.$message.success('更新成功')
              this.$emit('refresh')
              // 触发全局事件，通知其他组件数据已更新
              EventBus.$emit('orderStatiseUpdated')
            })
            .catch(error => {
              console.error('更新失败:', error)
              this.$message.error('更新失败')
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script> 

<style scoped lang="scss">
::v-deep .el-dialog__body {
  max-height: 75vh !important;
  padding: 20px 20px 10px;
  overflow-y: auto;
}
</style>
