<template>
  <div ref="playerRef" class="player">
    <i v-if="playIconShow && !errorStatus" class="video-play" @click="play" />
    <div v-show="errorStatus" class="jessibuca-error">
			<div class="jessibuca-error-icon">
				<img :src="errorIcon" alt="播放错误" />
			</div>
			<div class="jessibuca-error-text">
        {{ errorStatusText }}
        <el-link class="jessibuca-error-text-link" type="primary" @click="retry">重试</el-link></div>
		</div>
  </div>
</template>

<script >
import errorIcon from '@/assets/video-play-error.png';
export default {
  name: 'Jessibuca',
  jessibuca: null,
  props: {
    playUrl: {
      type: String,
      default: '',
    },
    autoPlay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      playIconShow: true,
      errorStatus: 0, // 错误状态码：1 超时 2 加载出错
      errorIcon: errorIcon
    }
  },
  computed: {
    errorStatusText() {
      if (this.errorStatus === 1) return '加载超时，请检查设备情况';
      if (this.errorStatus === 2) return '加载出错，请检查设备状态';
      return '';
    },
  },
  watch: {
    playUrl: {
      handler(newVal) {
        console.log('监听playUrl', newVal)
        this.jessibuca && this.destroy()
        this.playUrl = newVal
        this.play()
      },
      // immediate: true,
      // // deep: true,
    },
  },
  created() {

  },
  mounted() {
    if (this.autoPlay) {
      this.playIconShow = false
      this.createJessibuca()
      this.play(this.playUrl)
    }
  },
  destroyed() {
    this.destroy()
    console.log('destroyed')
  },
  methods: {
    createJessibuca() {
      const jessibuca = new Jessibuca({
        container: this.$refs.playerRef,
        videoBuffer: 1,
        decoder: "/static/jessibuca/decoder.js",
        isResize: true,
        isFullResize: false,
        isFlv: true,
        loadingText: "视频加载中，请稍候",
        heartTimeout: 30, // 播放中途,如果超过设定时长无数据返回,则回调timeout事件
        loadingTimeout: 20, //在连接成功之前,如果超过设定时长无数据返回,则回调timeout事件
        loadingTimeoutReplay: false, // 连接成功之前,如果超过设定时长无数据返回,是否重试
        heartTimeoutReplay: false, // 播放中途,如果超过设定时长无数据返回,是否重试
        hasAudio: true,
        debug: false,
        showBandwidth: true,
        operateBtns: {
          fullscreen: true,
          screenshot: true,
          play: true,
          audio: false,
          record: false,
        },
        useWebFullScreen: true,
        autoUseSystemFullScreen: true,
        controlAutoHide: true,
      })
      this.jessibuca = jessibuca

      jessibuca.on('timeout',  (error) => {
        console.log('jessibuca ==== timeout', error);
        this.errorStatus = 1;
        this.destroy(true)
      });

	    jessibuca.on('error',  (error) => {
		    console.log('jessibuca ==== error', error);
        this.errorStatus = 2;
        this.destroy(true)
      });

    },
    play() {
     	// 如果有错误状态，就当作重试处理
      if (this.errorStatus > 0) {
        this.retry();
        return;
      }
      this.createJessibuca()
      this.jessibuca.play(this.playUrl)
      this.playIconShow = false
    },
    retry() {
	    // 先销毁实例，但保持错误状态为0（清除错误显示）
	    this.destroy();
	    // 然后重新初始化并播放
      setTimeout(() => {
        this.createJessibuca();
        this.play();
      }, 100);
    },
    /**
     * 销毁实例，并保持错误状态
     * @param keepErrorState 是否保持错误状态
     */
    destroy(keepErrorState = false) {
      if (this.jessibuca) {
        this.jessibuca.destroy()
        this.jessibuca = null
      }
      this.playIconShow = true
      if (!keepErrorState) {
        this.errorStatus = 0
      }
    },
  },
}
</script>

<style lang='scss' scoped>

$--color-primary: #2d8cf0;
/* @import url(); 引入css类 */

.jessibuca-container {
	background: rgba(13, 14, 27, .7);
}
.player {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(13, 14, 27, .9);
  display: flex;
  align-items: center;
  justify-content: center;
  .video-play {
    width: 48px;
    height: 48px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACEElEQVRoQ+2ZXStEQRjH/3/yIXwDdz7J+i7kvdisXCk3SiFJW27kglBcSFFKbqwQSa4krykuKB09Naf2Yndn5jgzc06d53Znd36/mWfeniVyHsw5PwqB0DOonYEoijYBlOpAFwCMkHwLDS/9mwhEDUCfAAyTXA4tYSLwC6CtCegegH6S56FETAR+AHRoACcBTJAUWa+RloBAXwAYIrnt0yBNgZi7qtbHgw8RFwLC/QFglOScawlXAjH3gUqrE1cirgVi7mkAYyS/0xbxJSDcdwAGSa6nKeFTIOZeUyL3aYiEEBDuLwDjJGf+KxFKIOY+BdBL8iipSGiBmHtWbbuftiJZERBuOfgGSK7aSGRJIObeUml1ayKSRQHhlgtkiaTcdltGVgUE+ppkV54FaiS78yrwqlLoOI8Cch2XV548W7WRpTVwA6DP9kGUFYEpAOUkT9LQAvtq1M+0udKkQSgBqSlJWWYxKXj8vRACK+o6bbRIdYI+Ba7U7rKjg7L53JdAhWTZBsy0rWuBXZUuNVMg23auBF7UIl2yBbJt70JAoKV6/WwLk6R9mgKSJlJ1kLTxFmkJyCla8UZd15GJQKvyumyJ8gy8DAEvfZoINPqD41EtUjmUgoaJwAaAnjrKebVI34OSq85NBNqlCAWgE0CV5GEWwI3vQlmCbcSinYFCwPEIFDPgeIC1P1/MgHaIHDf4Aydx2TF7wnKeAAAAAElFTkSuQmCC);
    cursor: pointer;
  }
  .jessibuca-error {
		position: absolute;
		z-index: 200;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 14px;
		text-align: center;
    z-index: 100;
		&-icon {
			margin-bottom: 16px;
			
			img {
				width: 36px;
			}
		}
    &-text {
			position: relative;
			&-link {
				position: absolute;
				right: -35px;
				top: 0;
				pointer-events: auto;
			}
		}
	}
}


</style>
