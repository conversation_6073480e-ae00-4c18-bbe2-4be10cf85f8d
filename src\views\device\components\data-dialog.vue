/*
 * @Author: wangyj
 * @Date: 2023-01-04 17:48:36
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-02-14 17:12:52
 */

<template>
  <el-dialog width="60%" title="设备数据传输信息" :visible.sync="dialogVisible" :close-on-click-modal="false">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form ref="form" label-width="240px">
          <el-form-item label="重传路面图片数据已到达日期:">
            {{ temp.schedulerFrameTimestamp ? temp.schedulerFrameTimestamp.replace('T', ' ') : '' }}
          </el-form-item>
          <el-form-item label="重传路面图片已到达日期:">
            {{ temp.schedulerFrameFileTimestamp ? temp.schedulerFrameFileTimestamp.replace('T', ' ') : '' }}
          </el-form-item>
          <el-form-item label="尚未上传成功的路面图片元数据:">
            {{ temp.frameFailureNums }}
            <span class="iconfont icon-tubiao-zhexiantu" title="查看折线图" @click="handleInitCharts(1)" />
          </el-form-item>
          <el-form-item label="尚未上传成功的路面图片数据:">
            {{ temp.frameFileFailureNums }}
            <span class="iconfont icon-tubiao-zhexiantu" title="查看折线图" @click="handleInitCharts(2)" />
          </el-form-item>
          <el-form-item label="重传景观图片数据已到达日期:">
            {{ temp.schedulerLandscapeframeTimestamp ? temp.schedulerLandscapeframeTimestamp.replace('T', ' ') : '' }}
          </el-form-item>
          <el-form-item label="重传景观图片已到达日期:">
            {{ temp.schedulerLandscapeframeFileTimestamp ? temp.schedulerLandscapeframeFileTimestamp.replace('T', ' ') : '' }}
          </el-form-item>
          <el-form-item label="尚未上传成功的景观图片元数据:">
            {{ temp.frameLandscapeFailureNums }}
            <span class="iconfont icon-tubiao-zhexiantu" title="查看折线图" @click="handleInitCharts(3)" />
          </el-form-item>
          <el-form-item label="尚未上传成功的景观图片数据:">
            {{ temp.frameLandscapeFileFailureNums }}
            <span class="iconfont icon-tubiao-zhexiantu" title="查看折线图" @click="handleInitCharts(4)" />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col v-loading="loading" element-loading-text="数据加载中，请稍等" :span="12" style="min-height: 300px">
        <Echarts v-if="echartsId" :id="echartsId" style="width: 100%; height: 500px" :data="echartsOption" />
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
import { getDeviceDataInfo, getChangedFrameNums } from '@/api/device'
import Echarts from '@/views/data/components/echarts.vue'

export default {
  components: { Echarts },
  data() {
    return {
      deviceKey: null,
      temp: {},
      dialogVisible: false,
      echartsId: null,
      echartsOption: [],
      loading: false,
    }
  },
  methods: {
    async show(deviceKey) {
      this.deviceKey = deviceKey

      const { payload } = await getDeviceDataInfo({ deviceKey })
      this.temp = payload
      this.echartsId = null
      this.dialogVisible = true
    },
    async handleInitCharts(type) {
      this.loading = true
      this.echartsId = null
      const params = {
        deviceId: this.deviceKey,
        frameDataType: type,
      }

      const { payload } = await getChangedFrameNums(params)

      const xAxis_data = []
      const series_data = []
      let tempV = ''
      let tempN = ''
      switch (type) {
      case 1:
        tempV = 'frameFailureNums'
        tempN = '尚未上传成功的路面图片元数据'
        break
      case 2:
        tempV = 'frameFileFailureNums'
        tempN = '尚未上传成功的路面图片数据'
        break
      case 3:
        tempV = 'frameLandscapeFailureNums'
        tempN = '尚未上传成功的景观图片元数据'
        break
      case 4:
        tempV = 'frameLandscapeFileFailureNums'
        tempN = '尚未上传成功的景观图片数据'
        break
      default:
        tempV = ''
      }
      if (payload) {
        payload.forEach((item) => {
          xAxis_data.push(item.photoTime.split('T')[0])
          series_data.push(item[tempV])
        })
      }

      const option = {
        title: {
          text: tempN,
          left: 'center',
        },
        grid: {
          // bottom: 240,
          bottom: 200,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: xAxis_data,
          axisLabel: {
            interval: 0,
            rotate: 80,
          },
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: series_data,
            type: 'line',
          },
        ],

      }
      this.echartsOption = option
      this.echartsId = `${tempV}_chart`
      this.loading = false
    },
  },
}
</script>
<style lang="scss" scoped>
  .iconfont {
    color: #2d8cf0;
    cursor: pointer;
    margin-left: 10px;
  }
</style>
