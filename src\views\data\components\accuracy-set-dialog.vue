<template>
  <el-dialog
    width="40%"
    :title="title"
    :class="customClass"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :modal="false"
    :modal-append-to-body="false"
    :destroy-on-close="true"
  >
    <div v-if="dialogVisible">
      <el-row :gutter="30">
        <el-col :span="5">公共置信度</el-col>
        <el-col :span="19">
          <el-slider
            v-model.number="commonAccuracy"
            style="pointer-events: auto;"
            :min="0.01"
            :max="1"
            :step="0.01"
            show-input
            @input.native="inputChane"
            @change="handelChangeAfterThreshold"
          />
        </el-col>
      </el-row>
      <el-row v-for="item in accuracyList" :key="item.label + customClass" :gutter="30">
        <el-col :span="5">{{ item.chineseName }}</el-col>
        <el-col :span="19">
          <el-slider
            v-model.number="item.accuracy"
            style="pointer-events: auto;"
            :min="0.01"
            :max="1"
            :step="0.01"
            show-input
            @input.native="inputChane"
            @change="handelChangeAfterThreshold"
          />
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handelSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import _ from 'lodash'

export default {
  mixins: [],
  props: {
    modelType: {
      type: Number,
      default: 1,
    },
    customClass: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      type: 1,
      dialogVisible: false,
      modelList: [],
      version: null,
      accuracyList: [],
      commonAccuracy: 0.3,
      apiUrl: '',
      title: '',
    }
  },
  watch: {
    modelType(newVal) {
      this.type = newVal
    },
  },
  destroyed() {
    // console.log('销毁');
  },
  methods: {
    async show(data) {
      this.dialogVisible = true
      const temp = _.cloneDeep(data)
      this.title = `${temp.versionName} - 置信度设置`
      this.apiUrl = temp.apiUrl
      this.accuracyList = temp.modelDetail.labelList
      this.commonAccuracy = temp.modelDetail.commonAccuracy
    },
    async handelSubmit() {
      const params = {
        type: this.customClass,
        apiUrl: this.apiUrl,
        modelDetail: {
          commonAccuracy: this.commonAccuracy,
          labelList: this.accuracyList,
        },
      }
      this.$emit('nextDiseaseRefresh', _.cloneDeep(params))
      this.handleClose()
    },
    handleClose() {
      this.$emit('handleDigClose', this.customClass)
      this.dialogVisible = false
    },
    handelChangeAfterThreshold() {

    },
    onResetAccuracyList() {
      const that = this
      that.pitch.forEach((item, index) => {
        item.value = that.defaultPitchValue[index]
      })
      that.cement.forEach((item) => {
        item.value = 0.1
      })
    },
    inputChane(event, index) {
      const { min, max, value } = event.target
      if (isNaN(value) || !value) {
        event.target.value = 0
        return
      }
      console.log(value >= parseInt(max))
      const regex = /^(0(\.\d{0,3})?|1(\.0{0,3})?)$/
      if (regex.test(value)) {
        if (value >= parseInt(max)) {
          event.target.value = max * 1
          return
        }
        event.target.value = value
      } else if (value >= parseInt(max)) {
        event.target.value = max * 1
      } else {
        // 如果超过两位小数，只保留两位小数
        event.target.value = parseFloat(value).toFixed(3) * 1
      }
    },
  },
}
</script>

<style lang='scss' scoped>
/* @import url(); 引入css类 */
.el-col {
  line-height: 2.5;
}

::v-deep .el-dialog {
  .el-dialog__body {
    padding: 10px 20px;

  }
}
::v-deep .el-dialog__headerbtn {
  pointer-events: auto !important;
}

</style>
