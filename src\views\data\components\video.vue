/*
 * @Author: wangyj
 * @Date: 2022-08-04 15:36:22
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-03-09 13:25:57
 */

<template>
  <div class="video-container">
    <div v-loading="loading" element-loading-text="回放生成中，请稍等..." class="video-box">
      <canvas id="playCanvas" :width="canvasWidth" :height="canvasHeight" style="display: none" />
      <template v-if="!loading">
        <template v-if="imageUrlMap.size > 0">
          <div
            v-show="imageUrl === imageStateNotExist || imageUrl === ''"
            class="empty"
          >您查看的图片时间点是：{{ getPhotoTime() }}，当前图片续传到达时间：{{ getSchedulerFrameFileTimestamp() }}，还有{{ getFrameFileFailureNums() }}张图片尚未续传成功，请等待或联系管理员进一步查看。</div>
          <div v-show="imageUrl === imageStateArchived" class="empty">图片已归档，请联系管理员解冻</div>
          <div v-show="imageUrl === imageStateLoading" class="empty">图片正在加载中，请稍等...</div>
          <img
            v-show="imageUrl !== imageStateNotExist && imageUrl !== imageStateArchived && imageUrl !== '' && imageUrl !== imageStateLoading"
            lazy
            mode="aspectFit"
            :src="imageUrl"
          />
        </template>
        <template v-else>
          <div class="empty">暂无回放</div>
        </template>
      </template>
    </div>
    <div class="video-progress">
      <!-- <CustomProgress
        class="c-progress"
        :value="percentage"
        @stop="onPercentChange"
      />-->
      <el-progress :percentage="percentage" :show-text="false" color="#545E72" />
      <span :class="notAllowedClass">
        <i :class="pointerEventsNoneClass" class="el-icon-refresh-left" title="返回起点" @click="handleReturnToStart" />
      </span>
    </div>
    <div class="video_btn" :class="notAllowedClass">
      <div :class="pointerEventsNoneClass" style="height: 100%">
        <p
          v-show="!playFlag"
          class="active"
          @click="handlePlay"
          @mouseover="handleMouseover('playBool')"
          @mouseout="handleMouseout('playBool')"
        >
          <i v-if="playBool == true">
            <img src="@/assets/video/play_icon.png" alt />
          </i>
          <i v-else>
            <img src="@/assets/video/play_default_icon.png" alt />
          </i>
          播放
        </p>
        <p v-show="playFlag" @click="handlePause" @mouseover="handleMouseover('stopBool')" @mouseout="handleMouseout('stopBool')">
          <i v-if="stopBool == true">
            <img src="@/assets/video/stop_icon.png" alt />
          </i>
          <i v-else>
            <img src="@/assets/video/stop_default_icon.png" alt />
          </i>
          暂停
        </p>
        <p @click="handleLast" @mouseover="handleMouseover('lastFrameBool')" @mouseout="handleMouseout('lastFrameBool')">
          <i v-if="lastFrameBool == true">
            <img src="@/assets/video/last_frame_icon.png" alt />
          </i>
          <i v-else>
            <img src="@/assets/video/last_frame_default_icon.png" alt />
          </i>
          上一帧
        </p>
        <p @click="handleNext" @mouseover="handleMouseover('nextFrameBool')" @mouseout="handleMouseout('nextFrameBool')">
          <i v-if="nextFrameBool == true">
            <img src="@/assets/video/next_frame_icon.png" alt />
          </i>
          <i v-else>
            <img src="@/assets/video/next_frame_default_icon.png" alt />
          </i>
          下一帧
        </p>
        <p>
          <el-select v-model="slowValue" placeholder="慢放倍率" size="small" @change="handleChangeSlow">
            <el-option
              v-for="(sel, index) in slowPlayList"
              :key="index + sel.value"
              :label="sel.label"
              :value="sel.value"
            />
          </el-select>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import CanvasMixin from '@/mixin/canvas'
import CustomProgress from './custom-progress.vue'
import { getRoadPictures } from '@/api/data'
export default {
  components: { CustomProgress },
  mixins: [ CanvasMixin ],
  props: {
    pageQuery: {
      type: Object,
      default: () => ({
        page: 0,
        size: 50,
      }),
    },
    speed: {
      type: String,
      default: '慢速播放',
    }, // 当前播放倍数
    taskData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: true,
      pausedDueToLoading: false, // 是否因为数据加载而暂停播放
      isLoadingNextPage: false,  // 新增：是否正在加载下一页
      imgError: false,
      playBool: false,
      stopBool: false,
      lastFrameBool: false,
      nextFrameBool: false,
      slowValue: '慢速播放',
      slowPlayList: [
        {
          value: '1',
          label: '1.0X',
        },
        {
          value: '2',
          label: '0.5X',
        },
        {
          value: '5',
          label: '0.2X',
        },
        {
          value: '10',
          label: '0.1X',
        },
      ],
      imageIndex: 0, // 当前播放图片索引
      imageUrl: null, // 当前播放图片
      imageUrlMap: new Map(), // 所有病害图集合
      loadingImages: new Set(), // 新增：记录正在加载的图片索引
      timer: null, // 定时器
      playFlag: false, // 是否正在播放
      percentage: 0, // 播放进度
      totalPages: 0,
      totalElements: 0,
      canvasWidth: 640,
      canvasHeight: 360,
      canvas: null,
      ctx: null,
      lineIndexArr: [], // 有病害图片的数据索引
      keyIndexArr: [], // 每段数据对应的图片索引值，存在即要进入到下一段
      sourceData: [],
      collectionClick: false, // 是否点击地图上的点
      imageQuality: 0.7, // 图片质量压缩比例
      MAX_CACHE_SIZE: 100, // 最大缓存大小
      BUFFER_SIZE: 50, // 当前位置前后各保留的图片数
      imageStateNotExist: '不存在',
      imageStateLoading: '加载中',
      imageStateArchived: '归档',
    }
  },
  computed: {
    videoTimeSpeed() {
      // 播放倍速
      if (this.slowValue === '慢速播放') {
        return 200
      }
      return parseInt(this.slowValue) * 200
    },
    notAllowedClass() {
      return {
        'not-allowed': this.loading || this.imageUrlMap.size === 0,
      }
    },
    pointerEventsNoneClass() {
      return {
        'pointer-events-none': this.loading || this.imageUrlMap.size === 0,
      }
    },
  },
  watch: {
    imageIndex(val) {
      // console.log('imageIndex changed to', val, '已加载:', this.imageUrlMap.has(val))
      // 计算预加载阈值
      const loadThreshold =
        Math.floor(this.pageQuery.page * this.pageQuery.size + this.pageQuery.size * 0.4)
      // 检查是否需要加载下一页数据，并且确保不在当前加载过程中
      if (val >= loadThreshold && this.pageQuery.page < this.totalPages - 1 && !this.isLoadingNextPage) {
        console.log('触发加载下一页数据', val, loadThreshold)
        this.isLoadingNextPage = true // 标记正在加载
        let { page } = this.pageQuery
        this.$emit('updatePage', (page += 1))
        this.getPictures()
      }

      // 图片未加载或正在加载中
      if (!this.imageUrlMap.has(val) || this.imageUrlMap.get(val) === '加载中') {
        console.log('图片未加载或正在加载中', val)
        this.pauseForLoading()
        
        // 如果有数据源但图片未加载，开始加载
        if (this.sourceData[val] && (!this.imageUrlMap.has(val) || this.imageUrlMap.get(val) === '加载中')) {
          this.loadSingleImage(val)
        }
        return
      }
      
      // 更新当前显示的图片URL
      this.imageUrl = this.imageUrlMap.get(val)
      
      // 启用缓存管理
      // this.manageCache(val)
      
      // 预加载后续几张图片
      this.preloadNextImages(val)
      
      if (this.sourceData.length > 0) {
        let gps = {}
        // 确保当前索引和下一个索引都存在且有效
        if (
          this.sourceData[val] &&
          this.sourceData[val].gpsLongitude &&
          this.sourceData[val].gpsLatitude
        ) {
          gps.lngStart = this.sourceData[val].gpsLongitude
          gps.latStart = this.sourceData[val].gpsLatitude

          // 检查下一个索引是否存在
          if (
            this.sourceData[val + 1] &&
            this.sourceData[val + 1].gpsLongitude !== undefined &&
            this.sourceData[val + 1].gpsLatitude !== undefined
          ) {
            gps.lngEnd = this.sourceData[val + 1].gpsLongitude
            gps.latEnd = this.sourceData[val + 1].gpsLatitude
          } else {
            // 如果下一个索引不存在，使用当前索引的值
            gps.lngEnd = gps.lngStart
            gps.latEnd = gps.latStart
          }

          this.$emit('updateCar', gps)
        }
      }
    },
    speed: {
      handler(newVal, oldVal) {
        this.slowValue = newVal
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * 智能缓存管理
     * @param currentPosition 当前播放位置
     */
    manageCache(currentPosition) {
      // 如果缓存未超过限制，无需处理
      if (this.imageUrlMap.size <= this.MAX_CACHE_SIZE) return

      // 定义保留范围
      const firstPageEnd = this.pageQuery.size - 1
      const lowerBound = Math.max(0, currentPosition - this.BUFFER_SIZE)

      // 直接找出需要删除的键，单次遍历
      const keysToDelete = []
      
      this.imageUrlMap.forEach((value, key) => {
        // 只删除第一页之后、当前缓冲区之前的图片
        if (key > firstPageEnd && key < lowerBound) {
          keysToDelete.push(key)
        }
      })
      
      // 只有有键需要删除时才输出日志
      if (keysToDelete.length > 0) {
        console.log('manageCache', {
          currentPosition,
          totalCached: this.imageUrlMap.size,
          toDelete: keysToDelete.length,
          firstPageEnd,
          lowerBound,
          deleteRange: `${firstPageEnd+1} - ${lowerBound-1}`
        })
        
        // 执行删除并强制垃圾回收
        const deletedUrls = []
        keysToDelete.forEach(key => {
          const url = this.imageUrlMap.get(key)
          deletedUrls.push(url)
          this.revokeObjectURL(url)
          this.imageUrlMap.delete(key)
        })
        
        // 尝试强制垃圾回收
        setTimeout(() => {
          deletedUrls.length = 0
          if (window.gc) {
            try { window.gc() } catch (e) {}
          }
        }, 0)
        
        console.log(`已清理${keysToDelete.length}张前方图片，剩余${this.imageUrlMap.size}张`)
      }
    },
    // onPercentChange(num) {
    //   this.imageIndex = Math.round((this.totalElements - 1) * (num / 100))

    //   console.log(this.imageIndex, this.lineIndexArr)
    //   this.percentage = num
    //   if (this.imageUrlMap.has(this.imageIndex)) {
    //     this.imageUrl = this.imageUrlMap.get(this.imageIndex)
    //   } else {
    //     this.cacheImg(this.imageIndex)
    //   }

    //   if (this.sourceData[this.imageIndex]) {
    //     const lngStart =
    //       this.sourceData[this.imageIndex].gpsLngOriginal ||
    //       this.sourceData[this.imageIndex].gpsLongitude ||
    //       0
    //     const latStart =
    //       this.sourceData[this.imageIndex].gpsLatOriginal ||
    //       this.sourceData[this.imageIndex].gpsLatitude ||
    //       0
    //     const lngEnd = this.sourceData[this.imageIndex].gpsLongitude || lngStart
    //     const latEnd = this.sourceData[this.imageIndex].gpsLatitude || latStart

    //     this.$emit('updateCar', {
    //       lngStart,
    //       latStart,
    //       lngEnd,
    //       latEnd,
    //     })
    //   }
    // },
    async initImgs() {
      this.reset()
      await this.getModelIdentifyTypesAll()
      this.imageIndex = 0
      this.sourceData = []
      await this.getPictures()
      this.doPlay()
    },
    async getPlaybackPicturesAjax() {
      const { deviceKey, startTime, endTime, id } = this.taskData
      const { page, size } = this.pageQuery

      const params = {
        deviceId: deviceKey,
        startTime,
        endTime,
        page,
        size,
        taskId: id,
      }
      const response = await getRoadPictures(params)
      return response
    },
    // 通过接口获取图片数据
    async getPictures() {
      try {
        const that = this
        const { payload } = await this.getPlaybackPicturesAjax()
        const { page, size } = this.pageQuery
        const offset = page * size
        this.totalPages = payload.totalPages
        this.totalElements = payload.totalElements
        this.sourceData = [...this.sourceData, ...payload.content]

        for (let i = 0; i < payload.content.length; i++) {
          const { pictureUrl, coordinates, picState } = payload.content[i]
          const currentIndex = i + offset
          
          if (picState === 1) {
            // 先标记为加载中状态
            if (!this.imageUrlMap.has(currentIndex)) {
              that.imageUrlMap.set(currentIndex, '加载中')
              if (i === 0 && that.pageQuery.page === 0) {
                that.imageUrl = '加载中'
              }
            }
            
            // 优先加载当前页第一张图片和当前播放位置附近的图片
            const isPriority = (i === 0 && page === 0) || 
                             (Math.abs(currentIndex - this.imageIndex) < 5)
            
            if (isPriority) {
              // 立即加载优先图片
              await this.loadImageWithPriority(pictureUrl, coordinates, currentIndex)
            } else {
              // 其他图片放入队列，避免一次性加载过多图片阻塞主线程
              this.queueImageLoad(pictureUrl, coordinates, currentIndex)
            }
          } else {
            const imgUrl = picState === 2 ? '归档' : '不存在'
            if (i === 0 && that.pageQuery.page === 0) {
              that.imageUrl = imgUrl
            }
            that.imageUrlMap.set(currentIndex, imgUrl)
          }
        }
        
        console.log('数据加载完成，图片将在后台继续加载')
        this.resumeAfterLoading()
      } catch (err) {
        console.log(err)
        this.loading = false
        this.isLoadingNextPage = false
        this.pausedDueToLoading = false // 出错时重置状态
      }
    },
    // 队列化加载图片，防止一次性加载过多图片
    queueImageLoad(pictureUrl, coordinates, index) {
      setTimeout(() => {
        // 只有当图片状态为'加载中'或未加载时才加载
        if (!this.imageUrlMap.has(index) || this.imageUrlMap.get(index) === '加载中') {
          this.loadImageWithPriority(pictureUrl, coordinates, index)
        }
      }, 100) // 稍微延迟加载，让优先图片先加载
    },

    // 加载单张图片
    async loadSingleImage(index) {
      // 防止重复加载
      if (this.loadingImages.has(index)) {
        return
      }
      
      const data = this.sourceData[index]
      if (!data) return
      
      this.loadingImages.add(index)
      console.log('开始加载单张图片', index)
      
      try {
        const imgUrl = await this.loadingImg(data)
        this.imageUrlMap.set(index, imgUrl)
        
        // 如果当前显示的是这张图片，更新显示
        if (this.imageIndex === index) {
          this.imageUrl = imgUrl
          // 如果是因为图片加载而暂停的播放，且图片已加载完成，恢复播放
          if (this.pausedDueToLoading && imgUrl !== '加载中' && imgUrl !== '不存在') {
            this.resumeAfterLoading()
          }
        }
      } catch (error) {
        console.error('加载单张图片失败:', error)
      } finally {
        this.loadingImages.delete(index)
      }
    },

    // 优先加载图片的方法
    async loadImageWithPriority(pictureUrl, coordinates, index) {
      // 防止重复加载
      if (this.loadingImages.has(index)) {
        return
      }
      
      this.loadingImages.add(index)
      
      try {
        const that = this
        const image = await that.loadImg(pictureUrl)
        let imgUrl = ''
        
        if (image !== '') {
          // 创建完全独立的临时canvas和ctx
          const tempCanvas = document.createElement('canvas')
          const imageWidth = image.width
          const imageHeight = image.height
          tempCanvas.width = imageWidth
          tempCanvas.height = imageHeight
          const tempCtx = tempCanvas.getContext('2d')
          
          // 直接在临时canvas上绘制图像
          tempCtx.drawImage(image, 0, 0, imageWidth, imageHeight)
          
          // 手动实现标记绘制逻辑，而不是使用that.ctxDraw
          if (coordinates && coordinates.length > 0) {
            // 批量处理样式设置，减少状态切换
            tempCtx.lineWidth = 3
            tempCtx.font = 'bold 22px Arial'
            
            // 按类型分组，相同类型的标记一起绘制，减少样式切换
            const typeGroups = this.groupByType(coordinates)
            
            for (const [type, items] of Object.entries(typeGroups)) {
              // 获取类型对应的颜色和文本
              const typeInfo = that.getTypeInfo(type)
              
              // 设置一次样式，用于所有相同类型的标记
              tempCtx.strokeStyle = typeInfo.color
              tempCtx.fillStyle = typeInfo.color
              tempCtx.setLineDash([])
              
              // 绘制所有相同类型的标记
              items.forEach(item => {
                const coordinateArr = item.coordinate.split(' ')
                const x = coordinateArr[0] * 1
                const y = coordinateArr[1] * 1
                const width = coordinateArr[2] - x
                const height = coordinateArr[3] - y
                
                // 绘制矩形
                tempCtx.strokeRect(x, y, width, height)
                
                // 文字位置逻辑
                const txtW = tempCtx.measureText(typeInfo.txt).width
                tempCtx.textAlign = (x + txtW > imageWidth) ? 'right' : 'left'
                
                // 根据位置决定文字绘制的坐标
                const textX = (tempCtx.textAlign === 'right') ? (x + width) : x
                const textY = (y < 25) ? (y + height + 25) : (y - 6)
                
                // 绘制文字
                tempCtx.fillText(typeInfo.txt, textX, textY)
              })
            }
          }
          
          // 使用toBlob替代toDataURL
          imgUrl = await new Promise(resolve => {
            tempCanvas.toBlob(
              (blob) => {
                if (blob) {
                  // 使用createObjectURL创建URL
                  const url = URL.createObjectURL(blob)
                  resolve(url)
                } else {
                  resolve('不存在')
                }
              },
              'image/jpeg',
              that.imageQuality
            )
          })
          
          // 清理临时Canvas资源
          tempCtx.clearRect(0, 0, imageWidth, imageHeight)
          tempCanvas.width = 1
          tempCanvas.height = 1
          
          // 主动释放资源
          image.onload = null
          image.onerror = null
          image.src = ''
        } else {
          imgUrl = '不存在'
        }
        
        // 更新图片URL
        that.imageUrlMap.set(index, imgUrl)
        
        // 如果是当前显示的图片，更新显示
        if (that.imageIndex === index) {
          that.imageUrl = imgUrl
          // 如果是因为图片加载而暂停的播放，且图片已加载完成，恢复播放
          if (this.pausedDueToLoading && imgUrl !== '加载中' && imgUrl !== '不存在') {
            this.resumeAfterLoading()
          }
        }
      } catch (error) {
        console.error('加载图片失败:', error)
        // 出错时标记为不存在
        this.imageUrlMap.set(index, '不存在')
      } finally {
        this.loadingImages.delete(index)
      }
    },

    // 预加载后续几张图片
    preloadNextImages(currentIndex) {
      const preloadCount = 5 // 预加载后面5张图片
      for (let i = 1; i <= preloadCount; i++) {
        const nextIndex = currentIndex + i
        if (nextIndex < this.totalElements && this.sourceData[nextIndex]) {
          // 如果图片未加载或状态为"加载中"，则开始加载
          if (!this.imageUrlMap.has(nextIndex) || this.imageUrlMap.get(nextIndex) === '加载中') {
            this.loadSingleImage(nextIndex)
          }
        }
      }
    },

    // 监听播放
    doPlay() {
      const that = this
      clearTimeout(this.timer)
      if (this.playFlag) {
        if (that.imageIndex < that.totalElements - 1) {
          // 还有图片没有播放完毕
          that.imageIndex += 1 // 播放下一张图片
          
          // 检查下一个索引是否有数据
          if (!that.sourceData[that.imageIndex]) {
            // 如果没有数据，暂停播放并等待数据加载
            this.pauseForLoading()
            console.log('doPlay: 等待数据加载', that.imageIndex)
          } else if (that.imageUrlMap.has(that.imageIndex) && that.imageUrlMap.get(that.imageIndex) !== '加载中') {
            // 如果图片已加载完成（不是"加载中"状态），显示图片
            that.imageUrl = that.imageUrlMap.get(that.imageIndex)
          } else {
            // 图片未加载或正在加载中，暂停播放并开始加载
            this.pauseForLoading()
            console.log('doPlay: 等待图片加载', that.imageIndex)
            this.loadSingleImage(that.imageIndex)
          }
          
          that.percentage =
            (that.imageIndex / that.totalElements).toFixed(2) * 100 // 当前播放的百分比
        } else {
          // 表示所有图片都已经播放完毕
          this.playFlag = false
          this.percentage = 100
        }
      }

      this.timer = setTimeout(() => {
        that.doPlay()
      }, this.videoTimeSpeed)
    },
    handleReturnToStart() {
      this.imageIndex = 0
      
      // 检查索引0的图片是否已加载或是否在加载中
      if (!this.imageUrlMap.has(0) || this.imageUrlMap.get(0) === this.imageStateLoading) {
        console.log('返回起点: 索引0图片未加载，开始加载')
        // 如果索引0未加载或正在加载中，且有数据源，则尝试加载
        if (this.sourceData[0]) {
          this.loadSingleImage(0)
        } else {
          // 如果没有数据源，可能需要从接口获取第一页数据
          this.pageQuery.page = 0
          this.getPictures()
        }
      } else {
        // 已加载，直接更新显示
        this.imageUrl = this.imageUrlMap.get(0)
      }
      
      this.percentage = 0
    },
    handlePlay() {
      console.log('播放开始')
      this.playFlag = true
    },
    handlePause() {
      if (this.playFlag) {
        this.playFlag = false
      }
    },
    handleLast() {
      if (this.playFlag) {
        this.$message({
          message: '请先暂停视频',
          type: 'error',
        })
        return
      }
      if (this.imageIndex === 0) {
        this.$message({
          message: '已经是第一张图片了',
          type: 'warning',
        })
        return
      }

      this.imageIndex -= 1
      this.imageUrl = this.imageUrlMap.get(this.imageIndex)
      this.percentage = (this.imageIndex / this.totalElements).toFixed(2) * 100
    },
    handleNext() {
      if (this.playFlag) {
        this.$message({
          message: '请先暂停视频',
          type: 'error',
        })
        return
      }
      if (this.imageIndex === this.totalElements - 1) {
        this.$message({
          message: '已经是最后一张图片了',
          type: 'warning',
        })
        this.percentage = 100
        return
      }
      this.imageIndex += 1
      console.log('下一帧', this.imageIndex)

      this.imageUrl = this.imageUrlMap.get(this.imageIndex)
      this.percentage =
        (this.imageIndex / (this.totalElements - 1)).toFixed(2) * 100
    },
    handleChangeSlow(val) {
      // 速度改变时的处理
    },
    handleMouseover(key) {
      if (this.loading) return
      this[key] = true
    },
    handleMouseout(key) {
      if (this.loading) return
      this[key] = false
    },
    reset() {
      // 取消所有正在进行的加载
      this.loadingImages.clear()
      
      // 释放所有Object URLs
      this.imageUrlMap.forEach((url) => {
        this.revokeObjectURL(url)
      })
      
      // 重置所有状态
      this.imageUrl = null
      this.imageUrlMap.clear()
      this.totalElements = 0
      this.lineIndexArr = []
      this.keyIndexArr = []
      
      // 清理定时器
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      
      // 重置播放状态
      this.playFlag = false
      this.percentage = 0
      this.slowValue = this.speed
      this.loading = true
      this.imgError = false
      
      // 重置分页和加载状态
      this.pageQuery.page = 0
      this.isLoadingNextPage = false
      this.pausedDueToLoading = false
      
      // 清理Canvas
      if (this.ctx) {
        this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
        this.ctx = null
      }
      this.canvas = null
      
      // 强制垃圾回收
      setTimeout(() => {
        if (window.gc) {
          try { window.gc() } catch (e) {}
        }
      }, 0)
    },
    async cacheImg(start) {
      if (this.totalElements === this.imageUrlMap.size) return // 全部加载完成
      
      this.pauseForLoading() // 使用新方法替代原有逻辑
      
      const chunk = this.pageQuery.size
      const end = Math.min(this.totalElements, start + chunk)
      
      // 修复：确保我们只获取尚未加载的图片数据
      const startIndex = Math.floor(start / chunk) * chunk // 计算所在页的起始索引
      const tempSourceData = this.sourceData.slice(startIndex, end)
      console.log('cacheImg', start, tempSourceData)
      
      // 如果没有数据，不要继续处理
      if (tempSourceData.length === 0) {
        console.log('没有更多数据可加载')
        this.loading = false
        this.pausedDueToLoading = false
        return
      }
      
      // 优先加载当前请求的索引图片
      if (this.sourceData[start] && (!this.imageUrlMap.has(start) || this.imageUrlMap.get(start) === '加载中')) {
        console.log('优先加载当前索引图片', start)
        await this.loadSingleImage(start)
      }
      
      // 预加载后续几张图片
      this.preloadNextImages(start)
      
      this.resumeAfterLoading()
    },

    async loadingImg(sourceData) {
      return new Promise(async (resolve) => {
        const that = this
        const { picture, picState, damages, type, coordinate } = sourceData
        
        if (picState === 1) {
          try {
            const image = await that.loadImg(picture)
            if (image) {
              // 创建临时Canvas而不是复用已有的Canvas
              const tempCanvas = document.createElement('canvas')
              tempCanvas.width = image.width
              tempCanvas.height = image.height
              const tempCtx = tempCanvas.getContext('2d')
              
              // 在临时Canvas上绘制
              tempCtx.drawImage(image, 0, 0, image.width, image.height)
              
              // 保存当前的canvas和ctx引用
              const originalCanvas = that.canvas
              const originalCtx = that.ctx
              
              // 临时替换canvas和ctx，以便使用mixin中的ctxDraw方法
              that.canvas = tempCanvas
              that.ctx = tempCtx
              
              // 使用mixin中的方法绘制标记
              if (damages) {
                damages.forEach((damage) => {
                  that.ctxDraw(damage.type, damage.coordinate)
                })
              } else if (type && coordinate) {
                that.ctxDraw(type, coordinate)
              }
              
              // 使用toBlob创建URL
              tempCanvas.toBlob(
                (blob) => {
                  if (blob) {
                    const imgUrl = URL.createObjectURL(blob)
                    
                    // 主动释放资源
                    image.onload = null
                    image.onerror = null
                    image.src = ''
                    
                    // 恢复原始的canvas和ctx引用
                    that.canvas = originalCanvas
                    that.ctx = originalCtx
                    
                    // 清理临时Canvas
                    tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height)
                    tempCanvas.width = 1
                    tempCanvas.height = 1
                    
                    resolve(imgUrl)
                  } else {
                    // 恢复原始的canvas和ctx引用
                    that.canvas = originalCanvas
                    that.ctx = originalCtx
                    
                    resolve('不存在')
                  }
                },
                'image/jpeg',
                this.imageQuality
              )
            } else {
              resolve('不存在')
            }
          } catch (error) {
            console.error('处理图片时出错:', error)
            resolve('不存在')
          }
        } else {
          const imgUrl = picState === 2 ? '归档' : '不存在'
          resolve(imgUrl)
        }
      })
    },
    // 点击地图上的点后更新视频到相应位置
    // 如果点击的点对应的这段有视频，则显示这段的第一张图片
    // 如果点击的点对应的这段没有视频，则显示暂无图片，进度调整到上一个有视频的这段
    handleUpdateVideo(dataIndex, isImg = true) {
      this.playFlag = false
      this.collectionClick = true
      if (isImg) {
        if (this.imageUrlMap.has(dataIndex)) {
          this.imageUrl = this.imageUrlMap.get(dataIndex)
        } else {
          this.cacheImg(dataIndex)
        }
      } else {
        // console.log('video---else');
        this.imageUrl = '不存在'
      }
      this.imageIndex = dataIndex

      this.percentage =
        (this.imageIndex / (this.totalElements - 1)).toFixed(2) * 100
      // console.log(' this.collectionClick',  this.collectionClick);
    },
    getPhotoTime() {
      return this.sourceData[this.imageIndex] &&
        this.sourceData[this.imageIndex].photoTime
        ? this.sourceData[this.imageIndex].photoTime.replace('T', ' ')
        : '未知'
    },
    getSchedulerFrameFileTimestamp() {
      return this.sourceData[this.imageIndex] &&
        this.sourceData[this.imageIndex].deviceState &&
        this.sourceData[this.imageIndex].deviceState.schedulerFrameFileTimestamp
        ? this.sourceData[
            this.imageIndex
          ].deviceState.schedulerFrameFileTimestamp.replace('T', ' ')
        : '未知'
    },
    getFrameFileFailureNums() {
      return this.sourceData[this.imageIndex] &&
        this.sourceData[this.imageIndex].deviceState &&
        this.sourceData[this.imageIndex].deviceState.frameFileFailureNums !==
          undefined
        ? this.sourceData[this.imageIndex].deviceState.frameFileFailureNums
        : '未知'
    },
    // 计算对象占用的内存大小（MB）
    getObjectSizeInMB(object) {
      try {
        const jsonString = JSON.stringify(object);
        // 一个字符约等于2字节
        const bytes = jsonString ? jsonString.length * 2 : 0;
        return bytes / (1024 * 1024); // 转换为MB
      } catch (e) {
        console.error('计算对象大小出错:', e);
        return 0;
      }
    },
    // 计算Map占用的内存大小（MB）
    getMapSizeInMB(map) {
      try {
        let totalSize = 0;
        // 计算key的大小
        const keysSize = JSON.stringify(Array.from(map.keys())).length * 2;
        totalSize += keysSize;
        
        // 计算每个值的大小
        map.forEach((value) => {
          // 如果是DataURL，它可能很大
          if (typeof value === 'string') {
            totalSize += value.length * 2; // 粗略估计每个字符2字节
          } else {
            totalSize += JSON.stringify(value).length * 2;
          }
        });
        
        return totalSize / (1024 * 1024); // 转换为MB
      } catch (e) {
        console.error('计算Map大小出错:', e);
        return 0;
      }
    },
    // 释放不再使用的Object URL
    revokeObjectURL(url) {
      if (url && url !== '不存在' && url !== '归档' && url.startsWith('blob:')) {
        try {
          URL.revokeObjectURL(url)
          url = null // 尝试帮助垃圾回收
        } catch (e) {
          console.error('URL释放失败:', e)
        }
      }
    },
    // 因加载数据而暂停播放
    pauseForLoading() {
      if (this.playFlag) {
        console.log('因数据/图片加载暂停播放')
        this.pausedDueToLoading = true
        this.playFlag = false
      }
      this.loading = true
    },
    // 加载完成后恢复播放
    resumeAfterLoading() {
      this.loading = false
      this.isLoadingNextPage = false
      
      if (this.pausedDueToLoading) {
        console.log('数据加载完成，恢复播放')
        this.pausedDueToLoading = false
        this.$nextTick(() => {
          // 使用修正后的方法名
          this.handlePlay()
        })
      }
    },
    // 辅助方法：获取类型对应的颜色和文本
    getTypeInfo(type) {
      // 查找类型对应的信息
      const typeArr = this.allIdentifyType.filter((item) => item.engName === type)
      if (typeArr.length > 0) {
        return {
          color: typeArr[0].color,
          txt: typeArr[0].chineseName
        }
      } else if (type.includes('prop-road-width')) {
        return {
          color: '#0000FF',
          txt: `路宽 ${type.split(':')[1]}m`
        }
      } else {
        return {
          color: '#9C9C9C',
          txt: '其他'
        }
      }
    },
    // 按类型分组处理坐标
    groupByType(coordinates) {
      const groups = {}
      coordinates.forEach(item => {
        if (!groups[item.type]) {
          groups[item.type] = []
        }
        groups[item.type].push(item)
      })
      return groups
    },
  

    // 检查图片状态
    isImageLoaded(index) {
      return this.imageUrlMap.has(index) && 
             this.imageUrlMap.get(index) !== this.imageStateLoading &&
             this.imageUrlMap.get(index) !== this.imageStateNotExist
    },

    // 检查图片是否可播放
    isImagePlayable(index) {
      return this.isImageLoaded(index) && this.imageUrlMap.get(index) !== this.imageStateArchived
    },

    // 更新进度条百分比
    updatePercentage() {
      this.percentage = (this.imageIndex / (this.totalElements - 1 || 1)).toFixed(2) * 100
    },
  },
  deactivated() {
    console.log('deactivated')
    this.handlePause()
  },
  beforeDestroy() {
    console.log('视频组件 beforeDestroy')
    clearTimeout(this.timer)
    this.timer = null
    // 释放所有Object URLs
    this.imageUrlMap.forEach((url) => {
      this.revokeObjectURL(url)
    })
    // 清空所有缓存
    this.imageUrlMap.clear()
    this.sourceData = []
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
      this.ctx = null
    }
    this.canvas = null
  },
}
</script>

<style lang="scss" scoped>
.video-container {
  .video-box {
    width: 100%;
    border-radius: 6px;
    margin-bottom: 15px;
    position: relative;
    height: 370px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 6px;
    }
    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 0 10px;
    }
    .play-icon {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      .el-icon-video-play {
        font-size: 100px;
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }
  .video-progress {
    padding: 0 15px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .slider {
      width: 94%;
    }
    .el-progress {
      width: 96%;
    }
    i {
      font-size: 18px;
      cursor: pointer;
    }
  }
  .video_btn {
    height: 50px;
    background: #edf0f4;
    border-radius: 31px;
    margin: 0 10px;
    padding: 0 5px;
    div {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        flex: 1;
        height: 32px;
        padding: 0 !important;
        background: #ffffff;
        border-radius: 22px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #545e72;
        margin: 0 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #306af1;
          box-shadow: 0px 7px 11px rgba(48, 106, 241, 0.31);
          color: #fff;
        }

        &:last-child {
          padding: 0;
          background: transparent;

          &:hover {
            background: transparent;
            box-shadow: 0px 7px 11px transparent;
            color: #545e72;
          }
        }

        i {
          display: block;
          width: 19px;
          height: 19px;
          margin-right: 8px;

          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }

        &:nth-child(3),
        &:nth-child(4) {
          i {
            width: 16px;
            height: 16px;
          }
        }

        ::v-deep .el-input__inner {
          border-radius: 22px;
          border: none;
        }
      }
    }
  }
}

.not-allowed {
  opacity: 0.5;
  &:hover {
    cursor: not-allowed;
  }
}
.pointer-events-none {
  pointer-events: none;
}
</style>
