const Echarts = {
  data() {
    return {
      mileage_option: {
        legend: {
          icon: 'circle', // 这个字段控制形状
          right: 0,
          top: 0,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            color: '#333',
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '10%',
          top: '24%',
          width: '88%',
          height: '59%',
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 1,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: { // 这个字段控制网格线的样式
              show: true, // 表示显示网格线
              lineStyle: {
                type: 'dashed', // 表示网格线为虚线
              },
            },
            splitNumber: 2,
            axisLine: {
              show: true,
            },
            axisLabel: {
              formatter(value, index) {
                if (value >= 1000 && value < 10000) {
                  value = `${value / 1000}k`
                } else if (value >= 10000) {
                  value = `${value / 10000}w`
                }
                return value
              },
            },
          },
        ],
        series: [
          {
            name: '里程',
            type: 'bar',
            yAxisIndex: 0, // 指定Y轴
            data: [],
            label: {
              show: true,
              // position: ['-50%', '-50%'],
              position: 'top',
              distance: 0,
              color: '#50a2f6',
              fontSize: 11,
            },
            barWidth: '15px',
            itemStyle: {
              normal: {
                color: '#2D8CF0',
              },
            },
          },
        ],
      },
      discovery_option: {
        legend: [
          {
            data: ['病害'],
            top: 0,
            right: '18%',
            icon: 'path://M983.98499004 488.16274671H30.48033212a28.60515091 28.60515091 0 1 0 0 57.21030181H983.98499004a28.60515091 28.60515091 0 1 0 0-57.21030181z',
            textStyle: {
              color: '#333',
            },
          },
          {
            data: ['风险'],
            top: 0,
            right: 0,
            icon: 'path://M983.98499004 488.16274671H30.48033212a28.60515091 28.60515091 0 1 0 0 57.21030181H983.98499004a28.60515091 28.60515091 0 1 0 0-57.21030181z',
            textStyle: {
              color: '#333',
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '10%',
          top: '24%',
          width: '88%',
          height: '59%',
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: { // 这个字段控制网格线的样式
              show: true, // 表示显示网格线
              lineStyle: {
                type: 'dashed', // 表示网格线为虚线
              },
            },
            splitNumber: 2,
            axisLine: {
              show: true,
            },
            axisLabel: {
              formatter(value, index) {
                if (value >= 1000 && value < 10000) {
                  value = `${value / 1000}k`
                } else if (value >= 10000) {
                  value = `${value / 10000}w`
                }
                return value
              },
            },
          },
        ],
        series: [
          {
            name: '病害',
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 0,
            data: [],
            label: {
              show: true,
              position: 'top',
              distance: 0,
              color: '#00CBFB',
              fontSize: 10,
            },
            itemStyle: {
              normal: {
                color: '#00CBFB',
              },
            },
            lineStyle: {
              width: 3, // 线的宽度
            },
          },
          {
            name: '风险',
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 0,
            data: [],
            label: {
              show: true,
              distance: 1,
              position: 'top',
              color: '#FF8766',
              fontSize: 10,
            },
            itemStyle: {
              normal: {
                color: '#FF8766',
              },
            },
            lineStyle: {
              width: 3, // 线的宽度
            },
          },
        ],
      },
      classification_option: {
        tooltip: {
          trigger: 'item',
          formatter: '{b}{d}%',
        },
        legend: {
          type: 'scroll',
          bottom: '-3%',
          x: 'center',
          icon: 'circle',
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            top: '-2%',
            clockWise: false,
            data: [],
            label: {
              show: true,
              fontSize: 10,
              formatter: '{b}{d}%',

            },
            labelLine: {
              minTurnAngle: 60,
              lineStyle: {
                cap: 'round',
              },
            },
            emphasis: {
              label: {
                color(params) {
                  return params.color
                },
              },
            },
          },
        ],
      },
    }
  },
}

export default Echarts
