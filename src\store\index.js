/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:54
 * @LastEditors: guowy
 * @LastEditTime: 2020-02-24 15:04:16
 */
import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate' // 导入插件
import getters from './getters'

Vue.use(Vuex)
// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)
// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})
const store = new Vuex.Store({
  modules,
  getters,
  plugins: [createPersistedState({
    storage: window.sessionStorage, // 存储到会话存储中
    paths: [
      'detail.taskDataObj',
      'roads.allRoads',
      'roads.roadsByBelongTo',
      'roads.lastFetchTime'
    ],
  })],
})
export default store
