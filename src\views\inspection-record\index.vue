<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom: 30px">
      <el-row type="flex" align="middle" :gutter="10">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <UnitSelect
            v-model="listQuery.workUnitId"
            useNodeId
            placeholder="设备单位"
            @input="handleWorkUnitChange"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-select
            v-model="listQuery.deviceKey"
            clearable
            filterable
            placeholder="设备名称"
            @change="handleChangeKey"
          >
            <el-option
              v-for="device in deviceOptions"
              :key="device.deviceKey"
              :value="device.deviceKey"
              :label="device.deviceName"
            />
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.deviceKeyInput"
            :disabled="deviceKeyInputDisabled"
            placeholder="设备ID"
            clearable
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="开始时间"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="结束时间"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.roadFilter"
            clearable
            placeholder="路线名称、编号"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <el-input
            v-model="listQuery.adCode"
            clearable
            placeholder="行政区划编码"
          />
        </el-col>
        <el-button
          class="filter-item ml-5 mr-5"
          :class="{ 'button-group': isSmallScreen }"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
          >查询</el-button
        >
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="ID" prop="id" width="70" />
      <el-table-column align="center" label="任务ID" prop="taskId" width="105">
        <template slot-scope="{ row }">
          {{ row.taskId }}
          <el-tag v-if="row.roadType === 9" size="mini">慢行</el-tag>
          <el-tag v-if="row.roadType === 10" type="success" size="mini"
            >城管</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备编号" prop="deviceKey" />
      <el-table-column align="center" label="设备名称" prop="deviceName" />
      <el-table-column align="center" label="设备单位" prop="workUnit" />
      <el-table-column align="center" label="路线编号" prop="roadNum" />
      <el-table-column align="center" label="路线名称" prop="routeFullName" />
      <el-table-column align="center" label="检测单位" prop="detectUnit" />
      <el-table-column align="center" label="委托单位" prop="delegateUnit" />
      <el-table-column align="center" label="行政区划编码" prop="adCode" />
      <el-table-column align="center" label="巡检开始时间" width="135">
        <template slot-scope="{ row }">{{
          formatTime(row.startTime)
        }}</template>
      </el-table-column>
      <el-table-column align="center" label="巡检结束时间" width="135">
        <template slot-scope="{ row }">
          <template v-if="row.endTime">{{ formatTime(row.endTime) }}</template>
          <template v-else>巡检中</template>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="巡检里程(km)"
        prop="inspectMileage"
        width="107"
      />
      <el-table-column align="center" label="操作" width="150">
        <template slot-scope="{ row }">
          <el-button type="text" @click="handleDetail(row)">详情</el-button>
          <el-button
            v-if="!row.taskId"
            type="text"
            @click="handleCreateTask(row)"
            >创建巡检</el-button
          >
          <el-button
            v-if="row.taskId && !row.endTime"
            type="text"
            :loading="actionLoading"
            @click="handleFinishTask(row)"
            >结束巡检</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 详情弹窗 -->
    <detail-dialog ref="detailDialog" @task-finished="handleTaskFinished" />
  </div>
</template>

<script>
import { getTaskWorkUnits } from "@/api/data";
import {
  getInspectionRecords,
  createInspectionTask,
  finishInspectionTask,
} from "@/api/inspection-record";
import datePickerOptions from "@/mixin/datePickerOptions";
import Pagination from "@/components/Pagination/index.vue";
import DateTimePicker from "@/components/DateTimePicker/index.vue";
import DetailDialog from "./components/DetailDialog.vue";

export default {
  name: "InspectionRecord",
  components: {
    Pagination,
    DateTimePicker,
    DetailDialog,
  },
  mixins: [datePickerOptions],
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        workUnitId: null,
        deviceKey: null,
        startTime: null,
        endTime: null,
        deviceKeyInput: "",
        roadFilter: "",
        adCode: "",
      },
      deviceOptions: [], // 设备下拉选择项数据
      allDevices: [],
      dateFormat: "yyyy-MM-dd HH:mm:ss",
      unitOptions: [], // 设备单位options
      unitDisabled: false,
      deviceKeyInputDisabled: false,
      createTaskLoading: false, // 创建巡检操作按钮加载状态
      finishTaskLoading: false, // 结束巡检操作按钮加载状态
    };
  },
  computed: {
    isSmallScreen() {
      // 根据窗口尺寸判断是否为小屏幕 (对应lg=4或以下)
      return window.innerWidth < 1920; // 大约对应lg断点
    },
  },
  async created() {
    await this.getUnitsOptions();
    this.deviceOptions = this.allDevices;
    this.getList();
  },
  methods: {
    formatTime(time) {
      if (!time) return "";
      return this.$options.filters.parseTime(
        new Date(time),
        "{yyyy}-{mm}-{dd} {hh}:{ii}"
      );
    },
    async getUnitsOptions() {
      const { status, payload } = await getTaskWorkUnits();
      if (status === 200) {
        this.unitOptions = payload;
        const devices = this.unitOptions.flatMap((unit) => unit.devices || []);

        this.deviceOptions = devices;
        this.allDevices = [...devices];
      }
    },
    // async getAllDevices() {
    //   const { payload } = await getTaskDevices()
    //   this.allDevices = payload
    // },
    handleWorkUnitChange(value) {
      this.listQuery.deviceKey = "";
      if (value) {
        const unitArr = this.unitOptions.filter(
          (item) => item.workUnitId === value
        );
        this.deviceOptions = unitArr[0].devices;
      } else {
        this.deviceOptions = this.allDevices;
      }
    },
    getList() {
      this.listLoading = true;
      const { listQuery } = this;

      const query = {
        page: listQuery.currentPage - 1,
        size: listQuery.pageSize,
        workUnitId: listQuery.workUnitId || null,
        deviceKey: listQuery.deviceKey || listQuery.deviceKeyInput || null,
        startTime: listQuery.startTime || null,
        endTime: listQuery.endTime || null,
        roadFilter: listQuery.roadFilter || null,
        adCode: listQuery.adCode || null,
      };

      getInspectionRecords(query).then((response) => {
        this.list = response.payload.content;
        this.total = response.payload.totalElements;
        this.listLoading = false;
      });
    },
    handleFilter() {
      this.listQuery.currentPage = 1;
      this.getList();
    },
    handleChangeKey(value) {
      if (value) {
        this.deviceKeyInputDisabled = true;
        this.listQuery.deviceKeyInput = "";
      } else {
        this.deviceKeyInputDisabled = false;
      }
    },
    // 显示详情弹窗
    handleDetail(row) {
      this.$refs.detailDialog.open(row);
    },
    // 创建巡检任务
    async handleCreateTask(row) {
      if (this.createTaskLoading) {
        this.$message.warning("当前已有巡检在创建！");
        return
      }
      let notification;
      this.$confirm("是否创建巡检?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.createTaskLoading = true;
        try {
          notification = this.$notify({
            title: "创建巡检进行中",
            message: "正在创建巡检，请耐心等待...",
            duration: 0, // 不自动关闭
            position: "top-right",
            iconClass: "el-icon-loading",
            id: row.id, // 使用唯一ID
            offset: 0, // 使用动态偏移让通知不会重叠
          });
          const { status, payload } = await createInspectionTask(row.id);
          if (status === 200) {
            this.$message.success("任务创建成功");
            notification.close();
            this.$notify({
              title: "创建巡检成功",
              message: `ID：${row.id}创建巡检成功`,
              type: "success",
              duration: 5000,
              position: "top-right",
              id: taskId, // 使用相同的ID以替代原通知位置
            });
            this.getList(); // 刷新列表
          } else {
            this.$message.error(payload || "任务创建失败");
          }
        } catch (error) {
          console.error("创建任务失败", error);
          notification?.close();
          this.$notify({
            title: "创建巡检失败",
            message: error.statusText || "请稍后重试",
            type: "error",
            duration: 5000,
            position: "top-right",
            id: taskId, // 使用相同的ID以替代原通知位置
          });
        } finally {
          this.createTaskLoading = false;
        }
      });
    },

    // 结束巡检任务
    async handleFinishTask(row) {
      this.$confirm("是否结束巡检?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.finishTaskLoading = true;
        try {
          const { status, payload } = await finishInspectionTask(row.id);
          if (status === 200) {
            this.$message.success("任务已结束");
            this.getList(); // 刷新列表
          } else {
            this.$message.error(payload || "结束任务失败");
          }
        } catch (error) {
          console.error("结束任务失败", error);
          this.$message.error("结束任务失败，请稍后重试");
        } finally {
          this.finishTaskLoading = false;
        }
      });
    },

    // 处理任务结束事件
    handleTaskFinished() {
      this.getList(); // 刷新列表
    },
  },
};
</script>
