<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <UnitSelect v-model="listQuery.unitId" placeholder="单位名称" useNodeId />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <RoadTypeCascader @change="handleRoadTypeCascaderChange" placeholder="巡查归属/道路" />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="开始时间"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="结束时间"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
        <el-button :disabled="listLoading" class="filter-item ml-5">
          <el-checkbox
            :disabled="listLoading"
            class="filter-item ml-5"
            v-model="isSelectAll"
            @change="handleSelectAllChange"
          >选择全部 {{ downloadAllCount }}</el-checkbox>
        </el-button>
        <el-button
          type="success"
          :disabled="listLoading"
          class="filter-item ml-5"
          icon="el-icon-download"
          @click="handleBatchDownload"
        >批量下载</el-button>
      </el-row>
    </div>

    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
      @select="handleSelectionChange"
      @select-all="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="40" />
      <el-table-column align="center" label="巡查ID" prop="id" width="80" />
      <el-table-column align="center" label="单位名称" prop="workUnitName" />
      <el-table-column align="center" label="巡查归属" prop="belongToStr" width="100">
        <template slot-scope="{row}">
          <el-tag v-if="row.belongToStr" size="mini" :color="getBelongToColor(row.belongTo)">{{row.belongToStr}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="道路名称" prop="roadName" />
      <el-table-column align="center" label="道路编号" prop="roadCode" width="100" />
      <el-table-column align="center" label="开始时间" prop="startTime">
        <template slot-scope="{row}">{{ new Date(row.startTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</template>
      </el-table-column>
      <el-table-column align="center" label="结束时间" prop="endTime">
        <template slot-scope="{row}">{{ new Date(row.endTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</template>
      </el-table-column>
      <el-table-column align="center" label="起点桩号" prop="startPileStr" width="100" />
      <el-table-column align="center" label="终点桩号" prop="endPileStr" width="100" />
      <el-table-column align="center" label="巡查里程(km)" prop="inspectMileage" width="120" />
      <el-table-column align="center" label="巡查人员" prop="realName" />
      <el-table-column align="center" label="更新时间" prop="updateTime">
        <template slot-scope="{row}">{{ new Date(row.updateTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}</template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template slot-scope="{row}">
          <el-button type="text" @click="handleDetail(row)">详情</el-button>
          <el-button type="text" @click="handleExport(row.id)">下载</el-button>
          <el-button type="text" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="paginationParams.page"
      :limit.sync="paginationParams.size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getOrderRecordList,
  deleteOrderRecord,
} from '@/api/order-records'
import Pagination from '@/components/Pagination/index.vue'
import DateTimePicker from '@/components/DateTimePicker/index.vue'
import RoadTypeCascader from '@/components/RoadTypeCascader.vue'
import { getBelongToColor } from '@/utils/cd_constants'
import _ from 'lodash'
// 引入下载mixin
import downloadMixin from './mixins/downloadMixin'
import datePickerOptions from '@/mixin/datePickerOptions'

export default {
  name: 'OrderRecord',
  components: { Pagination, DateTimePicker, RoadTypeCascader },
  // 添加downloadMixin
  mixins: [datePickerOptions, downloadMixin],
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        unitId: null,
        startTime: null,
        endTime: null,
        belongTo: null,
        roadName: null,
      },
      paginationParams: {
        page: 1,
        size: 10,
      },
      cloneListQuery: {},
      ignoredIds: [],
      selectedItems: [], // 选中的项目
      isSelectAll: false, // 是否全选
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
    // 下载全部数量
    downloadAllCount() {
      return this.total - this.ignoredIds.length > 0
        ? '(' + (this.total - this.ignoredIds.length) + '条)'
        : ''
    },
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      let params = {
        ...this.listQuery,
        ...this.paginationParams,
      }
      params.page -= 1
      params = _.pickBy(params, (value) => {
        if (value === undefined || value === null || value === '') return false
        if (Array.isArray(value) && value.length === 0) return false
        return true
      })
      getOrderRecordList(params)
        .then((response) => {
          this.list = response.payload.content
          this.total = response.payload.totalElements

          this.$nextTick(() => {
            if (this.isSelectAll && this.$refs.tableRef) {
              // 先清除所有选择
              this.$refs.tableRef.clearSelection()

              // 全选当前页面所有行，但排除 ignoredIds 中的行
              this.list.forEach((row) => {
                if (!this.ignoredIds.includes(row.id)) {
                  this.$refs.tableRef.toggleRowSelection(row, true)
                }
              })
            } else if (this.selectedItems.length > 0 && this.$refs.tableRef) {
              // 对于非全选模式，根据保存的selectedItems恢复选中状态
              this.$refs.tableRef.clearSelection()

              // 为当前页面中已选中的项目恢复选中状态
              this.list.forEach((row) => {
                if (this.selectedItems.some((item) => item.id === row.id)) {
                  this.$refs.tableRef.toggleRowSelection(row, true)
                }
              })
            }
          })
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleExport(id) {
      // 单个导出，使用批量导出接口
      const ids = [id]
      this.downloadRecords(ids, 1)
    },
    handleDetail(row) {
      const { id } = row
      sessionStorage.setItem(
        `order-records/detail/${id}`,
        JSON.stringify(row)
      )

      this.$router.push({
        path: `/order-records/detail/${id}`,
        // query: {
        //   startTime: dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
        //   endTime: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
        //   userId,
        //   roadName,
        //   belongTo,
        // },
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteOrderRecord(id)
          .then(() => {
            this.$message.success('删除成功')
            this.getList()
          })
          .catch(() => {
            this.$message.error('删除失败')
          })
      })
    },
    handleFilter() {
      this.paginationParams.page = 1
      this.isSelectAll = false
      // 重置选中项和忽略项
      this.selectedItems = []
      this.ignoredIds = []
      
      this.cloneListQuery = _.cloneDeep(this.listQuery)
      this.getList()
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    // 选择全部变化
    handleSelectAllChange() {
      if (this.total <= 0) {
        this.$message.warning('暂无数据，无法选择')
        this.isSelectAll = false
        return
      }

      // 重置忽略ID列表
      this.ignoredIds = []
      
      if (this.isSelectAll && this.$refs.tableRef) {
        this.$nextTick(() => {
          this.list.forEach((row) => {
            this.$refs.tableRef.toggleRowSelection(row, true)
          })
        })
      } else if (this.$refs.tableRef) {
        this.$refs.tableRef.clearSelection()
        this.selectedItems = []
      }
    },
    // 选择变化
    handleSelectionChange(selection) {
      if (this.isSelectAll) {
        // 全选模式下，计算忽略的ID
        const currentPageIds = this.list.map((item) => item.id)
        const selectedIds = selection.map((item) => item.id)
        
        // 保留不在当前页面的忽略ID
        this.ignoredIds = this.ignoredIds.filter(
          (id) => !currentPageIds.includes(id)
        )
        
        // 添加当前页面中未选中的ID到忽略列表
        const currentPageIgnoredIds = currentPageIds.filter(
          (id) => !selectedIds.includes(id)
        )
        
        this.ignoredIds = [...this.ignoredIds, ...currentPageIgnoredIds]
        
        // 去重
        this.ignoredIds = [...new Set(this.ignoredIds)]
      } else {
        // 更新已选项，处理选中和取消选中的情况
        // 1. 当前页面的所有项目ID
        const currentPageIds = this.list.map((item) => item.id)

        // 2. 保留原有的不在当前页面的已选项
        const nonCurrentPageSelected = this.selectedItems.filter(
          (item) => !currentPageIds.includes(item.id)
        )

        // 3. 合并当前页面的选中项和其他页面已选项
        this.selectedItems = [...nonCurrentPageSelected, ...selection]
        
        // 清空忽略ID列表
        this.ignoredIds = []
      }
    },
    // 批量下载
    handleBatchDownload() {
      if (!this.selectedItems.length && !this.isSelectAll) {
        this.$message.warning('请至少选择一项进行下载')
        return
      }

      if (this.isSelectAll) {
        // 全选情况下，使用全选模式下载
        const downloadCount = this.total - this.ignoredIds.length
        const options = {
          isSelectAll: true,
          ignoredIds: this.ignoredIds,
          cloneListQuery: this.cloneListQuery
        }
        this.downloadRecords([], downloadCount, options)
      } else {
        // 非全选情况，直接使用已选择的项目ID
        const ids = this.selectedItems.map((item) => item.id)
        this.downloadRecords(ids, ids.length)
      }
    },
    handleRoadTypeCascaderChange(val) {
      // val: { belongTo, roadName }
      this.listQuery.belongTo = val.belongTo
      this.listQuery.roadName = val.roadName
      // 如有需要可自动触发查询
      // this.getList()
    },
    getBelongToColor(belongTo) {
      return getBelongToColor(belongTo)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-tag {
  color: #333333;
  border: none !important;
  line-height: 20px;
}
</style>
