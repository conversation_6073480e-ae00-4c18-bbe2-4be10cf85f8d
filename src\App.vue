<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  created() {
    const keyCodeMap = {
    // 91: true, // command
      61: true,
      107: true, // 数字键盘 +
      109: true, // 数字键盘 -
      173: true, // 火狐 - 号
      187: true, // +
      189: true, // -
    }
    // 覆盖ctrl||command + ‘+’/‘-’
    document.onkeydown = function (event) {
      const e = event || window.event
      const ctrlKey = e.ctrlKey || e.metaKey
      if (ctrlKey && keyCodeMap[e.keyCode]) {
        e.preventDefault()
      } else if (e.detail) { // Firefox
        event.returnValue = false
      }
    }
    // 覆盖鼠标滑动
    document.body.addEventListener('wheel', (e) => {
      if (e.ctrlKey) {
        if (e.deltaY < 0) {
          e.preventDefault()
          return false
        }
        if (e.deltaY > 0) {
          e.preventDefault()
          return false
        }
      }
    }, { passive: false })
  },
}
</script>
<style lang="scss">
  /* 去掉地图版权信息 */
  .anchorBL,
  .anchorTR,
  .BMap_zlHolder {
    display: none;
    visibility: hidden;
  }
  /* 去掉高德地图logo */
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }

  /* 查询 */
  .filter-container {
    display: flex;
    flex-wrap: wrap ;
    gap: 10px 0px ;

    .el-select,
    .el-input, 
    .el-date-editor {
      width: 100%;
      height: 36px;
      margin-right: 10px;
    }
    .el-row--flex {
      flex-wrap: wrap;
      gap: 10px 0px ;
      .el-col {
        height: 36px;
      }
    }
 
    /* 按钮组样式 */
    .button-group {
      display: flex;
      align-items: center;
      margin-left: auto;  /* 关键：使按钮组靠右 */
    }
    
  }

  /* 菜单栏 */
  .sidebar-logo-link {
    text-align: left !important;
    padding-left: 20px !important;
    .sidebar-logo {
      width: 31px!important;
      height: 28px!important;
    }
  }
  .collapse {
    .sidebar-logo-link {
      text-align: center !important;
      padding-left: 0 !important;
    }
  }

  .el-table--border th {
    color: #333;
    background: #f4f4f5;
  }

  .viewer-backdrop {
    background-color: rgba(0, 0, 0, 75%)!important;
  }
</style>
