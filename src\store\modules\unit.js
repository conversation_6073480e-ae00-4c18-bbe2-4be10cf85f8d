import { getUnitInfo } from '@/api/unit'

const state = {
  unitList: [],
}

const mutations = {
  SET_UNIT_LIST: (state, list) => {
    state.unitList = list
  },
  RESET_STATE: (state) => {
    state.unitList = []
  },
}

const actions = {
  async fetchUnitList({ commit, state, dispatch }) {
    // 如果缓存有数据，直接返回缓存数据
    if (state.unitList.length > 0) {
      console.log(' // 如果缓存有数据，直接返回缓存数据')
      return state.unitList
    }
    // 缓存过期或没有缓存，重新获取数据
    const res = await getUnitInfo()
    commit('SET_UNIT_LIST', res.payload)
    return res.payload
  },

  // 创建、修改单位 后强制刷新单位列表
  async refreshUnitList({ commit }) {
    const res = await getUnitInfo()
    commit('SET_UNIT_LIST', res.payload)
    return res.payload
  },
  // 添加重置状态的 action
  resetState({ commit }) {
    commit('RESET_STATE')
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
