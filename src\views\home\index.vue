<!--  -->
<template>
  <Chengde v-if="isChengDe" />
  <div v-else class="container">
    <Admin v-if="admin" />
    <NotAdmin v-else />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { AUTH } from '@/directive/auth'
// 懒加载
const NotAdmin = () => import('./components/not-admin.vue')
const Admin = () => import('./components/admin.vue')
const Chengde = () => import('@/views/chengde/screen/index.vue')
export default {
  name: 'Home',
  components: { Admin, NotAdmin, Chengde },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
    // 承德市政用户 显示承德地图
    isChengDe() {
      return AUTH('cheng-de-dp:*:cheng-de-dp') && !AUTH('*:*:*') && !this.admin
    },
  },
}

</script>
<style lang="scss" scoped>
  .container {
    width: 100%;
    height: calc(100vh - 94px);
    position: relative;

    .tool-container {
      position: absolute;
      right: 20px;
      top: 20px;
      z-index: 1;
    }

    .bm-view {
      width: 100%;
      height: 100%;

      ::v-deep.BMap_pop {
        & > div:nth-of-type(1),
        & > div:nth-of-type(2),
        & > div:nth-of-type(3),
        & > div:nth-of-type(5),
        & > div:nth-of-type(7),
        & > div:nth-of-type(8),
        .BMap_top,
        .BMap_center,
        .BMap_bottom{
          display: none;
        }

        & > div:nth-of-type(9){
          background: #fff;
          padding: 15px 20px;
          width: 260px!important;
          height: auto!important;
          border-radius: 6px;

          .BMap_bubble_title {
            color: #2d8cf0!important;
            font-weight: bold;
            font-size: 18px;
          }
          .c-info {
            color: #2d8cf0!important;
            line-height: 30px;
          }
        }

        & > img:nth-of-type(1) {
          left: 290px!important;
          top: 30px!important;
        }
      }

      ::v-deep .BMap_shadow {
        display: none !important;
      }

    }
    .bm-view_72 {
      width: 72%;
      max-width: calc(100% - 512px);
    }

    .inspection-panel {
      width: 500px;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1;
      background: #f6f6f6;
      overflow: auto;
      padding: 10px;

      .card {
        position: relative;
        border-radius: 10px;
        height: calc(100vh - 120px);
        border: none;
        box-shadow: none;

        .title {
          display: flex;
          align-items: center;
          color: #394380;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 20px;
          i {
            color: #394380;
            font-size: 20px;
            margin-right: 10px;
          }
        }
        .info {
          font-size: 14px;
          color: #606266;
          padding-bottom: 20px;

          p {
            display: flex;
            align-items: center;
          }

          span {
            width: 12px;
            height: 12px;
            display: inline-block;
            border-radius: 50%;
            margin-left: 10px;
          }
          .using {
            background: rgba(147, 229, 59);
          }
          .no-using {
            background: #909399;
          }
        }
        .el-icon-close {
          position: absolute;
          top: 20px;
          right: 20px;
          font-size: 18px;
          font-weight: bold;
          color:#394380;
          cursor: pointer;
        }

        .pagination-container {
          padding-bottom: 0;
          padding-top: 20px;
        }
      }
    }
  }
</style>

<style lang="scss">
.card {

  .title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
    i {
      font-size: 20px;
      margin-right: 4px;
    }
    span {
      color: #333;
    }
  }
  .boundary {
    display: inline-block;
    height: 20px;
    width: 3px;
    margin: 0 25px;
    background-color: #dcdfe6;
    border-radius: 2px;
  }
  .cursor-p {
    cursor: pointer;
    &:hover {
      i, span {
        color: #394380 !important;
      }
    }
  }
  .live-box {
    width: 573px;
    aspect-ratio: 16/9;
  }
  .active-tab {
    i, span {
      color: #394380 !important;
    }
  }
}
</style>
