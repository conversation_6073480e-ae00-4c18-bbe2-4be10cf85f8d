/*
 * @Author: wangyj
 * @Date: 2022-11-16 18:00:44
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-17 11:05:32
 */

<template>
  <el-dialog :title="dialogType === 'update' ? '修改道路': '添加道路'" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="行政区划编码" prop="adCode">
        <el-input v-model="temp.adCode" placeholder="请输入行政区划编码" />
      </el-form-item>
      <el-form-item label="道路编号" prop="roadCode">
        <el-input v-model="temp.roadCode" placeholder="请输入道路编号" />
      </el-form-item>
      <el-form-item label="道路名称" prop="roadName">
        <el-input v-model="temp.roadName" placeholder="请输入道路名称" />
      </el-form-item>
      <el-form-item label="路面宽度（m）" prop="pavementWidth">
        <el-input v-model="temp.pavementWidth" placeholder="请输入路面宽度" />
      </el-form-item>
      <el-form-item label="道路等级" prop="roadLevel">
        <el-radio-group v-model="temp.roadLevel">
          <el-radio :label="5">高速公路</el-radio>
          <el-radio :label="1">一级公路</el-radio>
          <el-radio :label="2">二级公路</el-radio>
          <el-radio :label="3">三级公路</el-radio>
          <el-radio :label="4">四级及以下公路</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="路面类型" prop="roadType">
        <el-radio-group v-model="temp.roadType">
          <el-radio :label="1">沥青路面</el-radio>
          <el-radio :label="2">水泥混凝土路面</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="检测方向" prop="detectDirection">
        <el-radio-group v-model="temp.detectDirection">
          <el-radio :label="1">上行</el-radio>
          <el-radio :label="2">下行</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="起点桩号" prop="startPileNum">
        <el-input v-model="temp.startPileNum" placeholder="请输入起点桩号" />
      </el-form-item>
      <el-form-item label="止点桩号" prop="endPileNum">
        <el-input v-model="temp.endPileNum" placeholder="请输入止点桩号" disabled />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='update' ? updateData() : createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateDetection } from '@/api/data'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dialogType: 'add',
      temp: {},
      dialogFormVisible: false,
      rules: {
        detectDirection: [{ required: true, message: '请选择检测方向', trigger: 'blur' }],
        roadName: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
        pavementWidth: [{ required: true, message: '请输入路面宽度', trigger: 'blur' }],
        roadLevel: [{ required: true, message: '请选择道路级别', trigger: 'blur' }],
        roadType: [{ required: true, message: '请选择路面类型', trigger: 'blur' }],
        startPileNum: [
          { required: true, message: '请输入起点桩号', trigger: 'blur' },
          { pattern: /^(?:[1-9]\d*|0)(?:\.\d{1,3})?$/, message: '请输入大于等于0的数字, 小数最多保留三位' },
        ],
        // adCode: [{ pattern: /^[1-8][0-7]\d{4}$/, message: '行政区划编码格式不正确' }],
      },
    }
  },
  watch: {
    async dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        console.log(this.temp)
        if (this.formData.id) {
          this.dialogType = 'update'
        } else {
          this.dialogType = 'add'
        }
        if (this.admin) {
          await this.getWorkUnits()
          if (this.workUnit) {
            const arr = this.workUnits.filter((item) => item.workUnit === this.workUnit)
            this.$set(this.temp, 'userId', arr[0].id)
          }
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const {
            id, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          } = this.temp

          const tempData = {
            id, adCode, roadCode, detectDirection, startPileNum, roadType, roadLevel, roadName, pavementWidth, detectUnit, delegateUnit,
          }

          if (detectDirection !== this.formData.detectDirection || startPileNum !== this.formData.startPileNum || roadType !== this.formData.roadType || roadLevel !== this.formData.roadLevel || pavementWidth !== this.formData.pavementWidth) {
            this.$confirm('修改路面宽度、道路等级、路面类型、检测方向和起点桩号后将自动刷新数据，请确认是否修改?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }).then(() => {
              updateDetection(tempData).then(async () => {
                this.dialogFormVisible = false
                this.$emit('refreshData', tempData)
              })
            }).catch(() => {

            })
          } else {
            updateDetection(tempData).then(() => {
              this.dialogFormVisible = false
              this.$emit('refreshTable')
            })
          }
        }
      })
    },
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) { }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
  },
}
</script>
