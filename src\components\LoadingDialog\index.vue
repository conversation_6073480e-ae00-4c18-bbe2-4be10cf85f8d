<template>
  <el-dialog
    class="loading-dialog"
    title="提示"
    :center="true"
    :show-close="false"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="300px"
  >
    <div class="content">
      <i class="el-icon-loading" />
      <div> {{ text }} </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LoadingDialog',
  props: {
    text: {
      type: String,
      default: '请求数据中，请稍等。。。',
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
}
</script>

<style lang="scss" scoped>
  .loading-dialog {
    ::v-deep .el-dialog {
      margin-top: 0!important;
      top: 50%;
      transform: translateY(-60%);
    }
    .content {
        display: flex;
        // flex-direction: column;
        justify-content: center;
        align-items: center;
        i {
            font-size: 30px;
        }
        div {
            margin-left: 10px;
        }
    }
  }
</style>
