<!--  -->
<template>
  <div class="app-container">
    
    <div class="filter-container" style="margin-bottom:30px; width: 100%;">
      <el-row type="flex" align="middle" :gutter="10" style="width: 100%;">
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-select v-model="listQuery.applyType" class="filter-item" placeholder="请选择识别类型" clearable>
            <el-option :value="1" label="图像" />
            <el-option :value="2" label="视频" />
          </el-select>
        </el-col>
        <el-button class="filter-item ml-10" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">
          添加模型
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="识别类型" prop="applyType">
        <template slot-scope="{row}">
          <span v-if="row.applyType === 1">图像</span>
          <span v-if="row.applyType === 2">视频</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="模型类型" prop="modelType">
        <template slot-scope="{row}">
          <span>{{ modelTypeToChineseMap(row.modelType) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="版本号" prop="versionName" />
      <el-table-column align="center" label="API地址" prop="apiUrl" />
      <el-table-column align="center" label="模型状态">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="1"
            inactive-value="0"
            @change="handleChangeModels(row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="更新时间">
        <template slot-scope="{row}">
          {{ new Date(row.updateTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleCoefficient(row)">置信度设置</el-button>
          <el-button type="text" @click="handleUpdate(row)">修改</el-button>
          <el-button type="text" @click="handleDelete(row,$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <CreateDialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" />
    <CoefficientDialog ref="coefficientDialog" />
  </div>
</template>

<script>
import ConstantsMixin from '@/mixin/constants'
import Pagination from '@/components/Pagination/index.vue'
import { getModels, deleteModel, modelChange } from '@/api/model'
import CreateDialog from './components/create-dialog.vue'
import CoefficientDialog from './components/coefficient-dialog.vue'

export default {
  components: { Pagination, CreateDialog, CoefficientDialog },
  mixins: [ConstantsMixin],
  data() {
    return {
      listQuery: {
        applyType: null,
        currentPage: 1,
        pageSize: 10,
      },
      listLoading: false,
      list: [],
      total: 0,
      temp: {},
    }
  },
  created() {
    this.getList()
  },

  methods: {
    async getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, applyType,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        applyType: applyType === '' ? null : applyType,
        sort: 'versionName,desc', // 模型仓库列表的排序前端不用传了，我在后端指定排序，按照路面类型+版本号排序(传不传都可以)
      }
      const { payload } = await getModels(query)
      this.list = payload.content
      this.total = payload.totalElements
      this.listLoading = false
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    handleCoefficient(row) {
      this.$refs.coefficientDialog.show(row)
    },
    handleUpdate(row) {
      this.temp = { ...row }
      console.log('handleUpdate', row)
      this.$refs.createDialog.show()
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该模型吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteModel(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getList()
      })
    },
    handleChangeModels(row) {
      modelChange({ id: row.id }).then((res) => {
        this.getList()
      }).catch((err) => {

      })
    },
    modelTypeToChineseMap(modelType) {
      const that = this
      return that.Model_Cn_Name[modelType] || ''
    },
  },
}

</script>
<style lang='scss' scoped>
</style>
