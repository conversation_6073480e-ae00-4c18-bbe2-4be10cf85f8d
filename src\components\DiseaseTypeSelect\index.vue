<template>
  <el-cascader
    ref="cascaderRef"
    v-model="selectedValue"
    :placeholder="placeholder"
    :options="options"
    :props="{ checkStrictly: true, value: 'engName', label: 'chineseName', expandTrigger: 'hover' }"
    :append-to-body="false"
    :show-all-levels="showAllLevels"
    clearable
    filterable
    @change="handleChange"
  ></el-cascader>
</template>

<script>
import CanvasMixin from '@/mixin/canvas';

export default {
  name: 'DiseaseTypeSelect',
  mixins: [CanvasMixin], // 仍然需要依赖这个mixin获取基础数据
  props: {
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择或输入病害类型'
    },
    showAllLevels: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedValue: [],
      options: [],
      diseaseParams: {
        type: null,
        damageType: null,
        roadType: null,
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.selectedValue = val;
      }
    }
  },
  async created() {
    await this.initOptions();
  },
  methods: {
    async initOptions() {
      // 初始化选项数据
      if (typeof this.getModelIdentifyTypes !== 'function') {
        console.error('病害类型组件需要依赖CanvasMixin');
        return;
      }
      
      await this.getModelIdentifyTypes();
      
      // 构建选项数据
      this.options = [
        {
          engName: -1,
          chineseName: '沥青路面病害',
          children: this.allIdentifyType.filter(
            item => item.classification === 1 && item.roadType === 1
          ),
        },
        {
          engName: -2,
          chineseName: '水泥路面病害',
          children: this.allIdentifyType.filter(
            item => item.classification === 1 && item.roadType === 2
          ),
        },
        {
          engName: 3,
          chineseName: '路面风险',
          children: this.allRoadForeignMatter,
        },
        {
          engName: 5,
          chineseName: '沿线设施损坏',
          children: this.allAlongLine,
        },
        {
          engName: 9,
          chineseName: '慢行病害',
          children: this.slowPatrolType,
        },
        {
          engName: 10,
          chineseName: '城市管理问题',
          children: this.cityManagementType,
        },
        {
          engName: 20,
          chineseName: '路基病害',
          children: this.subgradeDisease,
        },
        {
          engName: 40,
          chineseName: '桥梁病害',
          children: this.bridgeDisease,
        },
        {
          engName: 50,
          chineseName: '隧道病害',
          children: this.tunnelDisease,
        },
        {
          engName: 90,
          chineseName: '其他',
          children: this.otherDisease,
        },
        
      ];
    },
    handleChange(value) {
      console.log('value', value);
      // 重置参数
      this.diseaseParams.type = null;
      this.diseaseParams.damageType = null;
      this.diseaseParams.roadType = null;

      // 处理选择
      if (this.isAllOption(value)) {
        // 处理"全部"选项
      } else if (Array.isArray(value)) {
        // 处理数组选择
        this.handleArraySelection(value);
      } else {
        // 处理单值选择
        this.diseaseParams.damageType = value;
      }
      
      // 发送事件
      this.$emit('input', value);
      this.$emit('change', this.diseaseParams);
      
      // 关闭下拉框
      this.$refs.cascaderRef.dropDownVisible = false;
    },
    // 判断是否选择了"全部"选项
    isAllOption(value) {
      return (
        value &&
        (value === 'ALL' ||
          (Array.isArray(value) && value[0] === 'ALL') ||
          (Array.isArray(value) && value.length === 0))
      );
    },
    handleArraySelection(value) {
      // 获取第一个值（顶级类别）和最后一个值（最具体选择）
      const firstValue = value[0];
      const lastValue = value[value.length - 1];

      // 选择了多级
      if (value.length > 1) {
        this.diseaseParams.damageType = lastValue;
      }
      // 只选择了一级
      else if (value.length === 1) {
        // 特殊处理沥青和水泥路面病害
        if (firstValue === -1 || firstValue === -2) {
          this.diseaseParams.roadType = firstValue === -1 ? 1 : 2;
          this.diseaseParams.type = 1;
          return;
        }
        this.diseaseParams.type = firstValue;
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-cascader-menu__wrap {
  min-height: 375px !important;
}
</style> 