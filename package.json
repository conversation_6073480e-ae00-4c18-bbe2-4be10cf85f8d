{"name": "road-disease", "version": "1.0.0", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@robu/formatdate": "1.0.0", "@robu/menu-show": "1.0.8", "@robu/request": "1.0.0", "@robu/tagsview": "1.0.0", "@robu/user": "1.0.8", "@robu/validator": "1.0.0", "@vuemap/vue-amap": "legacy", "animate.css": "^4.1.1", "core-js": "^3.23.0", "dayjs": "^1.11.13", "echarts": "^5.3.3", "element-ui": "2.13.2", "html2canvas": "^1.4.1", "js-cookie": "3.0.1", "js-pinyin": "^0.2.7", "node-sass": "^6.0.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "^0.0.2", "sass": "^1.39.0", "v-viewer": "^1.6.4", "vue": "2.6.14", "vue_qrcodes": "^1.1.3", "vue-baidu-map": "^0.21.22", "vue-baidu-map-v3": "^1.0.2", "vue-count-to": "^1.0.13", "vue-router": "3.0.6", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^6.8.0", "eslint-config-airbnb-base": "14.2.0", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-webpack": "0.12.2", "eslint-plugin-html": "6.0.2", "eslint-plugin-import": "2.22.0", "eslint-plugin-vue": "6.2.2", "hasown": "^2.0.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass-loader": "^10.2.0", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.14"}, "engines": {"node": ">=10.0", "yarn": ">=1.22.4"}, "browserslist": ["> 1%", "last 2 versions"]}