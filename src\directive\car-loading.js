function addLoading(container) {
  const div = document.createElement('div')
  div.setAttribute('class', 'loading-next')
  const htmls = `
      <div class="loading-container">
        <div class="loading-car">
          <div class="loading-car-body">
            <div class="loading-car-top-back">
              <div class="loading-back-curve"></div>
            </div>
            <div class="loading-car-gate"></div>
            <div class="loading-car-top-front">
              <div class="loading-wind-sheild"></div>
            </div>
            <div class="loading-bonet-front"></div>
            <div class="loading-stepney"></div>
          </div>
          <div class="loading-boundary-tyre-cover">
            <div class="loading-boundary-tyre-cover-back-bottom"></div>
            <div class="loading-boundary-tyre-cover-inner"></div>
          </div>
          <div class="loading-tyre-cover-front">
            <div class="loading-boundary-tyre-cover-inner-front"></div>
          </div>
          <div class="loading-base-axcel">
          </div>
          <div class="loading-front-bumper"></div>
          <div class="loading-tyre">
            <div class="loading-gap"></div>
          </div>
          <div class="loading-tyre loading-front">
            <div class="loading-gap"></div>
          </div>
          <div class="loading-car-shadow"></div>
        </div>
        <div class="loading-street">
          <div class="loading-street-stripe"></div>
        </div>
      </div>
		`
  div.innerHTML = htmls
  container.insertBefore(div, container.childNodes[0])
}
function removeLoading(container) {
  const el = document.querySelector('.loading-next')
  el?.parentNode?.removeChild(el)
}

export default {
  bind(el, binding) {
    if (binding.value) {
      addLoading(el)
    }
  },
  update(el, binding) {
    if (binding.value !== binding.oldValue) {
      if (binding.value) {
        addLoading(el)
      } else {
        removeLoading(el)
      }
    }
  },
  unbind(el) {
    removeLoading(el)
  },
}
