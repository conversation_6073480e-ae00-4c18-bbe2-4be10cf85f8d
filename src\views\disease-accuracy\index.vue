/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:01:58
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-02-27 14:25:45
 */

<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <UnitSelect v-model="listQuery.workUnitId" placeholder="设备单位"  useNodeId @input="handleWorkUnitChange"/>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <el-select v-model="listQuery.deviceKey" clearable filterable placeholder="设备名称">
            <el-option v-for="device in deviceOptions" :key="device.deviceKey" :value="device.deviceKey" :label="device.deviceName" />
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <DateTimePicker
            v-model="listQuery.startTime"
            placeholder="开始时间"
            :picker-options="startPickerOptions(listQuery.endTime)"
          />
        </el-col>
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <DateTimePicker
            v-model="listQuery.endTime"
            placeholder="结束时间"
            :picker-options="endPickerOptions(listQuery.startTime)"
            isEndTime
          />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
        <el-button class="filter-item" icon="el-icon-download" @click="handleExport">
          导出
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="设备单位" prop="workUnit" width="250" />
      <el-table-column align="center" label="设备名称" prop="deviceName" />
      <el-table-column align="center" label="巡检开始时间">
        <template slot-scope="{row}">
          {{ new Date(row.startTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="巡检结束时间">
        <template slot-scope="{row}">
          <template v-if="row.hasEnd">
            {{ new Date(row.endTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
          </template>
          <template v-else>巡检中</template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="巡检里程(km)" prop="inspectMileage" />
      <el-table-column align="center" label="起点桩号" prop="startPileNum" />
      <el-table-column align="center" label="止点桩号" prop="endPileNum" />
      <el-table-column align="center" label="准确率" width="350">
        <template slot-scope="{row,$index}">
          <el-table :data="row.damageStatisticsList" size="mini">
            <el-table-column align="center" label="病害名称" prop="chineseName" />
            <el-table-column align="center" label="数量" prop="totalCount" />
            <el-table-column align="center" label="查准率(%)" prop="accuracyRate" />
            <el-table-column align="center" label="查全率(%)" prop="recallRate" />
          </el-table>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getTaskWorkUnits, getTaskDevices,
} from '@/api/data'
import { getDiseaseAccuracyStatistics } from '@/api/system'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination/index.vue'
import { exportFile, obj2Param } from '@/utils'
import datePickerOptions from '@/mixin/datePickerOptions'
import DateTimePicker from '@/components/DateTimePicker/index.vue'

export default {
  name: 'DataView',
  components: { Pagination, DateTimePicker },
  mixins: [datePickerOptions],
  data() {
    return {
      origList: null,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        deviceKey: '',
        workUnitId: null,
        startTime: null,
        endTime: null,
      },
      temp: {},
      dateFormat: 'yyyy-MM-dd HH:mm:ss',
      deviceOptions: [],
      dataType: 1, // 1为所有数据，2为问题数据
      unitOptions: [],
      unitDisabled: false,
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
      workUnitId: (state) => state.account.workUnitId,
    }),
  },
  async created() {
    await this.getUnitsOptions()
    if (!this.admin) {
      this.listQuery.workUnitId = this.workUnitId
      this.unitDisabled = true
    }

    this.getList()
  },
  methods: {
    async getUnitsOptions() {
      const { status, payload } = await getTaskWorkUnits()
      this.unitOptions = payload
      if (status === 200) {
        this.unitOptions = payload
        const devices = this.admin 
        ? this.unitOptions.flatMap(unit => unit.devices || [])
        : (this.unitOptions.find(unit => unit.workUnitId === this.workUnitId)?.devices || [])
      
        this.deviceOptions = devices;
        this.allDevices = [...devices];
      }
    },
    // async getAllDevices() {
    //   const { payload } = await getTaskDevices()
    //   this.allDevices = payload
    // },
    handleWorkUnitChange(value) {
      this.listQuery.deviceKey = ''
      if (value) {
        const unitArr = this.unitOptions.filter((item) => item.workUnitId === value)
        this.deviceOptions = unitArr[0].devices
      } else {
        this.deviceOptions = this.allDevices
      }
    },
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, workUnitId, deviceKey, startTime, endTime,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        workUnitId,
        deviceKey: deviceKey === '' ? null : deviceKey,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
      }
      getDiseaseAccuracyStatistics(query).then((response) => {
        // this.origList = response.payload.content
        // if (this.dataType === 1) {
        //   this.list = response.payload.content
        // } else if (this.dataType === 2) {
        //   this.list = this.origList.filter((item) => item.dataNotEnough || item.dataMissLarge || item.bootStartTimeChanged)
        // }
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleExport() {
      const {
        workUnitId, deviceKey, startTime, endTime,
      } = this.listQuery
      const params = {
        workUnitId,
        deviceKey: deviceKey === '' ? null : deviceKey,
        startTime: startTime === '' ? null : startTime,
        endTime: endTime === '' ? null : endTime,
      }
      const paramsStr = obj2Param(params).slice(1)

      exportFile(`inspect-tasks/exportDamageStatistics?${paramsStr}`)
    },
  },
}
</script>
<style lang="scss" scoped>
  // ::v-deep .el-table__row {
  //   &:hover {
  //     td {
  //       background: rgb(246, 247, 251)!important;
  //     }
  //   }
  // }
</style>
