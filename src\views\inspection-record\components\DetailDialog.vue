<template>
  <el-dialog
    title="智能巡检记录详情"
    :visible.sync="visible"
    width="60%"
    :close-on-click-modal="false"
    :append-to-body="true"
    style="top: -5vh"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div class="detail-info">
        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ detail.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">任务ID:</span>
            <span class="detail-value">
              {{ detail.taskId }}
              <el-tag v-if="detail.roadType === 9" size="mini">慢行</el-tag>
              <el-tag v-if="detail.roadType === 10" type="success" size="mini"
                >城管</el-tag
              >
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">设备编号:</span>
            <span class="detail-value">{{ detail.deviceKey }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">设备名称:</span>
            <span class="detail-value">{{ detail.deviceName || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">设备单位:</span>
            <span class="detail-value">{{ detail.workUnit || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">路线编号:</span>
            <span class="detail-value">{{ detail.roadNum || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">路线名称:</span>
            <span class="detail-value">{{ detail.routeFullName || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">检测单位:</span>
            <span class="detail-value">{{ detail.detectUnit || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">委托单位:</span>
            <span class="detail-value">{{ detail.delegateUnit || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">行政区划编码:</span>
            <span class="detail-value">{{ detail.adCode || "" }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">巡检开始时间:</span>
            <span class="detail-value">{{
              formatTime(detail.startTime) || ""
            }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">巡检结束时间:</span>
            <span class="detail-value"
              >{{ formatTime(detail.endTime) || "巡检中" }}
            </span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">最新运行时间:</span>
            <span class="detail-value">{{
              formatTime(
                detail.deviceState && detail.deviceState.latestRuntime
              ) || ""
            }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">最新采集时间:</span>
            <span class="detail-value">{{
              formatTime(
                detail.deviceState && detail.deviceState.latestInfertime
              ) || ""
            }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">巡检里程(km):</span>
            <span class="detail-value">{{ detail.inspectMileage || "" }}</span>
          </div>
        </div>
        <div class="long-fields-section">
          <div class="detail-item">
            <span class="detail-label">标定参数:</span>
            <div class="detail-content">
              <pre class="detail-text">{{ detail.area_infer_setting }}</pre>
              <el-button
                v-if="detail.area_infer_setting"
                type="text"
                size="mini"
                @click="handleCopyText(detail.area_infer_setting)"
                class="copy-btn"
                >复制</el-button
              >
            </div>
          </div>

          <div class="detail-item">
            <span class="detail-label">扩展任务信息:</span>
            <div class="detail-content">
              <pre class="detail-text">{{ detail.ext_task_info }}</pre>
              <el-button
                v-if="detail.ext_task_info"
                type="text"
                size="mini"
                @click="handleCopyText(detail.ext_task_info)"
                class="copy-btn"
                >复制</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getInspectionRecordDetail,
} from "@/api/inspection-record";
import { copyText } from "@/utils/clipboard";
import { mergeWith } from "lodash";
export default {
  name: "DetailDialog",
  data() {
    return {
      visible: false,
      loading: false,
      detail: {},
      actionLoading: false,
      recordId: null,
      latestTime: null,
    };
  },
  methods: {
    // 打开弹窗方法
    open(detail) {

      console.log(detail);
      this.recordId = detail.id;
      this.visible = true;
      this.detail = detail
      this.getDetail();
    },

    formatTime(time) {
      if (!time) return null;
      return this.$options.filters.parseTime(
        new Date(time),
        "{yyyy}-{mm}-{dd} {hh}:{ii}"
      );
    },
    async getDetail() {
      if (!this.recordId) return;
      this.loading = true;
      try {
        const { status, payload } = await getInspectionRecordDetail(
          this.recordId
        );
        if (status === 200 && payload) {
          // 智能合并：优先选择有值的字段
          this.detail = mergeWith({}, this.detail, payload, (objValue, srcValue) => {
            // 如果srcValue是null、undefined或空字符串，且objValue有值，则保留objValue
            if ((srcValue === null || srcValue === undefined || srcValue === '') &&
                (objValue !== null && objValue !== undefined && objValue !== '')) {
              return objValue;
            }
            // 否则使用srcValue（包括srcValue有值的情况）
            return srcValue;
          });
        }

        console.log(this.detail);
      } catch (error) {
        console.error("获取详情失败", error);
        this.$message.error("获取详情失败");
      } finally {
        this.loading = false;
      }
    },
    handleClose() {
      this.visible = false;
      this.detail = {};
      this.latestTime = null;
      this.actionLoading = false;
      this.recordId = null;
    },
    handleCopyText(text) {
      copyText(text);
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-info {
  .basic-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 20px;
    margin-bottom: 12px;
  }

  .detail-item {
    display: flex;
    align-items: flex-start;

    .detail-label {
      width: 120px;
      flex-shrink: 0;
      font-weight: bold;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }

    .detail-value {
      flex: 1;
      color: #303133;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-all;
    }

    .detail-content {
      flex: 1;
      position: relative;

      .detail-text {
        background-color: #f8f8f8;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 12px;
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-word;
        font-family: Consolas, Monaco, "Andale Mono", monospace;
        font-size: 14px;
        line-height: 1.5;
        width: 100%;
      }

      .copy-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        padding: 0;
        font-size: 12px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>

