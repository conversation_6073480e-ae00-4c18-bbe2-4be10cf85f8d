/*
 * @Author: wangyj
 * @Date: 2022-11-09 15:27:47
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-11 11:41:56
 */

<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :xl="6" :lg="12" class="video-item">
        <el-card :body-style="{ padding: '0px' }">
          <div class="video-container">
            <video />
            <div>
              <img src="@/assets/play.png">
            </div>
          </div>
          <div class="video-bottom">
            <div class="video-title-cont">
              <div>野生动物监测</div>
              <div>
                <el-button class="green" icon="el-icon-video-play" title="启动检测" circle />
                <el-button class="red" icon="el-icon-video-pause" title="停止检测" circle />
                <el-button icon="el-icon-camera-solid" title="抓拍" circle />
                <el-button icon="el-icon-time" title="历史记录" circle @click="handleHistory" />
                <el-dropdown class="el-dropdown-more">
                  <span class="el-dropdown-link">
                    <el-button icon="el-icon-more" title="更多" circle />
                  </span>
                  <el-dropdown-menu slot="dropdown" class="el-dropdown-menu-more">
                    <el-dropdown-item icon="el-icon-edit">编辑</el-dropdown-item>
                    <el-dropdown-item icon="el-icon-delete">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div class="video-info-cont">
              <div>
                <p>应用检测结果：野生动物</p>
                <p>检测间隔：5秒</p>
                <p>检测状态：<span class="dot" />已停止</p>
              </div>
              <div>
                <p>检测时间：2022-08-14 09:39:57</p>
                <p>报警频度：10秒</p>
                <p>数据源：视频流</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xl="6" :lg="12" class="video-item video-add-item">
        <el-card :body-style="{ padding: '0px' }">
          <div class="add-container" @click="handleCreate"><i class="el-icon-plus" /></div>
        </el-card>
      </el-col>
    </el-row>

    <CreateDialog ref="createDialog" />
    <HistoryDialog ref="historyDialog" />
  </div>
</template>

<script>
import CreateDialog from './components/create-dialog.vue'
import HistoryDialog from './components/history-dialog.vue'

export default {
  components: { CreateDialog, HistoryDialog },
  data() {
    return {
    }
  },

  computed: {},

  created() {},

  mounted() {},

  methods: {
    handleCreate() {
      this.$refs.createDialog.show()
    },
    handleHistory() {
      this.$refs.historyDialog.show()
    },
  },
}

</script>
<style lang='scss' scoped>
.video-item {
    margin-bottom: 30px;
}
.video-add-item {
    .add-container {
      width: 100%;
      height: 0;
      padding-bottom: calc(56.25% + 164px);
      position: relative;
      cursor: pointer;

      &:hover {
        background: #f8fbff;
      }

      i {
        font-size: 30px;
        color: #8c939d;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

}
.video-container {
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    position: relative;
    overflow: hidden;
}
.video-bottom{
   padding: 20px 10px;

   .video-title-cont {
        display: flex;
        justify-content: space-between;
        div {
            flex-shrink: 0;
        }
        ::v-deep.el-button {
            padding: 0;
            border: 0;
            color: #5394eb;

            &:hover, &:active {
                background: #fff;
            }
            &.green {
                color: #67c23a;
            }
            &.red {
                color: #f56c6c;
            }

            i {
                font-size: 18px;
            }
        }

        .el-dropdown-more {
            margin-left: 10px;

            ::v-deep.el-button {
                color: #333;
            }
        }

   }

   .video-info-cont {
        display: flex;
        justify-content: space-between;
        font-size: 14px;

        div:first-child {
            width: 49%;
        }
        div:last-child {
          width: 51%
        }

        .dot {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 10px;
          background-color: #ff4d4f;
          position: relative;

          &:before,
          &:after {
              content: '';
              width: 100%;
              height: 100%;
              position: absolute;
              left: 0;
              top: 0;
              border: 1px solid rgba(255, 77, 79, 0.5);
              border-radius: 50%;
              animation: warn 2s ease-out 0s infinite; //添加动画
            }
        }

        @keyframes warn {
          0% {
            transform: scale(0.5);
            opacity: 1;
          }
          25% {
            transform: scale(1);
            opacity: 0.75;
          }
          50% {
            transform: scale(1.5);
            opacity: 0.5;
          }
          75% {
            transform: scale(2);
            opacity: 0.25;
          }
          100% {
            transform: scale(2.5);
            opacity: 0;
          }
        }
   }
}

.el-dropdown-menu-more {
    .el-dropdown-menu__item {
        color: #5394eb;
    }
}
</style>
