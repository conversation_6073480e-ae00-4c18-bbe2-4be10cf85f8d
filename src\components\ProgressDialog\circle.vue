<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-07 10:46:44
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-07 16:18:41
 * @Description:
-->

<template>
  <el-dialog
    class="progress-dialog"
    :title="text"
    :center="true"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="300px"
  >
    <div class="content">
      <el-progress type="circle" :percentage="percentage" :status="percentage === 100 ? 'success' : null" />
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'CircleProgressDialog',
  props: {
    text: {
      type: String,
      default: '进度',
    },
  },
  data() {
    return {
      visible: false,
      percentage: 0,
    }
  },
  methods: {
    onShow() {
      this.percentage = 0
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
    onUpdatePercentage(value) {
      this.percentage = value
    },
  },
}
</script>

<style lang="scss" scoped>
  .progress-dialog {
    ::v-deep .el-dialog {
      margin-top: 0!important;
      top: 50%;
      transform: translateY(-60%);
    }
    .content {
        display: flex;
        // flex-direction: column;
        justify-content: center;
        align-items: center;
        i {
            font-size: 30px;
        }
        div {
            margin-left: 10px;
        }
    }
  }
</style>
