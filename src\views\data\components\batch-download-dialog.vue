<template>
  <!-- 标注Dialog 开始-->
  <el-dialog
    title="批量下载"
    :visible.sync="dialogVisible"
    width="30%"
    :close-on-click-modal="false"
  >
    <el-checkbox-group v-model="fileTypes" style="margin-bottom: 10px;">
      <el-checkbox v-for="(tag, index) in tags" :key="tag.label" :label="tag.value"><i class="el-icon-document" />{{ tag.label }}</el-checkbox>
    </el-checkbox-group>
    <el-checkbox-group v-model="fileTypes1">
      <el-checkbox v-for="(tag, index) in tags1" :key="tag.label" :label="tag.value"><i class="el-icon-document" />{{ tag.label }}</el-checkbox>
    </el-checkbox-group>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { exportFile } from '@/utils/index'

export default {
  props: {
    selection: { // 是否需要人工标注
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      fileTypes: [],
      fileTypes1: [],
      tags: [
        { label: '病害列表', value: 1 },
        { label: '去重图集', value: 2 },
      ],
      tags1: [
        { label: '病害xml、图集位置', value: 3 },
      ],
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.fileTypes = []
      this.fileTypes1 = []
    },
    handleSubmit() {
      const taskIds = this.selection.map(({ id }) => id)
      const fileTypes = [...this.fileTypes, ...this.fileTypes1]
      // console.log(fileTypes, taskIds)
      if (fileTypes.length > 0) {
        exportFile(`road-statises/dataBatchExport?taskIds=${taskIds}&fileTypes=${fileTypes}`)
      }
      this.handleClose()
    },
  },
}
</script>
