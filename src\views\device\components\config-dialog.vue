<!--*
 * @Author: wangyj
 * @Date: 2022-06-16 16:26:29
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-06-16 17:03:54
 *-->
<template>
  <div>
    <el-dialog title="配置运行策略" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="5vh">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="top"
        label-width="120px"
        style=" max-height: 70vh;overflow-y: auto;"
      >
        <el-form-item class="el-form-item_gpsRang" label="设备运行GPS范围" prop="gpsArea">
          <el-input v-model="temp.gpsArea" disabled placeholder="设备运行GPS范围" />
          <el-button type="success" @click="handleShowMapDialog">地图选择</el-button>
        </el-form-item>
        <el-form-item v-auth="'devices-config-push:view:devices-config-push'" label="推送地址" prop="pushAddress ">
          <el-input v-model="temp.pushAddress" placeholder="推送地址" />
        </el-form-item>
        <el-form-item v-auth="'devices-config-data-model:view:devices-config-data-model'" label="数据推动模式" prop="dataPushMode">
          <el-radio v-model="temp.dataPushMode" :label="1">手动模式</el-radio>
          <el-radio v-model="temp.dataPushMode" :label="2">自动模式</el-radio>
        </el-form-item>
        <el-form-item v-auth="'devices-config-mini-pavement-area:view:devices-config-mini-pavement-area'" label="沥青路面 病害最小推送面积（㎡）">
          <div class="minimum-area-vos">
            <div v-for="areaVos in temp.pitchDamagePushMinimumAreaVos" :key="areaVos.chineseName">
              <span>{{ areaVos.chineseName }}</span>
              <el-input
                v-model.number="areaVos.minimumArea"
                placeholder="面积（>=0）"
                oninput="value=value.replace(/[^.0-9]/g,'')"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item v-auth="'devices-config-mini-pavement-area:view:devices-config-mini-pavement-area'" label="水泥混凝土路面 病害最小推送面积（㎡）">
          <div class="minimum-area-vos">
            <div v-for="areaVos in temp.cementDamagePushMinimumAreaVos" :key="areaVos.chineseName">
              <span>{{ areaVos.chineseName }}</span>
              <el-input
                v-model.number="areaVos.minimumArea"
                placeholder="面积（>=0）"
                oninput="value=value.replace(/[^.0-9]/g,'')"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item v-auth="'devices-config-mini-pavement-area:view:devices-config-mini-pavement-area'" label="道路资产推送列表" prop="roadAssetPushConfigVos">
          <div v-for="vos in temp.roadAssetPushConfigVos" :key="vos.chineseName" style="display: flex;align-items: center;">
            <template v-if="vos.chineseName === '井盖'">
              <span style="padding-right: 10px;">{{ vos.chineseName }} :</span>
              <el-radio-group v-model="vos.needPush">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </template>
          </div>
          <!-- <el-radio v-model="temp.runMode" :label="1">巡检模式</el-radio>
          <el-radio v-model="temp.runMode" :label="2">检测模式</el-radio> -->
        </el-form-item>

        <el-form-item label="设备运行模式" prop="runMode">
          <el-radio v-model="temp.runMode" :label="1">巡检模式</el-radio>
          <el-radio v-model="temp.runMode" :label="2">检测模式</el-radio>
        </el-form-item>
        <el-form-item label="委托单位" prop="delegateUnit">
          <el-input v-model="temp.delegateUnit" placeholder="委托单位" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作时间" prop="time">
              <el-time-picker
                v-model="timeRange"
                is-range
                format="HH:mm"
                value-format="HH:mm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="频度">
              <el-select v-model="temp.deviceWorkTimeOption" style="width: 170px; margin-right: 10px">
                <el-option
                  v-for="item in workTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select v-show="temp.deviceWorkTimeOption == 3" v-model="temp.deviceWorkMonthDay" style="width: 170px; margin-right: 10px">
                <el-option
                  v-for="item in monthDayOptions"
                  :key="item"
                  :label="item + '号'"
                  :value="item"
                >
                  <span style="float: left">{{ item + '号' }}</span>
                  <span v-if="item>28" style="float: right; color: #8492a6; font-size: 12px; transform: scale(0.8)">并非每月都存在</span>
                </el-option>
              </el-select>
              <el-select v-show="temp.deviceWorkTimeOption == 2" v-model="temp.deviceWorkWeekDay" style="width: 170px; margin-right: 10px">
                <el-option
                  v-for="item in weekDayOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="过滤8:00~17:00的巡检数据" prop="filterByTimePeriod">
          <el-radio v-model="temp.filterByTimePeriod" :label="true">是</el-radio>
          <el-radio v-model="temp.filterByTimePeriod" :label="false">否</el-radio>
        </el-form-item>
        <el-form-item label="病害去重有效距离（m）" prop="diseaseRemoveDupDistance">
          <el-input v-model="temp.diseaseRemoveDupDistance" placeholder="有效距离" />
        </el-form-item>
        <el-form-item label="景观图像去重有效距离（m）" prop="landscapeRemoveDupDistance">
          <el-input v-model="temp.landscapeRemoveDupDistance" placeholder="有效距离" />
        </el-form-item>
        <el-form-item label="病害最小统计面积（㎡）">
          <div class="area-container">
            <div><span>横向裂纹</span><el-input v-model="temp.transverseMinimumStatisArea" placeholder="面积（>=0）" oninput="value=value.replace(/[^.0-9]/g,'')" /></div>
            <div><span>纵向裂纹</span><el-input v-model="temp.longitudinalMinimumStatisArea" placeholder="面积（>=0）" oninput="value=value.replace(/[^.0-9]/g,'')" /></div>
            <div><span>龟裂</span><el-input v-model="temp.aligatorMinimumStatisArea" placeholder="面积（>=0）" oninput="value=value.replace(/[^.0-9]/g,'')" /></div>
            <div><span>坑洞</span><el-input v-model="temp.potholeMinimumStatisArea" placeholder="面积（>=0）" oninput="value=value.replace(/[^.0-9]/g,'')" /></div>
          </div>
        </el-form-item>
        <el-form-item label="判断车辆是否偏移路线有效距离（m）" prop="routeDepartJudgeDistance">
          <el-input v-model="temp.routeDepartJudgeDistance" placeholder="有效距离" />
        </el-form-item>
        <el-form-item label="判断巡检任务是否结束设备持续未响应时长（min）" prop="workEndJudgeDuration">
          <el-input v-model="temp.workEndJudgeDuration" placeholder="响应时长" />
        </el-form-item>
        <el-form-item label="判断设备是否工作设备持续未响应时长（s）" prop="deviceOnlineJudgeDuration">
          <el-input v-model="temp.deviceOnlineJudgeDuration" placeholder="响应时长" />
        </el-form-item>
        <el-form-item label="数据存储周期（天）" prop="dataClearDay">
          <el-input v-model="temp.dataClearDay" placeholder="数据存储周期" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateConfig">
          确定
        </el-button>
      </div>
    </el-dialog>
    <MapDialog ref="mapDialog" @setGpsArea="handleSetGpsArea" />
  </div>
</template>

<script>
import { getDeviceConfig, updateDeviceConfig } from '@/api/device'
import { mapState } from 'vuex'
import MapDialog from './map-dialog.vue'
export default {
  components: { MapDialog },
  data() {
    return {
      deviceId: null,
      defaultTemp: {
        deviceWorkTimeOption: '',
        deviceWorkMonthDay: 1,
        deviceWorkWeekDay: '星期一',
        filterByTimePeriod: false,
      },
      temp: {},
      dialogFormVisible: false,
      rules: {
        runMode: [{ required: true, message: '请选择运行模式', trigger: 'change' }],
        // delegateUnit: [{ required: true, message: '请输入委托单位', trigger: 'blur' }],
        filterByTimePeriod: [{ required: true, message: '请选择', trigger: 'blur' }],
        diseaseRemoveDupDistance: [{
          required: true, message: '请输入病害去重有效距离', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value < 1) {
              callback(new Error('范围在1-20'))
            } else if (value > 20) {
              callback(new Error('范围在1-20'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        landscapeRemoveDupDistance: [{
          required: true, message: '请输入景观图像去重有效距离', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value <= 0) {
              callback(new Error('范围大于0'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        damageMinimumStatisArea: [{
          validator: (rule, value, callback) => {
            if (value === '' || value === null) {
              callback()
            } else if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value <= 0) {
              callback(new Error('面积值大于0'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        routeDepartJudgeDistance: [{
          required: true, message: '请输入判断车辆是否偏移路线有效距离', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value < 5) {
              callback(new Error('范围在5-50'))
            } else if (value > 50) {
              callback(new Error('范围在5-50'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        workEndJudgeDuration: [{
          required: true, message: '请输入判断巡检任务是否结束设备持续未响应时长', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value < 1) {
              callback(new Error('范围在1-120'))
            } else if (value > 120) {
              callback(new Error('范围在1-120'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        dataClearDay: [{
          required: true, message: '请输入数据存储周期', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value < 1) {
              callback(new Error('范围在1-180'))
            } else if (value > 180) {
              callback(new Error('范围在1-180'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
        deviceOnlineJudgeDuration: [{
          required: true, message: '请输入判断设备是否工作设备持续未响应时长', trigger: 'blur',
        }, {
          validator: (rule, value, callback) => {
            if (!/^\d+$|^\d+[.]?\d+$/.test(value)) {
              callback(new Error('请输入数字值'))
            } else if (value <= 0) {
              callback(new Error('范围在大于0，小于等于300'))
            } else if (value > 300) {
              callback(new Error('范围在大于0，小于等于300'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        }],
      },
      workTypeOptions: [{
        value: 1,
        label: '每天',
      }, {
        value: 2,
        label: '每周',
      }, {
        value: 3,
        label: '每月',
      }],
      monthDay: '',
      weekDay: '',
      weekDayOptions: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      timeRange: null,
    }
  },
  computed: {
    monthDayOptions() {
      const arr = new Array(31)
      for (let i = 0; i < arr.length; i += 1) {
        arr[i] = i + 1
      }
      return arr
    },
    ...mapState({
      workUnit: (state) => state.account.workUnit,
    }),
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.getConfig()
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      } else {
        this.timeRange = null
      }
    },
  },
  methods: {
    getConfig() {
      getDeviceConfig(this.deviceId).then((res) => {
        if (this.$auth('devices-config-mini-pavement-area:view:devices-config-mini-pavement-area')) {
          const pitchDamagePushMinimumAreaVos = []; const
            cementDamagePushMinimumAreaVos = []
          res.payload.damagePushMinimumAreaVos.forEach((item) => {
            if (item.roadType === 1) pitchDamagePushMinimumAreaVos.push(item) // 沥青路面
            else cementDamagePushMinimumAreaVos.push(item) // 水泥混凝土
          })
          res.payload.pitchDamagePushMinimumAreaVos = pitchDamagePushMinimumAreaVos
          res.payload.cementDamagePushMinimumAreaVos = cementDamagePushMinimumAreaVos
        }
        this.temp = res.payload
        if (this.temp.deviceWorkTimeOption === 1) {
          this.timeRange = this.temp.deviceWorkDaliyTime ? this.temp.deviceWorkDaliyTime.split('-') : null
        } else if (this.temp.deviceWorkTimeOption === 2) {
          this.timeRange = this.temp.deviceWorkWeekDaytime ? this.temp.deviceWorkWeekDaytime.split('-') : null
        } else if (this.temp.deviceWorkTimeOption === 3) {
          this.timeRange = this.temp.deviceWorkMonthDaytime ? this.temp.deviceWorkMonthDaytime.split('-') : null
        } else if (this.temp.deviceWorkTimeOption === 0) {
          this.temp.deviceWorkTimeOption = ''
        }
      })
    },
    fetchPostData() {
      let {
        deviceWorkTimeOption, diseaseRemoveDupDistance, routeDepartJudgeDistance, workEndJudgeDuration, dataClearDay, damageMinimumStatisArea, delegateUnit, runMode, transverseMinimumStatisArea, longitudinalMinimumStatisArea, aligatorMinimumStatisArea, potholeMinimumStatisArea, deviceOnlineJudgeDuration, filterByTimePeriod, gpsArea, landscapeRemoveDupDistance, dataPushMode, pushAddress, damagePushMinimumAreaVos, roadAssetPushConfigVos,
      } = this.temp

      if (this.$auth('devices-config-mini-pavement-area:view:devices-config-mini-pavement-area')) {
        damagePushMinimumAreaVos = [...this.temp.pitchDamagePushMinimumAreaVos, ...this.temp.cementDamagePushMinimumAreaVos]
      }

      console.log(roadAssetPushConfigVos[0].needPush)
      const postData = {
        deviceWorkTimeOption, diseaseRemoveDupDistance, routeDepartJudgeDistance, workEndJudgeDuration, dataClearDay, damageMinimumStatisArea, delegateUnit, runMode, transverseMinimumStatisArea, longitudinalMinimumStatisArea, aligatorMinimumStatisArea, potholeMinimumStatisArea, deviceOnlineJudgeDuration, filterByTimePeriod, gpsArea, landscapeRemoveDupDistance, dataPushMode, pushAddress, damagePushMinimumAreaVos, roadAssetPushConfigVos,
      }

      if (this.temp.deviceWorkTimeOption === 1) {
        if (this.timeRange) {
          postData.deviceWorkDaliyTime = this.timeRange.join('-')
        }
      } else if (this.temp.deviceWorkTimeOption === 2) {
        if (this.timeRange) {
          postData.deviceWorkWeekDaytime = this.timeRange.join('-')
        }
        postData.deviceWorkWeekDay = this.temp.deviceWorkWeekDay
      } else if (this.temp.deviceWorkTimeOption === 3) {
        if (this.timeRange) {
          postData.deviceWorkMonthDaytime = this.timeRange.join('-')
        }
        postData.deviceWorkMonthDay = this.temp.deviceWorkMonthDay
      } else if (this.temp.deviceWorkTimeOption === '') {
        postData.deviceWorkTimeOption = 0
      }

      postData.id = this.deviceId

      return postData
    },
    updateConfig() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const postData = this.fetchPostData()

          console.log(postData)
          updateDeviceConfig(postData).then((res) => {
            this.$message({
              message: '修改设备参数成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show(deviceId) {
      this.deviceId = deviceId
      this.dialogFormVisible = true
    },
    handleShowMapDialog() {
      const points = this.temp.gpsArea ? JSON.parse(this.temp.gpsArea) : []
      this.$refs.mapDialog.show(points)
    },
    handleSetGpsArea(gpsArr) {
      this.$set(
        this.temp,
        'gpsArea',
        gpsArr.length > 0 ? JSON.stringify(gpsArr) : null,
      )
    },
  },
}
</script>
<style lang="scss" scoped>
  .el-form-item_gpsRang {
    .el-input {
      width: 89%;
    }
  }
  .area-container {
    display: flex;
    &>div {
      width: 25% !important;
      display: flex;
      justify-content: space-between;
      margin-right: 10px;
      &:last-child {
        margin-right: 0;
      }
      span {
        display: inline-block;
        flex-shrink: 0;
        text-align: right;
        padding-right: 10px;
      }
    }
  }
  .minimum-area-vos {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    &>div {
      flex: 0 0 calc(25% - (3 * 12px / 4));
      display: flex;
      margin-right: 12px;
      margin-bottom: 10px;
      &:nth-child(4n) {
        margin-right: 0px;
      }
      span {
        width: 45%;
      }
      .el-input {
        width: 55%;
      }
      &:nth-last-child(-n+4) {
        margin-bottom: 0px;
      }
    }
  }
</style>
