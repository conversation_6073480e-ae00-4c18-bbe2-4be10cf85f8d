/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by:   wangyj
 * @Last Modified time: 2022-10-25 18:02:28
 */

<template>
  <el-dialog
    width="35%"
    :title="dialogType==='edit'?'修改账号':'添加账号'"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    @close="handelClose"
  >
    <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
      <el-form-item label="单位名称" prop="workUnitId" class="workUnitId">
        <UnitSelect v-model="temp.workUnitId" useNodeId placeholder="请选择所属单位"/>
      </el-form-item>
      <el-form-item label="账号" prop="username">
        <el-input v-model="temp.username" placeholder="请输入账号" />
      </el-form-item>
      <el-form-item label="姓名" prop="realName">
        <el-input v-model="temp.realName" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="temp.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item v-if="dialogType !== 'edit'" label="密码" prop="password">
        <el-input v-model="temp.password" show-password placeholder="请输入密码，用于系统登录" />
      </el-form-item>
      <el-form-item v-else label="新密码" prop="newPassword">
        <el-input v-model="temp.password" show-password placeholder="请输入密码，用于系统登录" @change="handlePwdChange" />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="temp.confirmPassword" show-password placeholder="请输入确认密码" />
      </el-form-item>
      <el-form-item label="角色名称" prop="roleId">
        <el-select
          v-model="temp.roleId"
          placeholder="请选择角色名称"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="允许图集下载" prop="allowDownloadPhoto">
        <el-radio v-model="temp.allowDownloadPhoto" :label="true">是</el-radio>
        <el-radio v-model="temp.allowDownloadPhoto" :label="false">否</el-radio>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createUser, updateUser } from '@/api/user'
import { getRoles } from '@/api/auth'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    const validateConfirmPwd = (rule, value, callback) => {
      if (value !== this.temp.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        workUnitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }],
        userType: [{ required: true, message: '请选择权限', trigger: 'blur' }],
        allowDownloadPhoto: [{ required: true, message: '请选择是否允许图集下载', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPwd, trigger: 'blur' },
        ],
        roleId: [{ required: true, message: '请选择角色名称', trigger: 'change' }],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ],
      },
      roleList: [],
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        if (this.formData.role) {
          this.$set(this.temp, 'roleId', this.formData.role.id)
        }
        if (this.formData.id) {
          this.dialogType = 'edit'
          this.rules.confirmPassword[0].required = false
        } else {
          this.$set(this.temp, 'userType', 1)
          this.$set(this.temp, 'allowDownloadPhoto', false)
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp, ...{ admin: this.temp.userType === 3 } }
          delete tempData.confirmPassword

          createUser(tempData).then((res) => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp, ...{ admin: this.temp.userType === 3 } }
          delete tempData.confirmPassword

          updateUser(tempData).then(() => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.getRoleList()
      this.dialogFormVisible = true
    },
    getRoleList() {
      getRoles().then(({ payload }) => {
        this.roleList = payload
      })
    },
    handelClose() {
      console.log('handelclose')
      this.dialogFormVisible = false
    },
    handlePwdChange(value) {
      this.$refs.dataForm.clearValidate('confirmPassword')
      this.$set(this.temp, 'confirmPassword', '')
      if (value !== '') {
        this.rules.confirmPassword[0].required = true
      } else {
        this.rules.confirmPassword[0].required = false
      }
    },
  },
}
</script>

<style scoped lang="scss">
.mr_10 {
  margin-right: 10px;
}
.area {
  width: 180px;
}
::v-deep .el-select--medium {
  width: 100%;
}

.workUnitId {
  ::v-deep .el-form-item__content {
    line-height: 0 !important;
  }
}
</style>
