<!--
 * @Author: guowy
 * @Date: 2020-11-17 16:06:30
 * @LastEditors: guowy
 * @LastEditTime: 2020-11-17 16:19:48
-->
<template>
  <div class="robu-badge" />
</template>

<script>
export default {
  name: '<PERSON><PERSON>B<PERSON><PERSON>',
}
</script>

<style lang="scss" scoped>
  .robu-badge{
    width: 6px;
    height: 6px;
    display: inline-block;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 5px;
    background-color:#e6ebf1;
    position: relative;
    top: -1px;
    &::after{
      position: absolute;
      top: 0;
      left: 0;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border:1px solid #e6ebf1;
      content: '';
      animation: aniStatusProcessing 1.2s ease-in-out infinite;
    }
    &.primary{
      background-color:#2d8cf0;
      &::after{
        border:1px solid #2d8cf0;
      }
    }
    &.danger{
      background-color: #ed4014;
      &::after{
        border:1px solid #ed4014;
      }
    }
    &.success{
      background-color: #19be6b;
      &::after{
        border:1px solid #19be6b;
      }
    }
  }
  @keyframes aniStatusProcessing {
    0% {
        transform:scale(.8);
        opacity:.5
    }
    to {
        transform:scale(2.4);
        opacity:0
    }
}
</style>
