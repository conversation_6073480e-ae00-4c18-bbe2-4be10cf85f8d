<!--
 * @Author: guowy
 * @Date: 2020-09-01 17:31:57
 * @LastEditors: guowy
 * @LastEditTime: 2021-04-21 17:38:32
-->
# 公司npm私有库功能介绍

## @robu/permission

### 使用示例

#### 模块引入
main.js添加以下代码
```
import robuPermission from '@robu/permission'
Vue.use(robuPermission)
```
#### 组件使用
```
/**
* 权限列表组件
* @param {String} tags(非必传) 标签数组参数,多个参数用分号分隔
*/
<permission-table :tags="tags" /> 

/**
* 角色模块（包括角色列表，角色增删改，分配权限）
* @param {String} tags(非必传) 标签数组参数,多个参数用分号分隔
*/
<role-table :tags="tags" />


/**
  * 分配角色组件（多选框）
  * @param {Number} userId 用户id(修改用户角色时使用)
  * @param {String} tags(非必传) 标签数组参数,多个参数用分号分隔
*/
<role-checkbox-list ref="roleList" :tags="tags" :userId="userId" />
// 内置方法
  getCheckRoles() 获取选中的角色id数组（这个方法目前可能用不到）
  assignRoles(userId) 分配角色
// 内置方法使用
  this.$refs.roleList.assignRoles(userId)//分配角色
  
  /**
  * 分配角色组件（单选框）
  * @param {Number} userId 用户id(修改用户角色时使用)
  * @param {String} tags(非必传) 标签数组参数,多个参数用分号分隔
*/
  <role-radio-list ref="roleList" :tags="tags" :userId="userId" />
  // 内置方法
  getCheckRoles() 内置方法，获取选中的角色id数组（这个方法目前可能用不到）
  assignRoles(userId) 内置方法，分配角色
  // 内置方法使用
  this.$refs.roleList.assignRoles(userId)//分配角色

 
/**
  * 权限分配表格（多选框选择方式）
  * @param {Number} roleId 角色id
*/
<permission-assign :roleId="roleId" />

/**
  * 权限分配（穿梭框方式）
  * @param {Number} roleId 角色id
*/
<permission-assign-transfer :roleId="roleId" />
```
#### 接口使用
```
/**
 * 获取所有权限
 * @param {String} tags 标签参数,多个参数用分号分隔
 */
this.$permission.getPerms(tags)

/**
 * 获取所有角色
 * @param {String} tags 标签参数,多个参数用分号分隔
 */
this.$role.getRoles(tags)

/**
 * 获取某个角色
 * @param {*} id 角色id
 */
this.$role.getRole(id)

/**
 * 添加角色
 * @param {*} data 角色数据
 */
this.$role.addRole(data)

/**
 * 修改角色
 * @param {*} id 角色id
 * @param {*} data 角色数据
 */
this.$role.updateRole(id, data)

/**
 * 删除角色
 * @param {*} id 角色id
 */
this.$role.deleteRole(id)

/**
 * 获取某个角色的权限
 * @param {*} roleId 角色id
 */
this.$role.getRolePerms(roleId)

/**
 * 分配权限
 * @param {*} roleId 角色id
 * @param {*} data 角色name数组
 */
this.$role.assignRolePerms(roleId, data)

/**
 * 删除角色权限
 * @param {*} roleId 角色id
 * @param {*} data 角色name数组
 */
this.$role.unassignRolePerms(roleId, data)

/**
 * 获取用户的角色
 * @param {*} userId 用户id
 */
this.$role.getUserRoles(userId)

/**
 * 分配用户角色
 * @param {*} userId 用户id
 * @param {*} ids 新分配的角色数组
 */
this.$role.dissociateRole(userId, ids)

/**
 * 删除用户角色
 * @param {*} userId 用户id
 * @param {*} ids 用户角色is数组
 */
this.$role.associateRole(userId, ids)

/**
 * 删除用户角色
 * @param {*} userId 用户id
 * @param {*} ids 用户选择的角色id数组（不用区分新增和删除）
 */
this.$role.associateRoles(userId, ids)

```

## @robu/request

base url需要在环境变量文件中设置VUE_APP_BASE_API
### 使用示例
```
import store from '@/store'
import $axios from '@robu/request'
import router from '@/router'

const options = {
  timeout: 5000, // 请求超时设置，默认5000
  beforeSend: () => {
    // 请求发送前
  },
  toLogin: () => { 
    store.dispatch('user/resetToken').then(() => {
      router.push({ path: '/login' })
    })
  },
}

export default function request(_option) {
  return new Promise((resolve, reject) => {
    $axios(options)(_option).then((res) => {
      resolve(res)
    }).catch((err) => {
      reject(err)
    })
  })
}
```

## @robu/validator

## @robu/user
#### 模块引入
main.js引入
```
import robuUser from '@robu/user'
import store from './store'
import * as routerJs from './router'
Vue.use(robuUser, { store, routerJs })
```
#### 组件使用
登录
```
<login />
```

修改当前用户密码
```
<change-password />
```
#### 内置方法
```
//获取token
this.$user.getToken()
//删除token
this.$user.removeToken()
```

#### 接口使用
```
/*
* 登录
* principal 用户名
* password 密码
*/
this.$user.api.login({ principal, password })

/*
* 手机登录
* principal 手机号
* code 验证码
*/
this.$user.api.mobileLogin({ principal, code })

/*
* 发送验证码
* mobile手机号
*/
this.$user.api.sendCode({mobile:'111234283'})

//退出
this.$user.api.logout()

/**
* 修改密码
* oldPass 旧密码
* newPass 新密码
*/
this.$user.api.resetPassword({ oldPass, newPass })

/*
* 刷新token
* refreshToken 登录时后台返回的refreshToken
*/
const token = getToken()
this.$user.api.refreshToken({ refreshToken: token })

//获取当前用户信息
this.$user.api.getInfo()

```



## @robu/formatdate
```
/**
 * 时间转换成字符串
 * @param {(Object|string|number)} time
 * @param {string} cFormat 默认{y}-{m}-{d} {h}:{i}:{s}
 * @returns {string | null}
 */
 parseTime(time, cFormat)

 /**
 * 毫秒转换友好的显示格式（刚刚，几分钟前，几小时前，1天前）
 * @param {number} time
 * @param {string} option 时间格式化类型，选填，如果填写功能同parseTime。示例：{y}-{m}-{d} {h}:{i}:{s}
 * @returns {string}
 */
 formatTime(time, option)

 /**
 * 获取几天几前的时间戳
 * @param {number} index 天
 */
 getFewDaysAgo(index)
```



## @robu/menu-show

### 使用示例

#### 模块引入
  main.js添加以下代码
  ```
  import Layout from '@/layout/index.vue'
  import store from './store'
  import * as routerJs from './router'
  import robuMenuShow from '@robu/menu-show'
  Vue.use(robuMenuShow, { store, routerJs, Layout })
  ```
  setting.js文件修改
  ```
  menuTheme: '', // 默认为空是深色主题，light浅色主题（蓝色），light-green浅色主题（绿色）
  ```
#### 组件使用
src/layout/index.vue
```
<sidebar class="sidebar-container" />
```

## @robu/tagsview
main.js引入
```
import store from './store'
import robuTagsView from '@robu/tagsview'
Vue.use(robuTagsView, { store })
```
setting.js文件
`tagsView: true`

#### 使用
`<tags-view />`