<template>
  <div>
    <div
      class="select-cont-outer"
      :class="{ 'select-cont-outer-fold': !panelShow }"
    >
      <el-select
        v-model.number="curMilVal"
        placeholder="请选择"
        @change="handleMilChange"
      >
        <el-option
          v-for="item in milArr"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-radio-group
      v-model="layerType"
      class="layer-radio"
      :class="{ 'layer-radio-fold': !panelShow }"
      size="small"
      @change="handleLayerTypeChange"
    >
      <el-radio-button label="路况" />
      <el-radio-button label="病害" />
      <el-radio-button v-if="taskId === 603" label="绿视率" />
    </el-radio-group>
    <div v-show="!panelShow" class="unfold-btn" @click="handleUnfold">
      <i class="el-icon-d-arrow-left" />
    </div>
    <div v-show="panelShow" class="panel">
      <div class="panel-head">
        <span class="folding-btn" @click="handleFold">
          <i class="el-icon-d-arrow-right" />
        </span>
        <!-- <el-button type="text" class="report-btn" @click="handleMakeReport">生成报告</el-button> -->
      </div>
      <el-tabs
        v-model="tabActive"
        @tab-click="handleTabClick"
        :before-leave="handleBeforeLeave"
      >
        <el-tab-pane label="巡检一览" name="3">
          <el-card class="common-card">
            <div class="common-card-top">
              <div class="title">
                <i class="el-icon-discount" />
                <span v-if="taskData">{{ taskData.deviceName }}巡检历史</span>
              </div>
            </div>
            <el-table
              v-loading="inspectionLoading"
              size="mini"
              :data="inspectionListData"
              border
              :max-height="tableHeightLong"
              :row-class-name="inspectionTableRowClassName"
              style="width: 100%"
            >
              <el-table-column align="center" label="巡检开始时间">
                <template slot-scope="{ row }">{{
                  new Date(row.startTime)
                    | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
                }}</template>
              </el-table-column>
              <el-table-column align="center" label="巡检结束时间">
                <template slot-scope="{ row }">
                  <template v-if="row.hasEnd">{{
                    new Date(row.endTime)
                      | parseTime("{yyyy}-{mm}-{dd} {hh}:{ii}")
                  }}</template>
                  <template v-else>巡检中</template>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="巡检里程(km)"
                prop="inspectMileage"
              />
              <el-table-column align="center" label="操作" width="70">
                <template slot-scope="{ row, $index }">
                  <el-button
                    type="text"
                    :disabled="taskId === row.id"
                    @click="handleDetail(row, $index)"
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              v-if="inspectionTotal > 0"
              :total="inspectionTotal"
              :page.sync="inspectionQuery.currentPage"
              :limit.sync="inspectionQuery.pageSize"
              :pager-count="5"
              layout="total, prev, pager, next"
              @pagination="onInspectionPagination"
            />
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="病害一览" name="1">
          <el-card class="common-card">
            <div class="common-card-top">
              <div class="title">
                <i class="el-icon-discount" />
                <span>{{ listTitle }}</span>
              </div>
              <div class="select-cont">
                <!-- <el-button round @click="handleExport">导出</el-button> -->
              </div>
            </div>
            <el-form :inline="true" size="small">
              <el-form-item label class="my-form-item">
                <el-select
                  v-model="query.type"
                  placeholder="请选择病害类型"
                  style="width: 96px"
                  clearable
                  @clear="handleClear"
                >
                  <el-option label="全部" value="all" />
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.engName"
                    :label="item.chineseName"
                    :value="item.engName"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label class="my-form-item">
                <el-input
                  v-model="query.area"
                  :disabled="modelIdentifyType !== 1"
                  type="number"
                  style="width: 130px"
                >
                  <template slot="prepend">{{ filterTxt }}</template>
                  <template slot="append">{{ filterUnit }}</template>
                </el-input>
              </el-form-item>
              <el-form-item class="my-form-item">
                <el-button type="primary" @click="handleSearch">检索</el-button>
                <!-- <el-button @click="handleExport">导出</el-button> -->
              </el-form-item>
              <el-form-item class="my-form-item">
                <el-radio-group
                  v-model="tableType"
                  @change="handleChangeTableType"
                >
                  <el-radio-button label="1">按类型</el-radio-button>
                  <el-radio-button label="2">按距离</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="my-form-item">
                <el-dropdown @command="handleCommand">
                  <el-button
                    type="primary"
                    style="padding-bottom: 8px !important"
                  >
                    {{ commandTxt }}
                    <i class="el-icon-arrow-down el-icon--right" />
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-if="taskData && taskData.roadType === 9"
                      command="慢行病害"
                      >慢行病害</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="taskData && taskData.roadType === 9"
                      command="路面病害"
                      >路面病害</el-dropdown-item
                    >
                    <el-dropdown-item v-else command="病害一览"
                      >病害一览</el-dropdown-item
                    >

                    <template v-if="taskData && taskData.roadType !== 10">
                      <!-- "城市管理问题"隐藏 -->
                      <el-dropdown-item command="道路资产"
                        >道路资产</el-dropdown-item
                      >
                      <el-dropdown-item command="路面风险"
                        >路面风险</el-dropdown-item
                      >
                      <el-dropdown-item command="沿线设施损坏"
                        >沿线设施损坏</el-dropdown-item
                      >
                    </template>
                    <template v-if="taskData && taskData.roadType === 1">
                      <el-dropdown-item command="慢行病害"
                        >慢行病害</el-dropdown-item
                      >
                      <el-dropdown-item command="城市管理问题"
                        >城市管理问题</el-dropdown-item
                      >
                    </template>
                    <el-dropdown-item
                      v-if="
                        (taskData && taskData.roadType === 10) ||
                        taskData.roadType === 9
                      "
                      command="城市管理问题"
                      >城市管理问题</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-button type="primary" @click="handleSwitchover">{{ modelIdentifyType === 1 ? '道路资产' : '病害一览' }}</el-button> -->
              </el-form-item>
            </el-form>
            <el-table
              v-loading="diseaseLoading"
              size="mini"
              :data="diseaseListData"
              border
              :max-height="tableHeight"
              :row-class-name="diseaseTableRowClassName"
              style="width: 100%"
              @row-click="handleRowClick"
              @sort-change="handleSortChange"
            >
              <el-table-column
                label="序号"
                min-width="6%"
                header-align="center"
                align="center"
              >
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>
              <el-table-column
                prop="distance"
                label="距离(m)"
                min-width="15%"
                sortable="custom"
                show-overflow-tooltip
                header-align="center"
                align="center"
              />
              <el-table-column
                label="桩号"
                min-width="16%"
                show-overflow-tooltip
                header-align="center"
                align="center"
              >
                <template slot-scope="{ row }">
                  <!-- <template v-if="taskData.detectDirection === 1 && row.pileNum !== null">K{{ Math.floor(row.pileNum) }}+{{ row.pileNum.toFixed(3).toString().split('.')[1] }}</template>
                  <template v-else-if="taskData.detectDirection === 2 && row.pileNum !== null">K{{ Math.ceil(row.pileNum) }}-{{ (Math.ceil(row.pileNum) - row.pileNum).toFixed(3).toString().split('.')[1] }}</template>-->
                  <template v-if="row.pileNum !== null"
                    >K{{ Math.floor(row.pileNum) }}+{{
                      row.pileNum.toFixed(3).toString().split(".")[1]
                    }}</template
                  >
                  <template v-else />
                </template>
              </el-table-column>
              <!-- qp需求开始 -->
              <el-table-column
                label="类型"
                min-width="16%"
                show-overflow-tooltip
                header-align="center"
                align="center"
              >
                <template slot-scope="scope">
                  <template v-if="tableType === '1'">{{
                    getDiseaseType(scope.row.type)
                  }}</template>
                  <template v-if="tableType === '2'">
                    <span v-for="(damage, i) in scope.row.damages" :key="i"
                      >{{ i == 0 ? "" : ", "
                      }}{{ getDiseaseType(damage.type) }}</span
                    >
                  </template>
                </template>
              </el-table-column>
              <el-table-column
                :label="modelIdentifyType === 1 ? '面积(㎡)' : '数量(处)'"
                min-width="15%"
                prop="area"
                :sortable="
                  tableType === '1' && modelIdentifyType === 1
                    ? 'custom'
                    : false
                "
                show-overflow-tooltip
                header-align="center"
                align="center"
              >
                <template slot-scope="scope">
                  <!-- 病害一览 -->
                  <template v-if="modelIdentifyType === 1">
                    <!-- 按类型 -->
                    <template v-if="tableType === '1'">{{
                      scope.row.area
                    }}</template>
                    <!-- 按距离 -->
                    <template v-if="tableType === '2'">
                      <span v-for="(damage, i) in scope.row.damages" :key="i"
                        >{{ i == 0 ? "" : ", " }}{{ damage.area }}</span
                      >
                    </template>
                  </template>
                  <!-- 其他 -->
                  <template v-else>
                    <!-- 按类型没有damageCount，默认是1 -->
                    {{ tableType === "2" ? scope.row.damageCount || "" : 1 }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column
                v-if="
                  tableType === '1' &&
                  $auth(
                    'inspect-tasks-detail-check-push:view:inspect-tasks-detail-check-push'
                  )
                "
                label="状态"
                min-width="15%"
                show-overflow-tooltip
                header-align="center"
                align="center"
              >
                <template slot-scope="scope">{{
                  scope.row.patrolDataSyncResult ? "已推送" : "未推送"
                }}</template>
              </el-table-column>
              <!-- qp需求结束 -->
              <!-- <el-table-column
                prop="gpsLongitude"
                label="经度"
                min-width="14%"
              >
                <template slot-scope="scope">
                  <el-tooltip placement="top">
                    <div slot="content">{{ scope.row.gpsLongitude }}</div>
                    <span>{{ scope.row.gpsLongitude.toFixed(3) }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>-->
              <!-- <el-table-column
                prop="gpsLatitude"
                label="纬度"
                min-width="13%"
              >
                <template slot-scope="scope">
                  <el-tooltip placement="top">
                    <div slot="content">{{ scope.row.gpsLatitude }}</div>
                    <span>{{ scope.row.gpsLatitude.toFixed(3) }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>-->

              <el-table-column
                label="操作"
                min-width="10%"
                header-align="center"
                align="center"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="handleDeal(scope.row, scope.$index)"
                    >核查</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              v-if="total > 0"
              :total="total"
              :page.sync="query.currentPage"
              :limit.sync="query.pageSize"
              :pager-count="5"
              layout="total, prev, pager, next"
              @pagination="onPagination"
            />
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="统计分析" name="2">
          <el-card class="common-card">
            <div class="common-card-top">
              <div class="title">
                <i class="el-icon-discount" />
                路面病害构成
              </div>
              <div class="select-cont">
                <!-- <el-button round @click="handleExportStatistic">导出</el-button> -->
                <!-- <el-select v-model.number="curMilVal" placeholder="请选择" @change="handleMilChange">
                  <el-option v-for="item in milArr" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>-->
              </div>
            </div>
            <div v-if="taskData" class="statistics-cont">
              <div>
                <span>路线编码：{{ taskData.roadCode }}</span>
                <span>行政区划：{{ taskData.adCode }}</span>
                <span />
              </div>
              <div>
                <span
                  v-for="damage in taskData.damageCountList"
                  :key="damage.type"
                  >{{ getDiseaseType(damage.type) }}：{{
                    damage.totalAmount
                  }}</span
                >
                <!-- <span>横向裂纹：{{ taskData.totalTransverseAmount }}</span>
                <span>纵向裂纹：{{ taskData.totalLongitudinalAmount }}</span>
                <span>龟裂：{{ taskData.totalAligatorAmount }}</span>
                <span>坑洞：{{ taskData.totalPotholeAmount }}</span>
                <span>块状裂缝：{{ taskData.totalBlockCracksAmount }}</span>
                <span>块状裂缝：{{ taskData.totalBlockCracksAmount }}</span>-->
              </div>
            </div>
            <ul class="chart_tab">
              <li
                v-for="item in radioData"
                :key="item"
                :class="{ active: radioVal == item }"
                @click="handleRadioTypeChange(item)"
              >
                <span>{{ item.split(" ")[0] }}</span>
                <span>{{ item.split(" ")[1] }}</span>
              </li>
            </ul>
            <div v-if="radioVal == 'PCI 路面损坏状况指数'" class="chart-radio">
              <el-radio-group
                v-model="crackleTypeVal"
                @change="handleCrackleTypeChange"
              >
                <el-radio
                  v-for="(item, index) in crackleTypeArr"
                  :key="index"
                  :label="item.value"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </div>
            <div class="chart-cont">
              <Echarts
                v-if="tabActive == 2"
                id="supervisor"
                :data="lineObj"
                style="width: 100%; height: 260px"
                @echartsClick="handleEchartsClick"
              />
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="病害回放" name="4">
          <el-card class="common-card">
            <div class="common-card-top">
              <div class="title">
                <i class="el-icon-discount" />
                <span>路段实况</span>
              </div>
            </div>
            <Video
              ref="videoRef"
              :page-query="videoPageQuery"
              :taskData="taskData"
              speed="5"
              @updateCar="handleUpdateCar"
              @updatePage="handleUpdatePageQuery"
            />
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="数据下载" name="5">
          <el-card class="common-card">
            <!-- <el-checkbox-group v-model="downloadCheckList" class="download-check-group">
              <el-checkbox :label="6">汇总报告</el-checkbox>
              <el-checkbox :label="1">检测报告</el-checkbox>
              <el-checkbox :label="2">检测原始数据</el-checkbox>
              <el-checkbox :label="3">病害列表</el-checkbox>
              <el-checkbox :label="4">指数统计</el-checkbox>
              <el-checkbox :label="5" :disabled="!allowDownloadPhoto" :title="allowDownloadPhoto ? '' : '无道路图集下载权限'">道路图集</el-checkbox>
            </el-checkbox-group>
            <el-button style="margin-top: 5px" @click="handleBatchExport">生成并下载</el-button>-->
            <div class="report-row">
              <span> <i class="el-icon-document" /> 汇总报告 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(6)"
                >下载</el-button
              >
            </div>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 检测报告 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(1)"
                >下载</el-button
              >
            </div>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 检测原始数据 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(2)"
                >下载</el-button
              >
            </div>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 病害列表 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(3)"
                >下载</el-button
              >
            </div>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 指数统计 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(4)"
                >下载</el-button
              >
            </div>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 道路图集 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                :disabled="!allowDownloadPhoto"
                :title="allowDownloadPhoto ? '' : '无道路图集下载权限'"
                @click="handleSingleDownload(5)"
                >下载</el-button
              >
            </div>
            <template v-if="taskData">
              <div
                v-auths="[
                  '*:*:*',
                  'inspect-tasks-detail-download-detect:view:inspect-tasks-detail-download-detect',
                ]"
                class="report-row"
              >
                <span> <i class="el-icon-document" /> 自动化检测数据 </span>
                <el-button
                  size="small"
                  icon="el-icon-download"
                  @click="handleSingleDownload(7)"
                  >下载</el-button
                >
              </div>
              <div
                v-auths="[
                  '*:*:*',
                  'inspect-tasks-detail-download-landscape:view:inspect-tasks-detail-download-landscape',
                ]"
                class="report-row"
              >
                <span> <i class="el-icon-document" /> 图像类数据 </span>
                <el-button
                  size="small"
                  icon="el-icon-download"
                  @click="handleSingleDownload(8)"
                  >下载</el-button
                >
              </div>
            </template>
            <div class="report-row">
              <span> <i class="el-icon-document" /> 日常巡查记录 </span>
              <el-button
                size="small"
                icon="el-icon-download"
                @click="handleSingleDownload(9)"
                >下载</el-button
              >
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
    <Report
      :pci-all-list="pciAllList"
      :rqi-list="rqiList"
      :pqi-list="pqiList"
      :mil-val="milVal"
    />
  </div>
</template>

<script>
import {
  getDiseaseList,
  correctDisease,
  deleteDisease,
  getDataList,
  getDiseaseListByDistance,
} from "@/api/data";
import { obj2Param, exportFile } from "@/utils/index";
import Pagination from "@/components/Pagination/index.vue";
import Echarts from "@/views/data/components/echarts.vue";
import { mapState } from "vuex";
import Video from "./video.vue";
import Report from "./report.vue";

export default {
  name: "MapPanel",
  components: {
    Pagination,
    Echarts,
    Video,
    Report,
  },
  props: {
    taskId: {
      type: Number,
      default: -1,
    },
    taskData: {
      type: Object,
      default: () => ({}),
    },
    milVal: {
      type: Number,
      default: 18,
    },
    diseaseWindow: {
      typeof: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      panelShow: true,
      diseaseLoading: false,
      diseaseListData: [],
      tabActive: "1",
      query: {
        type: "all",
        area: 0,
        currentPage: 1,
        pageSize: 10,
        currentPage1: 1,
        pageSize1: 10,
        sort: null,
      },
      filterTxt: "面积>",
      filterUnit: "㎡",
      tableHeightLong: 500,
      tableHeight: 400,
      tableRowIndex: null,
      total: 0,
      curMilVal: 18,
      milArr: [
        {
          label: "50m",
          value: 18,
        },
        {
          label: "100m",
          value: 17,
        },
        {
          label: "200m",
          value: 16,
        },
        {
          label: "500m",
          value: 15,
        },
        {
          label: "1km",
          value: 14,
        },
      ],
      radioData: [
        "PCI 路面损坏状况指数",
        "RQI 路面行驶质量指数",
        "PQI 路面技术状况指数",
      ],
      radioVal: "PCI 路面损坏状况指数",
      crackleTypeVal: "all",
      crackleTypeArr: [],
      pciAllList: [], // 整体病害数据
      rqiList: [], // 平整度数据
      pqiList: [], // PQI数据
      lineObj: {},
      inspectionLoading: false, // 巡检一览loading
      inspectionListData: [], // 巡检一览数据
      inspectionRowIndex: null, // 巡检一览行索引
      inspectionQuery: {
        // 巡检一览检索条件
        currentPage: 1,
        pageSize: 10,
      },
      inspectionTotal: 0,
      videoLoading: false, // 路段实况loading
      videoImageUrl: null,
      layerType: "路况",
      tableType: "2",
      downloadCheckList: [1],
      commandTxt: "",
      modelIdentifyType: 1, // 模型识别类型 1：病害   2： 道路资产  3： 路面风险  5： 沿线设施损坏   9： 慢行病害
      damageStatisLineObj: {}, // 统计分析PCI折线图数据，{病害type：数据}
      isInitImgs: false, // 病害回放 第一次加载图片
      videoPageQuery: {
        page: 0,
        size: 50,
      },
    };
  },
  computed: {
    ...mapState({
      allowDownloadPhoto: (state) => state.account.allowDownloadPhoto,
      workUnit: (state) => state.account.workUnit,
    }),
    listTitle() {
      // 设置病害一览表title
      // 边缘检测设备（ID）  在某日某时间段  跑了一段xx公里的路，发现了xx病害
      const that = this;
      if (that.taskData) {
        const parseStartTime = this.$time.parseTime(
          new Date(that.taskData.startTime),
          "{yyyy}-{mm}-{dd} {hh}:{ii}"
        );
        const parseEndTime = this.$time.parseTime(
          new Date(that.taskData.endTime),
          "{yyyy}-{mm}-{dd} {hh}:{ii}"
        );
        let txt = "";
        let count = 0;
        if (that.modelIdentifyType === 1) {
          txt = "病害";
          count = that.taskData.totalDamageAmount;
        } else if (that.modelIdentifyType === 2) {
          txt = "道路资产";
          count = that.taskData.totalRoadAssetAmount;
        } else if (that.modelIdentifyType === 3) {
          txt = "路面风险";
          count = that.taskData.totalPavementMatterAmount;
        } else if (that.modelIdentifyType === 5) {
          txt = "沿线设施损坏";
          count = that.taskData.totalRoadsideFacilitiesDamageAmount;
        } else if (that.modelIdentifyType === 9) {
          txt = "慢行病害";
          count = that.taskData.totalSlowDamageAmount;
        }

        let name = that.taskData.deviceName;
        if (
          that.taskData.inspectRecord &&
          that.taskData.inspectRecord.routeFullName
        ) {
          name = that.taskData.inspectRecord.routeFullName;
        }
        if (parseStartTime.split(" ")[0] === parseEndTime.split(" ")[0]) {
          // 同一天
          return `${name} ${parseStartTime.split(" ")[0]} ${
            parseStartTime.split(" ")[1]
          }-${parseEndTime.split(" ")[1]}检测里程${
            that.taskData.inspectMileage
          }公里，发现${txt}${count}处`;
        }
        // 跨天
        return `${name} ${parseStartTime}至${parseEndTime}检测里程${that.taskData.inspectMileage}公里，发现${txt}${count}处`;
      }
      return "";
    },
    typeOptions() {
      if (this.modelIdentifyType === 1) {
        console.log("this.$parent.allDiseaseType", this.$parent.allDiseaseType);
        return this.$parent.allDiseaseType;
      }
      if (this.modelIdentifyType === 2) {
        return this.$parent.allRoadAssetsType;
      }
      if (this.modelIdentifyType === 3) {
        return this.$parent.allRoadForeignMatter;
      }
      if (this.modelIdentifyType === 5) {
        return this.$parent.allAlongLine;
      }
      if (this.modelIdentifyType === 9) {
        return this.$parent.slowPatrolType;
      }
      if (this.modelIdentifyType === 10) {
        return this.$parent.cityManagementType;
      }
      return [];
    },
  },
  watch: {
    taskData: {
      deep: true,
      handler() {
        // 初始化 `commandTxt` 和 `modelIdentifyType`
        if (this.taskData && this.taskData.roadType === 9) {
          this.modelIdentifyType = 9;
          this.commandTxt = "慢行病害";
        } else {
          this.modelIdentifyType = 1;
          this.commandTxt = "病害一览";
        }
        if (this.tableType === "1") {
          this.getDiseaseListAjax();
        } else {
          this.getDiseaseListByDistanceAjax();
        }
        this.getInspectionListAjax();
      },
    },
    milVal: {
      handler() {
        this.curMilVal = this.milVal;
      },
    },
  },
  mounted() {
    const that = this;
    this.initTableHeight();
    window.addEventListener("resize", () => {
      that.initTableHeight();
    });
  },
  destroyed() {
    window.removeEventListener("resize", () => {
      this.initTableHeight();
    });
  },
  methods: {
    initTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          document.getElementsByClassName("app-main")[0].clientHeight - 383;
        this.tableHeightLong =
          document.getElementsByClassName("app-main")[0].clientHeight - 302;
      });
    },
    // 4 回放
    handleTabClick(tab) {
      if (tab.name === "4") {
        // 病害回放 只初始化一次图片
        if (!this.isInitImgs) {
          this.isInitImgs = true;
          this.$refs.videoRef.initImgs();
        }
      }
    },
    // 切换标签之前的钩子，若返回 false 或者返回 Promise 且被 reject，则阻止切换。
    handleBeforeLeave(activeName, oldActiveName) {
      if (oldActiveName === "4") {
        this.$refs.videoRef.handlePause();
      }
    },
    handleClear() {
      this.query.type = "all";
      this.getDiseaseListAjax();
    },
    handleFold() {
      this.panelShow = false;
      this.$emit("switchPanel", false);
    },
    handleUnfold() {
      this.panelShow = true;
      this.$emit("switchPanel", true);
    },
    // 设置病害类型Label
    getDiseaseType(type) {
      const arr = this.$parent.allIdentifyType.filter(
        (item) => item.engName === type
      );
      const typeLabel = arr.length > 0 ? arr[0].chineseName : "";
      return typeLabel;
    },
    getDiseaseArea(damages) {
      let str = "";
      damages.forEach((damage, i) => {
        if (i === 0) {
          str += damage.area.toString();
        } else {
          str += `,${damage.atea.toString()}`;
        }
      });
      return str;
    },
    // 获取病害一览list
    async getDiseaseListAjax() {
      this.diseaseLoading = true;
      const { type, area, currentPage, pageSize, sort } = this.query;

      const params = {
        type: type === "all" ? null : type,
        area: this.modelIdentifyType === 1 ? area || 0 : null,
        page: currentPage - 1,
        size: pageSize,
        taskId: this.taskId,
        sort,
        modelIdentifyType: this.modelIdentifyType,
      };

      if (this.tableType === "1") {
        params.dataSource = 0
      }
      const { payload } = await getDiseaseList(params);
      this.diseaseListData = payload.content;
      this.total = payload.totalElements;
      this.diseaseLoading = false;
    },
    // 获取病害一览按距离list sjb需求
    async getDiseaseListByDistanceAjax() {
      this.diseaseLoading = true;
      const { type, area, currentPage, pageSize, sort } = this.query;

      const params = {
        type: type === "all" ? null : type,
        area: this.modelIdentifyType === 1 ? area || 0 : null,
        page: currentPage - 1,
        size: pageSize,
        taskId: this.taskId,
        sort,
        modelIdentifyType: this.modelIdentifyType,
      };
      const { payload } = await getDiseaseListByDistance(params);
      this.diseaseListData = payload.content;
      this.total = payload.totalElements;
      this.diseaseLoading = false;
    },
    async onPagination() {
      // qp需求
      if (this.tableType === "1") {
        await this.getDiseaseListAjax();
      } else {
        await this.getDiseaseListByDistanceAjax();
      }

      if (this.diseaseWindow.show) {
        this.tableRowIndex = 0;
        const rowData = this.diseaseListData[0];
        this.handleDeal(rowData, this.tableRowIndex);
        // this.onSetTagSelected()
      } else {
        this.tableRowIndex = null;
      }
    },
    // 道路病害一览表查询
    handleSearch() {
      this.query.currentPage = 1;
      // qp需求

      if (this.tableType === "1") {
        this.getDiseaseListAjax();
      } else {
        this.getDiseaseListByDistanceAjax();
      }

      this.tableRowIndex = null;
      this.$emit("closeWindow");
    },
    // 道路病害一览表排序
    handleSortChange({ column, prop, order }) {
      if (order) {
        const orderTxt = order === "ascending" ? "asc" : "desc";
        this.query.sort = `${prop},${orderTxt}`;
      } else {
        this.query.sort = null;
      }
      // qp需求
      if (this.tableType === "1") {
        this.getDiseaseListAjax();
      } else {
        this.getDiseaseListByDistanceAjax();
      }
    },
    // 道路病害一览表行点击
    handleRowClick(row, column, event) {
      const index = this.diseaseListData.indexOf(row);
      this.handleDeal(row, index);
    },
    // table高亮
    diseaseTableRowClassName({ row, rowIndex }) {
      if (rowIndex === this.tableRowIndex) {
        return "highlight-row";
      }
      return "";
    },
    // 病害一览导出
    handleExport() {
      const { type, area } = this.query;
      const params = {
        type: type === "all" ? null : type,
        area,
        taskId: this.taskId,
      };
      const paramsStr = obj2Param(params).slice(1);

      exportFile(`damages/export?${paramsStr}`);
    },
    // 看图处理
    handleDeal(row, index) {
      if (row.pictureUrl === null) {
        this.$message({
          message: "没有接收到设备侧上传的图片，请进行重传",
          type: "error",
        });
        return;
      }
      this.tableRowIndex = index;

      console.log("row", row);
      // qp需求
      this.$emit("deal", {
        row,
        index,
        currentPage: this.query.currentPage,
        tableRowIndex: this.tableRowIndex,
        tableType: this.tableType,
        modelIdentifyType: this.modelIdentifyType,
      });
    },
    // 纠错
    async handleCorrect(diseaseType) {
      const rowData = this.diseaseListData[this.tableRowIndex];

      const params = {
        id: rowData.id,
        type: diseaseType,
      };

      const res = await correctDisease(params);
      if (res.status === 200) {
        this.$message({
          message: "纠错成功",
          type: "success",
        });
        // this.tagActive = diseaseType
        // rowData.type = diseaseType
        await this.getDiseaseListAjax();
        const nextRowData = this.diseaseListData[this.tableRowIndex];
        if (nextRowData) {
          this.handleDeal(nextRowData, this.tableRowIndex);
        } else {
          this.$message({
            message: "已经是最后一张图片了，请手动选择一张处理",
            type: "warning",
          });
          this.$emit("closeWindow");
        }
      }
    },
    // 忽略
    async handleIgnore() {
      const rowData = this.diseaseListData[this.tableRowIndex];

      const res = await deleteDisease(rowData.id);
      if (res.status === 200) {
        // this.$message({
        //   message: '该病害已被忽略',
        //   type: 'success',
        // })
        this.$message({
          message: "纠错成功",
          type: "success",
        });
        await this.getDiseaseListAjax();
        const nextRowData = this.diseaseListData[this.tableRowIndex];
        if (nextRowData) {
          this.handleDeal(nextRowData, this.tableRowIndex);
        } else {
          this.$message({
            message: "已经是最后一张图片了，请手动选择一张处理",
            type: "warning",
          });
          this.$emit("closeWindow");
        }
      }
    },
    // 上一张
    async handlePrev() {
      if (this.tableRowIndex === 0) {
        if (this.query.currentPage === 1) {
          this.$message({
            message: "已经是第一张图片了",
            type: "warning",
          });
        } else {
          this.query.currentPage -= 1;
          this.tableRowIndex = this.query.pageSize - 1;
          // qp需求
          if (this.tableType === "1") {
            await this.getDiseaseListAjax();
          } else {
            await this.getDiseaseListByDistanceAjax();
          }

          const rowData = this.diseaseListData[this.query.pageSize - 1];
          this.handleDeal(rowData, this.tableRowIndex);
        }
      } else {
        const rowData = this.diseaseListData[this.tableRowIndex - 1];
        this.handleDeal(rowData, this.tableRowIndex - 1);
      }
    },
    // 下一张
    async handleNext() {
      if (this.tableRowIndex === this.query.pageSize - 1) {
        const totalPage = Math.ceil(this.total / this.query.pageSize);
        if (this.query.currentPage === totalPage) {
          this.$message({
            message: "已经是最后一张图片了",
            type: "warning",
          });
        } else {
          this.query.currentPage += 1;
          this.tableRowIndex = 0;
          // qp需求
          if (this.tableType === "1") {
            await this.getDiseaseListAjax();
          } else {
            await this.getDiseaseListByDistanceAjax();
          }
          const rowData = this.diseaseListData[0];
          this.handleDeal(rowData, this.tableRowIndex);
        }
      } else {
        if (this.diseaseListData.length - 1 === this.tableRowIndex) {
          this.$message({
            message: "已经是最后一张图片了",
            type: "warning",
          });
          return;
        }
        const rowData = this.diseaseListData[this.tableRowIndex + 1];
        this.handleDeal(rowData, this.tableRowIndex + 1);
      }
    },
    removeTableRowIndex() {
      this.tableRowIndex = null;
    },
    // 统计分析
    initStatistic(data) {
      console.log("initStatistic/统计分析");
      const that = this;
      that.pciAllList = [];
      that.rqiList = [];
      that.pqiList = [];
      that.crackleTypeArr = [];

      that.damageStatisLineObj = {};
      data.forEach((segment, index) => {
        if (segment.damageStatisList) {
          segment.damageStatisList.forEach((list) => {
            if (!that.damageStatisLineObj[list.type]) {
              that.damageStatisLineObj[list.type] = [];
            }
            that.damageStatisLineObj[list.type].push(list.pci);
          });
        }
        that.pciAllList.push(segment.pciAll);
        that.rqiList.push(segment.rqi);
        that.pqiList.push(segment.pqi);
      });

      Object.keys(that.damageStatisLineObj).forEach((key) => {
        that.crackleTypeArr.push({
          name: that.getDiseaseType(key),
          value: key,
        });
      });
      that.crackleTypeArr.push({
        name: "总体",
        value: "all",
      });

      that.initChart();
    },
    handleMilChange() {
      console.log("handleMilChange", this.curMilVal);
      //   this.zoom = this.milVal
      // videoPlayMultiple = 100
      // this.slowPlay_list = '慢放倍率'
      this.radioVal = "PCI 路面损坏状况指数";
      this.crackleTypeVal = "all";
      this.$emit("milChange", this.curMilVal);
    },
    handleRadioTypeChange(itemParams) {
      this.radioVal = itemParams;
      this.initChart();
    },
    handleCrackleTypeChange() {
      this.initChart();
    },
    // 折线图
    initChart() {
      let tipName = "";
      let yAxisMin = 0;
      let yAxisMax = 0;
      let showData = [];
      let xName = "";
      const xMilData = [];
      let initVal = 0;

      if (this.radioVal === "RQI 路面行驶质量指数") {
        tipName = "RQI 路面行驶质量指数";
        yAxisMin = 0;
        yAxisMax = 3;
        showData = this.rqiList;
      } else if (this.radioVal === "PQI 路面技术状况指数") {
        tipName = "PQI 路面技术状况指数";
        yAxisMin = 50;
        yAxisMax = 100;
        showData = this.pqiList;
      } else {
        yAxisMax = 70;
        yAxisMin = 0;
        // if (this.crackleTypeVal === 'transverse_ratio') {
        //   tipName = '横向裂纹'
        //   showData = this.pciTransverseList
        // } else if (this.crackleTypeVal === 'longitudinal_ratio') {
        //   tipName = '纵向裂纹'
        //   showData = this.pciLongitudinalList
        // } else if (this.crackleTypeVal === 'aligator_ratio') {
        //   tipName = '龟裂路面'
        //   showData = this.pciAligatorList
        // } else if (this.crackleTypeVal === 'pothole_ratio') {
        //   tipName = '坑洞路面'
        //   showData = this.pciPotholeList
        // } else if (this.crackleTypeVal === 'blockCracks_ratio') {
        //   tipName = '块状裂缝'
        //   showData = this.pciBlockCracksList
        // } else if (this.crackleTypeVal === 'all_ratio') {
        //   tipName = '整体'
        //   showData = this.pciAllList
        // }

        if (this.crackleTypeVal === "all") {
          tipName = "整体";
          showData = this.pciAllList;
        } else {
          tipName = this.getDiseaseType(this.crackleTypeVal);
          showData = this.damageStatisLineObj[this.crackleTypeVal];
        }
      }

      for (let i = 0; i < showData.length; i += 1) {
        if (this.milVal === 14) {
          initVal += 1;
          xMilData.push(initVal);
          xName = "KM";
        } else if (this.milVal === 15) {
          initVal += 500;
          xMilData.push(initVal);
          xName = "M";
        } else if (this.milVal === 16) {
          initVal += 200;
          xMilData.push(initVal);
          xName = "M";
        } else if (this.milVal === 17) {
          initVal += 100;
          xMilData.push(initVal);
          xName = "M";
        } else if (this.milVal === 18) {
          initVal += 50;
          xMilData.push(initVal);
          xName = "M";
        }
      }

      this.lineObj = {
        tooltip: { trigger: "axis" },
        dataZoom: [
          {
            type: "slider",
            show: true,
            realtime: true,
            xAxisIndex: [0],
            // start: 0,
            // end: 30,
            brushSelect: false,
          },
          {
            type: "slider",
            show: true,
            yAxisIndex: [0],
            start: 0,
            end: 100,
            brushSelect: false,
          },
        ],
        grid: {
          x: 40,
          y: 40,
          x2: 70,
          y2: 80,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          name: xName,
          axisLine: {
            lineStyle: {
              type: "solid",
              color: "#A8C0CE", // 左边线的颜色
              width: "2", // 坐标线的宽度
            },
          },
          axisLabel: {
            color: "#394380", // 坐标值得具体的颜色
            fontSize: "14px",
          },
          axisTick: {
            show: false,
          },
          data: xMilData,
        },
        yAxis: {
          max: 100,
          min: 0,
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#545F9E", // 左边线的颜色
              width: "1", // 坐标线的宽度
            },
          },
          axisLabel: {
            fontSize: "14px",
          },
        },
        series: [
          {
            name: tipName,
            type: "line",
            symbol: "circle",
            symbolSize: 9,
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(48, 106, 241, 0.24)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(48, 106, 241, 0)", // 100% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
            itemStyle: {
              color: "#306AF1", // 改变折线点的颜色
              borderColor: "#306AF1",
              lineStyle: {
                color: "#306AF1", // 改变折线颜色
              },
            },
            data: showData,
          },
        ],
      };
    },
    handleEchartsClick(index) {
      this.$emit("echartClick", index);
      // this.handleUpdateVideo(index)
    },
    // 统计分析导出
    handleExportStatistic() {
      const params = {
        taskId: this.taskId,
        zoom: this.milVal,
      };
      const paramsStr = obj2Param(params).slice(1);

      exportFile(`road-statises/export?${paramsStr}`);
    },
    // 巡检一览table高亮
    inspectionTableRowClassName({ row, rowIndex }) {
      if (row.id === this.taskId) {
        return "highlight_blue-row";
      }
      return "";
    },
    // 获取巡检一览列表数据
    async getInspectionListAjax() {
      this.inspectionLoading = true;
      const { currentPage, pageSize } = this.inspectionQuery;
      const query = {
        page: currentPage - 1,
        size: pageSize,
        deviceKey: this.taskData.deviceKey,
      };

      const { payload } = await getDataList(query);
      this.inspectionListData = payload.content;
      this.inspectionTotal = payload.totalElements;
      this.inspectionLoading = false;
    },
    onInspectionPagination() {
      this.getInspectionListAjax();
    },
    // 巡检一览详情
    async handleDetail(row, index) {
      const { path } = this.$route;
      if (path.indexOf("data") > -1) {
        // 数据详情
        await this.$store.dispatch("detail/setTaskData", row.id);
        this.$router.push(`/data/detail/${row.id}`);
      } else {
        // 巡检详情
        this.$router.push(`/inspection/detail/${row.id}`);
      }
    },

    handleUpdateCar(data) {
      this.$emit("updateCar", data);
    },
    handleUpdatePageQuery(page) {
      this.videoPageQuery.page = page;
    },
    handleUpdateVideo(dataIndex, isImg = true) {
      this.$refs.video.handleUpdateVideo(dataIndex, isImg);
    },
    handleLayerTypeChange(val) {
      this.$emit("layerType", val);
    },
    handleMakeReport() {
      this.$emit("makeReport");
    },
    // qp需求
    handleChangeTableType(val) {
      this.query.currentPage = 1;
      this.query.sort = null;
      this.tableRowIndex = null;
      if (this.diseaseWindow.show) {
        this.$emit("closeWindow");
      }

      if (val === "1") {
        this.getDiseaseListAjax();
      } else {
        this.getDiseaseListByDistanceAjax();
      }
    },
    // 生成并下载
    handleBatchExport() {
      const checkList = this.downloadCheckList;
      if (checkList.length === 0) {
        return;
      }
      if (this.$parent.layerType !== "路况") {
        this.$alert(
          "检测报告无法下载，请将地图显示切换到路况模式。",
          "重要提示",
          {
            confirmButtonText: "确定",
          }
        );
        return;
      }
      if (this.$parent.diseaseWindow.show) {
        this.$alert(
          "检测报告无法下载，请先关闭病害处理框，确保路况图显示在可视范围内。",
          "重要提示",
          {
            confirmButtonText: "确定",
          }
        );
        return;
      }
      // if (this.$parent.taskData.taskMode === 1 && checkList.includes(2)) {
      //   this.$alert('当前巡检非检测任务，无法下载检测原始数据。', '重要提示', {
      //     confirmButtonText: '确定',
      //   })
      //   return
      // }
      this.$message({
        message: "报告正在生成中，稍后将自动下载到本地，请勿频繁操作",
        duration: 10000,
      });

      for (let i = 0; i < checkList.length; i++) {
        if (checkList[i] === 1) {
          setTimeout(() => {
            this.$emit("makeReport", { docType: 1 });
          }, 5 * 1000);
        } else if (checkList[i] === 3) {
          setTimeout(() => {
            this.handleExport();
          }, i * 1000);
        } else if (checkList[i] === 4) {
          setTimeout(() => {
            this.handleExportStatistic();
          }, i * 1000);
        } else if (checkList[i] === 5) {
          setTimeout(() => {
            // exportFile(`task-photo-remove-dups/batchDownload?taskId=${this.taskId}`)
            exportFile(`damages/batchDownload?taskId=${this.taskId}`);
          }, i * 1000);
        } else if (checkList[i] === 2) {
          setTimeout(() => {
            exportFile(
              `road-statises/exportRoadDetectZip?taskId=${this.taskId}`
            );
          }, i * 1000);
        } else if (checkList[i] === 6) {
          setTimeout(() => {
            this.$emit("makeReport", { docType: 2 });
          }, 7 * 1000);
        }
      }
    },
    // 数据下载---单个下载
    handleSingleDownload(dType) {
      if (dType === 1) {
        if (this.$parent.layerType !== "路况") {
          this.$alert(
            "检测报告无法下载，请将地图显示切换到路况模式。",
            "重要提示",
            {
              confirmButtonText: "确定",
            }
          );
          return;
        }
        if (this.$parent.diseaseWindow.show) {
          this.$alert(
            "检测报告无法下载，请先关闭病害处理框，确保路况图显示在可视范围内。",
            "重要提示",
            {
              confirmButtonText: "确定",
            }
          );
          return;
        }
        this.$emit("makeReport", {
          docType: 1,
          fileName: `检测报告_${this.taskId}`,
        });
      } else if (dType === 2) {
        exportFile(`road-statises/exportRoadDetectZip?taskId=${this.taskId}`);
      } else if (dType === 3) {
        this.handleExport();
      } else if (dType === 4) {
        this.handleExportStatistic();
      } else if (dType === 5) {
        exportFile(`damages/batchDownload?taskId=${this.taskId}`);
      } else if (dType === 6) {
        this.$emit("makeReport", {
          docType: 2,
          fileName: `汇总报告_${this.taskId}`,
        });
      } else if (dType === 7) {
        exportFile(
          `road-statises/exportAutomaticDetectFile?taskId=${this.taskId}`
        );
      } else if (dType === 8) {
        exportFile(
          `road-statises/exportLandscapeImageFile?taskId=${this.taskId}`
        );
      } else if (dType === 9) {
        exportFile(`inspect-tasks/task-daily-report?taskId=${this.taskId}`);
      }
    },
    // 病害一览--道路资产--路面异物切换，更换标题，列表内容，筛选条件内容
    handleCommand(command) {
      this.commandTxt = command;
      if (command === "病害一览") {
        this.modelIdentifyType = 1;
      } else if (command === "路面病害") {
        this.modelIdentifyType = 1;
      } else if (command === "道路资产") {
        this.modelIdentifyType = 2;
      } else if (command === "路面风险") {
        this.modelIdentifyType = 3;
      } else if (command === "沿线设施损坏") {
        this.modelIdentifyType = 5;
      } else if (command === "慢行病害") {
        this.modelIdentifyType = 9;
      } else if (command === "城市管理问题") {
        this.modelIdentifyType = 10;
      }

      this.query.type = "all";
      this.query.currentPage = 1;
      this.query.sort = null;
      this.tableRowIndex = null;
      if (this.diseaseWindow.show) {
        this.$emit("closeWindow");
      }
      if (this.tableType === "1") {
        // 按类型
        this.getDiseaseListAjax();
      } else {
        // 按距离
        this.getDiseaseListByDistanceAjax();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";

.unfold-btn {
  width: 30px;
  height: 30px;
  box-sizing: border-box;
  position: absolute;
  right: 10px;
  top: 10px;
  border: 1px solid #5c5e5f;
  border-radius: 8px;
  padding: 6px;
  cursor: pointer;
}
// 数据信息panel
.panel {
  width: 32%;
  min-width: 570px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  background: #f6f6f6;
  overflow-y: auto;

  .panel-head {
    // padding: 15px 10px;
    // border-bottom: 1px solid #ddd;
    width: 100%;
    position: absolute;
    // left: 10px;
    // top: 4px;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    .folding-btn {
      width: 30px;
      height: 30px;
      box-sizing: border-box;
      border: 1px solid #5c5e5f;
      border-radius: 8px;
      padding: 6px;
      cursor: pointer;
    }
    .report-btn {
      // margin-right: 20px;
    }
  }

  ::v-deep.el-tabs__nav-scroll {
    padding: 0 60px;
  }
  ::v-deep.el-tabs__content {
    padding: 0px 15px 15px;
    // card
    .common-card {
      border-radius: 10px;
      height: calc(100vh - 170px);

      .common-card-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .title {
          display: flex;
          align-items: center;
          color: #333;
          font-size: 18px;
          font-weight: bold;
          i {
            color: #333;
            font-size: 20px;
            margin-right: 10px;
          }
        }

        .select-cont {
          .el-input__inner {
            width: 100px;
            height: 36px;
            border-radius: 40px;
            margin-left: 10px;
          }
        }
      }

      .statistics-cont {
        // display: none;
        padding: 15px 0 15px 25px;
        margin-bottom: 25px;
        border: 1px solid #d5e2fd;
        background: #f6f8ff;
        border-radius: 8px;
        color: #333;
        font-size: 14px;

        & > div {
          display: flex;
          // justify-content: start;

          &:first-child {
            span {
              flex-shrink: 0;
            }
          }
          span {
            display: inline-block;
            width: 33.3%;
          }

          &:last-child {
            flex-wrap: wrap;
            span {
              margin-top: 10px;
            }
          }
        }
      }

      .chart_tab {
        display: flex;
        justify-content: space-between;
        margin: 10px 10px 0;
        list-style: none;
        border-bottom: 2px solid #dfe4ed;
        padding: 0;

        li {
          // flex: 1;
          font-size: 14px;
          color: #9b9b9b;
          text-align: center;
          border-radius: 2px;
          border-bottom: 2px solid #dfe4ed;
          margin-bottom: -2px;
          // line-height: 30px;
          cursor: pointer;

          &.active {
            border-color: #2d8cf0;
            color: #2d8cf0;
          }
          span {
            display: block;
            &:first-of-type {
              font-weight: bold;
            }
            &:last-of-type {
              padding: 5px 0 15px;
            }
          }
        }
      }

      .chart-radio {
        width: 100%;
        .el-radio-group {
          width: 100%;
          padding: 25px 10px 0;
          display: flex;
          // justify-content: space-between;
          flex-flow: wrap;
        }

        .el-radio {
          width: 25%;
          margin-right: 0px;
          margin-bottom: 10px;
          &:last-child {
            margin-right: 0;
          }
          .el-radio__label {
            font-size: 14px;
          }
        }
      }

      .chart-cont {
        // width: 530px;
        width: 100%;
        margin: 0 auto;
      }

      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
      }
      input[type="number"] {
        -moz-appearance: textfield !important;
      }
      tr.highlight-row td {
        background-color: #eaf4fe;
      }
      tr.highlight_blue-row td {
        background-color: #eaf4fe;
      }
      .el-input-group__prepend,
      .el-input-group__append {
        padding: 0 5px;
      }

      .video-box {
        min-height: 278px;
        height: auto;

        .empty {
          min-height: 278px;
        }
      }

      .el-form {
        .my-form-item {
          .el-select {
            .el-input__inner {
              padding-left: 10px;
              padding-right: 10px;
            }
          }
          .el-input-group {
            .el-input__inner {
              padding-left: 5px;
              padding-right: 5px;
            }
          }
          .el-radio-group {
            margin-top: -3px;
          }

          .el-radio-button__inner {
            padding: 9px 7px !important;
          }

          .el-button {
            padding: 9px 7px !important;
          }
        }

        .el-form-item:last-child {
          margin-right: 0;
        }
      }

      .download-check-group {
        .el-checkbox {
          display: flex;
          align-items: flex-start;
          margin-bottom: 15px;
          .el-checkbox__input {
            margin-top: 2px;
          }

          div {
            font-size: 12px;
            color: #9b9b9b;
            margin-top: 5px;
          }
        }
      }
    }

    #pane-2 {
      .common-card {
        overflow-y: auto;
        @include scrollBar;
      }
    }
  }
  ::v-deep.el-table th > .cell {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}
.select-cont-outer {
  position: absolute;
  bottom: 48px;
  right: calc(32% + 10px);
  z-index: 2;

  @media screen and (max-width: 1600px) {
    right: 580px;
  }

  ::v-deep.el-input__inner {
    width: 100px;
    height: 36px;
    border-radius: 40px;
    margin-left: 10px;
  }
}
.layer-radio {
  position: absolute;
  bottom: 10px;
  right: calc(32% + 10px);
  z-index: 2;

  @media screen and (max-width: 1600px) {
    right: 580px;
  }

  ::v-deep.el-radio-button--small {
    .el-radio-button__inner {
      padding: 9px 12px;
    }
  }
  ::v-deep.el-radio-button:first-child {
    .el-radio-button__inner {
      border-radius: 20px 0 0 20px;
    }
  }
  ::v-deep.el-radio-button:last-child {
    .el-radio-button__inner {
      border-radius: 0 20px 20px 0;
    }
  }
}

.select-cont-outer-fold,
.layer-radio-fold {
  right: 10px;
}

.pagination-container {
  padding-bottom: 0;
  padding-top: 20px;
}

.report-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.el-form--inline .el-form-item {
  margin-right: 5px;
}
</style>
