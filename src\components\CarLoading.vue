<template>
  <div class="loading-next">
    <div class="loading-container">
      <div class="loading-car">
        <div class="loading-car-body">
          <div class="loading-car-top-back">
            <div class="loading-back-curve" />
          </div>
          <div class="loading-car-gate" />
          <div class="loading-car-top-front">
            <div class="loading-wind-sheild" />
          </div>
          <div class="loading-bonet-front" />
          <div class="loading-stepney" />
        </div>
        <div class="loading-boundary-tyre-cover">
          <div class="loading-boundary-tyre-cover-back-bottom" />
          <div class="loading-boundary-tyre-cover-inner" />
        </div>
        <div class="loading-tyre-cover-front">
          <div class="loading-boundary-tyre-cover-inner-front" />
        </div>
        <div class="loading-base-axcel" />
        <div class="loading-front-bumper" />
        <div class="loading-tyre">
          <div class="loading-gap" />
        </div>
        <div class="loading-tyre loading-front">
          <div class="loading-gap" />
        </div>
        <div class="loading-car-shadow" />
      </div>
      <!-- <div class="loading-street">
        <div class="loading-street-stripe"></div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.loading-next {
  transform: scale(0.5);
  width: 0;
  height: 0;
  bottom: -2px;
}

</style>
