<template>
  <div class="header">
    <div class="header-left"></div>
    <div class="header-right">
      <div class="current-time">
        <span class="date">{{ currentDate }}</span>
        <span class="week">{{ currentWeek }}</span>
      </div>
      <div class="header-right-user">
        <img src="@/assets/cheng-de-screen/user.png" alt="人员" class="header-right-user-icon" />
        <span class="user-name">{{ admin ? username : workUnit }}</span>
      </div>
      <div class="header-right-fullscreen" @click="toggleFullScreen">
        <img
          :src="isFullscreen ? fullScreenExitIcon : fullScreenIcon"
          alt="全屏"
          :title="isFullscreen ? '退出全屏' : '全屏'"
          class="fullscreen-icon"
        />
        <span class="fullscreen-text">{{ isFullscreen ? '退出全屏' : '全屏' }}</span>
      </div>
      <img :src="logoutIcon" alt="退出登录" title="退出登录" class="header-right-logout" @click="logout" />
    </div>
    <Logout ref="logoutRef" />
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import logoutIcon from '@/assets/cheng-de-screen/logout.png'
import fullScreenIcon from '@/assets/cheng-de-screen/full-screen.png'
import fullScreenExitIcon from '@/assets/cheng-de-screen/exit-full-screen.png'

import Logout from './logout.vue'
export default {
  components: {
    Logout,
  },
  name: 'Header',
  data() {
    return {
      timer: null,
      now: new Date(),
      logoutIcon,
      isFullscreen: false,
      fullScreenIcon,
      fullScreenExitIcon,
    }
  },
  computed: {
    ...mapGetters(['username']),
    ...mapState({
      admin: (state) => state.account.admin,
      workUnit: (state) => state.account.workUnit,
    }),
    currentDate() {
      return dayjs(this.now).format('YYYY年MM月DD日')
    },
    currentWeek() {
      dayjs.locale('zh-cn')
      return dayjs(this.now).format('dddd')
    },
  },
  methods: {
    async logout() {
      this.$refs.logoutRef.show()
    },
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    // 全屏
    toggleFullScreen() {
      const targetElement = document.getElementById('chengde-screen')

      if (!targetElement) {
        this.$message.warning('未找到目标元素，无法全屏显示')
        return
      }

      if (
        !document.fullscreenElement &&
        !document.mozFullScreenElement &&
        !document.webkitFullscreenElement &&
        !document.msFullscreenElement
      ) {
        if (targetElement.requestFullscreen) {
          targetElement.requestFullscreen()
        } else if (targetElement.mozRequestFullScreen) {
          targetElement.mozRequestFullScreen()
        } else if (targetElement.webkitRequestFullScreen) {
          targetElement.webkitRequestFullScreen()
        } else if (targetElement.msRequestFullscreen) {
          targetElement.msRequestFullscreen()
        }
      } else {
        this.exitFullscreen()
      }
    },
    handleFullscreenChange() {
      this.isFullscreen =
        !!document.fullscreenElement ||
        !!document.mozFullScreenElement ||
        !!document.webkitFullscreenElement ||
        !!document.msFullscreenElement
      this.$emit('fullscreenChange', this.isFullscreen)
    },
  },
  mounted() {
    this.timer = setInterval(() => {
      this.now = new Date()
    }, 1000)

    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener(
      'mozfullscreenchange',
      this.handleFullscreenChange
    )
    document.addEventListener(
      'webkitfullscreenchange',
      this.handleFullscreenChange
    )
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    document.removeEventListener(
      'fullscreenchange',
      this.handleFullscreenChange
    )
    document.removeEventListener(
      'mozfullscreenchange',
      this.handleFullscreenChange
    )
    document.removeEventListener(
      'webkitfullscreenchange',
      this.handleFullscreenChange
    )
    document.removeEventListener(
      'MSFullscreenChange',
      this.handleFullscreenChange
    )
  },
}
</script>
<style lang="scss" scoped>
$color: rgba(133, 180, 224, 1);
.header {
  position: relative;
  width: 100%;
  height: 84px;
  background: url('~@/assets/cheng-de-screen/header.png') no-repeat;
  background-size: 100% 100%;
  &-right {
    position: absolute;
    right: 0px;
    top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 20px;
  }
  .current-time {
    color: $color;
    font-size: 18px;
    margin-right: 71px;
    .date {
      font-weight: bold;
      margin-right: 10px;
    }
  }

  .header-right-user {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 36px;
    color: $color;
    .header-right-user-icon {
      width: 24px;
      height: 22px;
      margin-right: 7px;
    }
  }
  .header-right-fullscreen {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 36px;
    img {
      width: 21px;
      height: 21px;
      margin-right: 5px;
    }
    &:hover {
      transform: scale(1.03);
    }
    .fullscreen-text {
      font-size: 14px;
      color: $color;
    }
  }
  .header-right-logout {
    width: 21px;
    height: 22px;
    cursor: pointer;
    
    &:hover {
      transform: scale(1.03);
    }
  }
}
</style>

