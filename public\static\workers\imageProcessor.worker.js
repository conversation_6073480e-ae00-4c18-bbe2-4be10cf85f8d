self.onmessage = async function(e) {
  try {
    const { imageData, type, coordinate, allIdentifyType } = e.data;

    // 创建离屏 Canvas，使用图片实际尺寸
    const image = await createImageBitmap(imageData);
    const canvas = new OffscreenCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');

    // 绘制图片
    ctx.drawImage(image, 0, 0, image.width, image.height);

    // 绘制标注
    if (coordinate && coordinate.length) {
      drawAnnotation(canvas, ctx, type, coordinate, allIdentifyType);
    }

    // 获取处理后的图片数据
    const blob = await canvas.convertToBlob();
    const processedImageUrl = URL.createObjectURL(blob);

    // 返回处理后的图片URL和加载状态
    self.postMessage({ processedImageUrl});
  } catch (error) {
    self.postMessage({ error: error.message});
  }
}


/**绘制标注的函数 (同 @/mixin/canvas.js ctxDraw)
 * @param {OffscreenCanvas} canvas 
 * @param {CanvasRenderingContext2D} ctx 
 * @param {string} type 
 * @param {string} coordinate 
 * @param {Array} allIdentifyType 
 */
function drawAnnotation(canvas, ctx, type, coordinate, allIdentifyType) {
  ctx.lineWidth = 3
  ctx.font = 'bold 22px Arial'

  let txt = ''
  let typeArr = allIdentifyType.find((item) => item.engName === type)
  
  if (typeArr) {
    ctx.strokeStyle = typeArr.color
    ctx.fillStyle = typeArr.color
    txt = typeArr.chineseName
  } else if (type.includes('prop-road-width')) {
    ctx.strokeStyle = '#0000FF'
    ctx.fillStyle = '#0000FF'
    txt = `路宽 ${type.split(':')[1]}m`
  } else {
    ctx.strokeStyle = '#9C9C9C'
    ctx.fillStyle = '#9C9C9C'
    txt = '其他'
  }

  const coordinateArr = coordinate.split(' ')
  ctx.strokeRect(
    coordinateArr[0] * 1, 
    coordinateArr[1] * 1, 
    coordinateArr[2] - coordinateArr[0], 
    coordinateArr[3] - coordinateArr[1]
  )

  // 文字位置：框上或下，对齐：左对齐或右对齐
  const txtW = ctx.measureText(txt).width
  if (coordinateArr[0] * 1 + txtW > canvas.width) {
    ctx.textAlign = 'right'
    if (coordinateArr[1] * 1 < 25) {
      ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[3] * 1 + 25)
    } else {
      ctx.fillText(txt, coordinateArr[2] * 1, coordinateArr[1] * 1 - 6)
    }
  } else {
    ctx.textAlign = 'left'
    if (coordinateArr[1] * 1 < 25) {
      ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[3] * 1 + 25)
    } else {
      ctx.fillText(txt, coordinateArr[0] * 1, coordinateArr[1] * 1 - 6)
    }
  }
}