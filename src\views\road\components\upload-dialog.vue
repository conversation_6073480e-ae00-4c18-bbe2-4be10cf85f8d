<template>
  <el-dialog title="导入公里桩、百米桩" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
    <el-upload
      ref="upload"
      class="upload-demo"
      drag
      action="https://jsonplaceholder.typicode.com/posts/"
      multiple
      :auto-upload="false"
      :limit="1"
      accept=".xls,xlsx"
      :before-upload="beforeUpload"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击选择</em></div>
      <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件，且不超过500kb</div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <span>
        <el-button type="success" @click="handleTemplateDownload">
          模板下载
        </el-button>
      </span>
      <span>
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="">
          确定
        </el-button>
      </span>
    </div>
  </el-dialog>
</template>
<script>
import { obj2Param, exportFile } from '@/utils/index'

export default {
  props: {
    roadID: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
    }
  },
  methods: {
    show() {
      this.dialogFormVisible = true
    },
    // 做一些文件的验证，比如大小，类型等
    beforeUpload(file) {
      console.log(file)
      // 如果不符合条件，可以返回false或者Promise.reject()
      return true
    },
    // 模板下载
    handleTemplateDownload() {

    },
  },
}

</script>

<style lang="scss" scoped>

.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
