import axios from 'axios'

/**
 * 请求取消管理器
 * 用于集中管理所有的请求取消操作
 */
class RequestCancelManager {
  constructor() {
    // 存储所有取消令牌的映射，键为请求ID或分组名称
    this.cancelTokens = new Map()

    // 分组映射，键为分组名，值为该分组下所有请求ID的集合
    this.groups = new Map()
  }

  /**
   * 创建一个取消令牌并注册到管理器中
   * @param {string} id 请求唯一标识
   * @param {string} [group] 分组名称，可选
   * @returns {CancelToken} 创建的取消令牌
   */
  create(id, group) {
    // 如果已存在同ID的令牌，先取消旧的
    if (this.cancelTokens.has(id)) {
      this.cancel(id, '请求被新请求替代')
    }

    // 创建新的取消令牌源
    const source = axios.CancelToken.source()

    // 注册到管理器
    this.cancelTokens.set(id, source)

    // 如果指定了分组，将请求添加到分组中
    if (group) {
      if (!this.groups.has(group)) {
        this.groups.set(group, new Set())
      }
      this.groups.get(group).add(id)
    }

    return source.token
  }

  /**
   * 取消指定ID的请求
   * @param {string} id 请求唯一标识
   * @param {string} [message='请求被取消'] 取消消息
   * @returns {boolean} 是否成功取消
   */
  cancel(id, message = '请求被取消') {
    if (!this.cancelTokens.has(id)) {
      return false
    }

    try {
      const source = this.cancelTokens.get(id)
      source.cancel(message)
      this.cancelTokens.delete(id)

      // 从所有分组中移除此ID
      this.groups.forEach((ids) => {
        ids.delete(id)
      })

      return true
    } catch (error) {
      console.error('取消请求失败:', error)
      return false
    }
  }

  /**
   * 取消指定分组的所有请求
   * @param {string} group 分组名称
   * @param {string} [message='分组请求被取消'] 取消消息
   * @returns {number} 成功取消的请求数量
   */
  cancelGroup(group, message = '分组请求被取消') {
    if (!this.groups.has(group)) {
      return 0
    }

    const ids = this.groups.get(group)
    let cancelCount = 0

    ids.forEach((id) => {
      if (this.cancel(id, message)) {
        cancelCount++
      }
    })

    // 清空分组
    this.groups.delete(group)

    return cancelCount
  }

  /**
   * 取消所有请求
   * @param {string} [message='所有请求被取消'] 取消消息
   * @returns {number} 成功取消的请求数量
   */
  cancelAll(message = '所有请求被取消') {
    let cancelCount = 0

    this.cancelTokens.forEach((source, id) => {
      try {
        source.cancel(message)
        cancelCount++
      } catch (error) {
        console.error(`取消请求 ${id} 失败:`, error)
      }
    })

    // 清空所有映射
    this.cancelTokens.clear()
    this.groups.clear()

    return cancelCount
  }

  /**
   * 检查请求是否已被取消
   * @param {Error} error 错误对象
   * @returns {boolean} 如果请求被取消返回true，否则返回false
   */
  isCancel(error) {
    return axios.isCancel(error)
  }
}

// 创建单例实例
const requestCancelManager = new RequestCancelManager()

export default requestCancelManager
