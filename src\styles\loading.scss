.loading-next {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  position: absolute;
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;

  .loading-container {
    width: 260px;
    height: 200px;
    position: relative;
    perspective: 400px;
    transform: scale(0.6);
    .loading-street {
      background: #7a7a7a;
      height: 80px;
      width: 260px;
      border-radius: 3px;
      position: absolute;
      top: -40%;
      transform: translateY(-50%);
      overflow: hidden;
      box-shadow: 0 1px 16px rgba(111, 35, 51, 0.4) inset;
      transform: rotateX(60deg) translateZ(-150px);

      .loading-street-stripe {
        background: #d4d4d4;
        height: 8px;
        width: 65px;
        position: absolute;
        bottom: 40px;
        border-radius: 2px;
        box-shadow: 95px 0 0 #d4d4d4, 195px 0 0 #d4d4d4, 295px 0 0 #d4d4d4,
          395px 0 0 #d4d4d4, 495px 0 0 #d4d4d4, 595px 0 0 #d4d4d4,
          695px 0 0 #d4d4d4, 795px 0 0 #d4d4d4;
        -moz-animation: myfirst 4s linear infinite;
        -webkit-animation: myfirst 4s linear infinite;
      }
      @keyframes myfirst {
        to {
          transform: translateX(-600px);
        }
      }

      @-webkit-keyframes myfirst {
        to {
          transform: translateX(-600px);
        }
      }
    }
  }

  /*车*/
  .loading-car {
    position: absolute;
    top: 18%;
    left: 18%;
    z-index: 1;
    animation: run 1.5s linear infinite;
    .loading-tyre {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #3f3f40;
      position: absolute;
      z-index: 2;
      left: 7px;
      top: 20px;
      -moz-animation: tyre-rotate 1.5s infinite linear;
      -webkit-animation: tyre-rotate 1.5s infinite linear;
      &::before {
        content: "";
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #bdc2bd;
        position: absolute;
        top: 5px;
        left: 5px;
      }
    }
    .loading-gap {
      background: #3f3f40;
      width: 2px;
      height: 4px;
      position: absolute;
      left: 14px;
      top: 8px;
      box-shadow: 0 9px 0 #3f3f40;
      &::before {
        content: "";
        display: block;
        width: 2px;
        height: 4px;
        position: absolute;
        background: #3f3f40;
        box-shadow: 0 12px 0 #3f3f40;
        -webkit-transform: rotate(-90deg);
        -webkit-transform-origin: 0 7px;
        -moz-transform: rotate(-90deg);
        -moz-transform-origin: 0 7px;
        z-index: 3;
      }
    }

    .loading-car-base {
      position: absolute;
      display: block;
      width: 125px;
      height: 30px;
      background: #000000;
      border-radius: 10% 10% 50% 50% / 60% 100% 20% 10%;
      -webkit-transform: rotate(-2deg);
      -moz-transform: rotate(-2deg);
      border: solid;
      border-color: #000000;
    }

    .back-bonet {
      background: #4c4b4b;
      border-radius: 54% 25% 0 0;
      height: 22px;
      left: 11px;
      position: absolute;
      top: 8px;
      width: 40px;
    }

    .loading-tyre.loading-front {
      left: 94px;
    }

    .loading-car-body {
      width: 125px;
      height: 24px;
      background: #50b4fd;
      border-top: 1px solid rgba(5, 134, 228, 1);
      border-bottom: 24px solid #50b4fd;
      height: 0;
      top: 10px;
      width: 120px;
      position: relative;
      &::before {
        content: "";
        display: inline-block;
        width: 30px;
        height: 24px;
        position: absolute;
        right: -5px;
        background: #50b4fd;
        border-top-right-radius: 4px;
        z-index: 1;
      }
      &::after {
        content: "";
        display: inline-block;
        width: 121px;
        border-bottom: 1px solid rgba(4, 100, 171, 1);
        border-right: 2px solid transparent;
        height: 0;
        z-index: 2;
        position: absolute;
      }
      .loading-car-top-back {
        background: none repeat scroll 0 0 #4c4b4b;
        border-radius: 5px 0 0 0;
        height: 20px;
        left: 4px;
        position: absolute;
        top: -20px;
        width: 58px;
        &::before {
          width: 30px;
          height: 15px;
          background: #736f6f;
          content: "";
          position: absolute;
          top: 3px;
          left: 8px;
          border-radius: 2px;
        }
        &::after {
          content: "";
          background: #4c4b4b;
          border-radius: 30%;
          height: 5px;
          left: 3px;
          position: absolute;
          top: -1px;
          width: 62px;
        }
        .loading-back-curve {
          background: none repeat scroll 0 0 #4c4b4b;
          border-radius: 960% 100% 0 0;
          height: 20px;
          left: -3px;
          position: absolute;
          top: 1px;
          transform: rotate(6deg);
          width: 5px;
        }
      }

      .loading-car-gate {
        width: 32px;
        height: 20px;
        background: #50b4fd;
        border-radius: 0 0 2px 8px / 0 0 2px 8px;
        box-shadow: 0 0 0 1px rgba(2, 111, 191, 1);
        position: absolute;
        left: 48px;
        &::before {
          content: "";
          width: 8px;
          height: 2px;
          background: #4c4b4b;
          position: absolute;
          top: 2px;
          left: 4px;
          box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
        }
      }
      .loading-car-top-front {
        top: -19px;
        position: absolute;
        left: 47px;
        width: 20px;
        height: 20px;
        background: rgba(105, 191, 255, 1);
        border-left: 1px solid rgba(2, 111, 191, 1);
        border-radius: 2px 0 0 0;
        &::after {
          width: 26px;
          height: 20px;
          -webkit-transform: skew(37deg);
          -moz-transform: skew(37deg);
          background: rgba(105, 191, 255, 1);
          content: "";
          position: absolute;
          top: 0;
          left: 6px;
          border-radius: 4px 0 4px 4px;
        }
        &::before {
          width: 12px;
          height: 5px;
          background: rgba(105, 191, 255, 1);
          content: "";
          position: absolute;
          top: 14px;
          left: 28px;
          z-index: 1;
          border: solid rgba(5, 134, 228, 1);
          border-width: 0 1px 1px 0;
        }
        .loading-wind-sheild {
          top: 3px;
          left: 3px;
          position: absolute;
          z-index: 3;
          width: 18px;
          height: 12px;
          background: #f5e7e7;
          border-radius: 0 3px 0 0;
          &::after {
            width: 12px;
            height: 12px;
            -webkit-transform: skew(25deg);
            -moz-transform: skew(25deg);
            background: #f5e7e7;
            content: "";
            position: absolute;
            top: 0;
            left: 10px;
            border-radius: 3px;
          }
        }
      }

      .loading-bonet-front {
        background: #50b4fd;
        border-radius: 5px 258px 0 38px / 36px 50px 0 0;
        height: 4px;
        position: absolute;
        right: 0;
        top: -4px;
        width: 40px;
        z-index: 0;
      }

      .loading-stepney {
        height: 6px;
        left: -4px;
        position: absolute;
        top: 6px;
        width: 8px;
        z-index: -1;
        background: #3f3f40;
        &::before {
          width: 8px;
          height: 12px;
          background: #3f3f40;
          content: "";
          position: absolute;
          top: -10px;
          left: -7px;
          border-radius: 3px 3px 0 0;
        }

        &::after {
          width: 8px;
          height: 12px;
          background: #0d0c0d;
          content: "";
          position: absolute;
          top: 0px;
          left: -7px;
          border-radius: 0 0 3px 3px;
        }
      }
    }

    .loading-boundary-tyre-cover {
      position: absolute;
      top: 14px;
      left: 10px;
      border-bottom: 20px solid #4c4b4b;
      border-right: 10px solid transparent;
      height: 0;
      width: 20px;
      &::before {
        content: "";
        position: absolute;
        display: inline-block;
        background: #4c4b4b;
        height: 20px;
        width: 15px;
        -webkit-transform: skewX(-20deg);
        -moz-transform: skewX(-20deg);
        border-radius: 3px;
        left: -6px;
        top: 0;
      }

      &::after {
        content: "";
        position: absolute;
        display: inline-block;
        background: #4c4b4b;
        height: 20px;
        width: 20px;
        -webkit-transform: skewx(40deg);
        -moz-transform: skewX(40deg);
        border-radius: 3px;
        right: -22px;
        top: 0;
      }
      .loading-boundary-tyre-cover-back-bottom {
        position: absolute;
        width: 14px;
        height: 8px;
        background: #4c4b4b;
        top: 12px;
        left: -19px;
      }
      .loading-boundary-tyre-cover-inner {
        position: absolute;
        top: 4px;
        left: 4px;
        border-bottom: 16px solid black;
        border-right: 10px solid transparent;
        height: 0;
        width: 15px;
        z-index: 2;
        &::before {
          content: "";
          position: absolute;
          display: inline-block;
          background: black;
          height: 16px;
          width: 15px;
          -webkit-transform: skewX(-20deg);
          -moz-transform: skewX(-20deg);
          border-radius: 3px 3px 0 0;
          left: -8px;
          top: 0;
        }

        &::after {
          content: "";
          position: absolute;
          display: inline-block;
          background: black;
          height: 16px;
          width: 20px;
          -webkit-transform: skewx(40deg);
          -moz-transform: skewX(40deg);
          border-radius: 3px 3px 0 0;
          right: -18px;
          top: 0;
        }
      }
    }

    .loading-tyre-cover-front {
      background: #4c4b4b;
      height: 4px;
      left: 97px;
      position: absolute;
      top: 13px;
      width: 22px;
      z-index: 1;
      &::before {
        background: none repeat scroll 0 0 #4c4b4b;
        content: "";
        display: inline-block;
        height: 21px;
        left: -10px;
        position: absolute;
        top: 0;
        transform: skewX(-30deg);
        width: 10px;
        z-index: 6;
        border-radius: 4px 0 0 0;
      }

      &::after {
        background: none repeat scroll 0 0 #4c4b4b;
        content: "";
        display: inline-block;
        height: 6px;
        left: 14px;
        position: absolute;
        top: 0;
        transform: skewX(30deg);
        width: 17px;
        z-index: 6;
        border-radius: 0 4px 2px 0;
      }

      .loading-boundary-tyre-cover-inner-front {
        position: absolute;
        top: 4px;
        left: 4px;
        border-bottom: 16px solid black;
        border-right: 10px solid transparent;
        height: 0;
        width: 15px;
        z-index: 7;
        &::before {
          background: none repeat scroll 0 0 #000000;
          border-radius: 3px 3px 0 0;
          content: "";
          display: inline-block;
          height: 17px;
          left: -10px;
          position: absolute;
          top: 0;
          transform: skewX(-30deg);
          width: 15px;
        }

        &::after {
          content: "";
          position: absolute;
          display: inline-block;
          background: black;
          height: 16px;
          width: 20px;
          -webkit-transform: skewx(25deg);
          -moz-transform: skewX(25deg);
          border-radius: 3px 3px 0 0;
          right: -23px;
          z-index: 20;
          top: 0;
        }
      }
    }

    .loading-base-axcel {
      background: none repeat scroll 0 0 black;
      bottom: -15px;
      height: 10px;
      left: 30px;
      position: absolute;
      transform: rotate(-2deg);
      width: 70px;
      z-index: -1;
      &::before {
        background: none repeat scroll 0 0 black;
        border-radius: 0 0 0 10px / 0 0 0 5px;
        content: "";
        height: 10px;
        left: -35px;
        position: absolute;
        top: -2px;
        transform: rotate(6deg);
        width: 30px;
      }

      &::after {
        background: none repeat scroll 0 0 black;
        border-radius: 0 0 0 10px / 0 0 0 5px;
        content: "";
        height: 10px;
        right: -33px;
        position: absolute;
        top: -1px;
        transform: rotate(-4deg);
        width: 40px;
        border-radius: 0 10px 5px 0;
      }
    }

    .loading-front-bumper {
      background: none repeat scroll 0 0 #4c4b4b;
      border-radius: 0 2px 2px 0;
      height: 8px;
      position: absolute;
      right: -15px;
      width: 11px;
      z-index: 1;
      -moz-transform: rotate(-5deg);
      -webkit-transform: rotate(-5deg);
      &::before {
        background: none repeat scroll 0 0 #000000;
        content: "";
        height: 10px;
        left: -7px;
        position: absolute;
        transform: rotate(-22deg);
        width: 9px;
        z-index: 1;
      }
    }

    .loading-car-shadow {
      background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
      bottom: -15px;
      box-shadow: -5px 10px 15px 3px #000000;
      left: -7px;
      position: absolute;
      width: 136px;
    }
  }

  @keyframes run {
    0% {
      transform: translate(0.2em, 0.1em) rotate(0deg);
    }

    20% {
      transform: translate(0.1em, 0.2em) rotate(1deg);
    }

    40% {
      transform: translate(0.1em, -0.1em) rotate(-1deg);
    }

    60% {
      transform: translate(-0.1em, 0.2em) rotate(0deg);
    }

    80% {
      transform: translate(-0.1em, 0.1em) rotate(1deg);
    }

    100% {
      transform: translate(0.2em, 0.1em) rotate(-1deg);
    }
  }

  @-moz-keyframes tyre-rotate {
    from {
      -moz-transform: rotate(-360deg);
    }

    to {
      -moz-transform: rotate(0deg);
    }
  }

  @-webkit-keyframes tyre-rotate {
    from {
      -webkit-transform: rotate(-360deg);
    }

    to {
      -webkit-transform: rotate(0deg);
    }
  }
}
