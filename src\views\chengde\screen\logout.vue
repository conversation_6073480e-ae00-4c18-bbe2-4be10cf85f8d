<template>
  <transition name="fade">
    <div v-if="visible" class="logout-dialog-mask" @click="handleOutsideClick">
      <transition name="zoom">
        <div v-if="visible" class="logout-dialog animate__animated animate__fadeIn" ref="dialogEl">
          <!-- 弹窗头部 -->
          <div class="logout-dialog-header">
            <span>退出确认</span>
            <span class="logout-dialog-close" @click="handleCancel"></span>
          </div>
          
          <!-- 弹窗内容区 -->
          <div class="logout-dialog-body">
            <div class="logout-message-row">
              <div class="logout-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="logout-message">
                <p>确定要退出登录吗？</p>
              </div>
            </div>
            
            <div class="logout-dialog-footer">
              <button class="logout-btn logout-btn-cancel" @click="handleCancel">取消</button>
              <button class="logout-btn logout-btn-confirm" @click="handleConfirm">确定退出</button>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script>
import { logout } from '@/utils/auth'
export default {
  name: 'LogoutDialog',
  data() {
    return {
      visible: false
    }
  },
  methods: {
    // 显示弹窗
    show() {
      this.visible = true
      // 禁止背景滚动
      document.body.style.overflow = 'hidden'
    },
    // 隐藏弹窗
    hide() {
      const dialog = this.$refs.dialogEl
      
      // 添加淡出动画
      dialog.classList.remove('animate__fadeIn')
      dialog.classList.add('animate__fadeOut')
      
      const handleAnimationEnd = () => {
        this.visible = false
        document.body.style.overflow = ''
        
        // 移除淡出动画类，为下次显示准备
        dialog.classList.remove('animate__fadeOut')
        dialog.classList.add('animate__fadeIn')
        
        // 清理事件监听器
        dialog.removeEventListener('animationend', handleAnimationEnd)
      }
      
      dialog.addEventListener('animationend', handleAnimationEnd)
    },
    // 处理取消操作
    handleCancel() {
      this.hide()
      this.$emit('cancel')
    },
    // 处理确认退出操作
    handleConfirm() {
      this.hide()
      logout()
      this.$emit('confirm')
    },
    // 处理点击弹窗外部区域
    handleOutsideClick(e) {
      // 如果点击的是遮罩层而不是弹窗内容
      if (e.target.classList.contains('logout-dialog-mask')) {
        this.handleCancel()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 动画时长定义
.animate__fadeIn,
.animate__fadeOut {
  animation-duration: 0.3s;
}

// 遮罩层样式
.logout-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2001;
  display: flex;
  justify-content: center;
  align-items: center;
  
  // 弹窗容器样式 - 匹配地图弹窗风格
  .logout-dialog {
    width: 355px;
    background-color: rgba(9, 28, 48, 1);
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    color: white;
    
    // 弹窗头部样式 - 使用与地图弹窗相同的背景图片
    &-header {
      height: 36px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 30px;
      padding-right: 6px;
      font-size: 16px;
      background: url('~@/assets/cheng-de-screen/map-header.png') no-repeat;
      background-size: 100% 100%;
      color: white;
    }
    
    // 关闭按钮样式 - 使用与地图弹窗相同的图标
    &-close {
      width: 13px;
      height: 14px;
      cursor: pointer;
      background: url('~@/assets/cheng-de-screen/close.png') no-repeat;
      background-size: 100% 100%;
      
      &:hover {
        transform: scale(1.1);
      }
    }
    
    // 弹窗内容区样式
    &-body {
      padding: 20px;
    }
    
    // 弹窗底部样式 - 修改为靠右显示
    &-footer {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end; // 靠右显示
      gap: 15px;
    }
  }
}

// 图标和文本在同一行显示
.logout-message-row {
  display: flex;
  align-items: center; // 垂直居中对齐
  margin-bottom: 10px;
}

// 警告图标样式
.logout-icon {
  margin-right: 15px; 
  
  i {
    font-size: 20px;
    color: #E6A23C;
  }
}

// 提示消息样式
.logout-message {
  font-size: 16px;
  color: white;
  
  p {
    margin: 0;
    line-height: 1.5;
  }
}

// 按钮基础样式
.logout-btn {
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s;
  
  // 取消按钮样式 - 半透明风格
  &-cancel {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
  
  // 确认按钮样式 - 蓝色系风格
  &-confirm {
    background-color: #409EFF;
    color: white;
    
    &:hover {
      background-color: #66b1ff;
    }
  }
}

// 过渡动画 - 遮罩层淡入淡出
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

// 过渡动画 - 弹窗缩放
.zoom-enter-active, .zoom-leave-active {
  transition: transform 0.3s;
}
.zoom-enter, .zoom-leave-to {
  transform: scale(0.9);
}
</style>