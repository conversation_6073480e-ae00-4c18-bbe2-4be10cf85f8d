/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:53
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-09 21:27:06
 */
// import Mock from 'mockjs'

const tokens = {
  refreshToken: 'admin-token',
  ACCESS_TOKEN: 'admin-token'
}

module.exports = [
  // user login
  {
    url: '/authc/login',
    type: 'post',
    response: config => {
      // const { principal } = config.body
      return {
        status: 200,
        payload: tokens
      }
    }
  },
  // 退出
  {
    url: '/authc/logout',
    type: 'post',
    response: _ => {
      return {
        status: 200,
        payload: 'success'
      }
    }
  },

  // 获取用户信息
  {
    url: '/users/info',
    type: 'get',
    response: config => {
      return {
        status: 200,
        payload: {
          id: 1,
          avatar: '',
          nickname: '超级管理员'
        }
      }
    }
  },
  {
    url: '/authc/refresh',
    type: 'post',
    response: _ => {
      return {
        status: 200,
        payload: 'success'
      }
    }
  }
]
