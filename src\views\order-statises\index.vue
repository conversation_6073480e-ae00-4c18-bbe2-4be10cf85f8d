<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-row type="flex" align="middle" :gutter="10" >
        <el-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3" >
          <UnitSelect v-model="listQuery.unitId" placeholder="单位名称" useNodeId />
        </el-col>
        <el-button class="filter-item ml-5" type="primary" icon="el-icon-search" @click="handleFilter">
          查询
        </el-button>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="单位名称" prop="unitName" />
      <el-table-column align="center" label="统计类型">
        <template slot-scope="{row}">
          {{ translateStatisType(row.statisType) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="统计目标">
        <template slot-scope="{row}">
          {{ getBelong(row.statisTarget) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="统计频率">
        <template slot-scope="{row}">
          {{ translateStatisFreq(row) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="巡查里程(km)" prop="inspectMileage" />
    
      <el-table-column align="center" label="创建时间" prop="createTime" >
        <template slot-scope="{row}">
          {{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="更新时间" prop="updateTime" >
        <template slot-scope="{row}">
          {{ new Date(row.updateTime) | parseTime('{yyyy}-{mm}-{dd} {hh}:{ii}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row}">
           <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <EditDialog ref="editDialog" @refresh="refreshTable" />

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getOrderStatises } from '@/api/order-statises'
import Pagination from '@/components/Pagination/index.vue'
import EditDialog from './edit-dialog.vue'
import { getBelongToLabel } from '@/utils/cd_constants'

export default {
  name: 'orderStatises',
  components: { Pagination, EditDialog },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        unitId: null,
        startTime: null,
        endTime: null,
      },
      dateFormat: 'yyyy-MM-dd HH:mm:ss',
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, unitId, startTime, endTime,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        unitId,
        startTime,
        endTime,
      }
      getOrderStatises(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
      })
      .finally(() => {
        this.listLoading = false
      })
    },
    handleEdit(row) {
      this.$refs.editDialog.open(row)
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    getBelong(statisTarget) {
      return getBelongToLabel(statisTarget)
    },
    refreshTable() {
      this.getList()
    },
    translateStatisFreq(row) {
      if (row.statisFreq === 'totally') {
        return '累计'
      } else {
        return row.statisTime
      }
    },
    translateStatisType(statisType) {
      if (statisType === 'belong_to') {
        return '归属'
      } else {
        return statisType || ''
      }
    },
  },
}
</script>
