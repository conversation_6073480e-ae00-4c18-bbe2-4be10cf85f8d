<template>
  <el-dialog title="病害详情" :visible.sync="dialogVisible" width="50%" @close="close">
    <div class="damage-images">
      <el-carousel
        ref="carousel"
        v-if="imageList && imageList.length > 0"
        height="350px"
        trigger="click"
        :autoplay="false"
        @change="handleCarouselChange"
      >
        <el-carousel-item v-for="(image, index) in imageList" :key="index">
          <el-image
            :src="image.objectStorageUrlPrefix + image.originalImagePath"
            fit="contain"
            @click="handleImageClick(index)"
          >
            <template #error>
              <div class="item-image-error"></div>
            </template>
            <template #placeholder>
              <i class="el-icon-loading" style="font-size: 14px;"></i>
            </template>
          </el-image>
        </el-carousel-item>
      </el-carousel>
      <div class="count" v-if="imageList && imageList.length > 0">{{this.currentImageIndex + 1}}/{{imageList.length}}</div>
    </div>
    <div class="detail-info">
      <!-- 第一列 -->
      <div class="detail-item">
        <span class="detail-label">病害名称:</span>
        <span class="detail-value">{{ currentRow.damageName }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">病害归属:</span>
        <span class="detail-value">{{ currentRow.belongToStr }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">位置描述:</span>
        <span class="detail-value">{{ currentRow.addressDescription }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">病害描述:</span>
        <span class="detail-value">{{ currentRow.damageDescription }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">道路名称:</span>
        <span class="detail-value">{{ currentRow.roadName }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">道路编号:</span>
        <span class="detail-value">{{ currentRow.roadCode }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">道路桩号:</span>
        <span class="detail-value">{{ currentRow.startPileStr }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">巡查方向:</span>
        <span class="detail-value">{{ currentRow.directionStr }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">经纬度:</span>
        <span
          class="detail-value"
          v-if="currentRow.gpsLongitude && currentRow.gpsLatitude"
        >{{ currentRow.gpsLongitude + ',' + currentRow.gpsLatitude }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">行政编码:</span>
        <span class="detail-value">{{ currentRow.adCode }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">巡查时间:</span>
        <span class="detail-value">{{ formatTime(currentRow.createTime)}}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">巡查人员:</span>
        <span class="detail-value">{{ currentRow.realName }}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { api as viewerApi } from 'v-viewer'
import 'viewerjs/dist/viewer.css'
export default {
  name: 'DamageDetail',
  data() {
    return {
      dialogVisible: false,
      imageList: [],
      currentRow: {},
      currentImageIndex: 0,
      viewer: null,
    }
  },
  computed: {
    previewSrcList() {
      return this.imageList.map(
        (image) => image.objectStorageUrlPrefix + image.originalImagePath
      )
    },
  },
  methods: {
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString()
    },
    handleCarouselChange(index) {
      console.log('index', index)
      this.currentImageIndex = index
    },
    open(row) {
      this.currentRow = row || {}
      this.imageList = row.images || []
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      this.imageList = []
      this.currentRow = {}
      this.currentImageIndex = 0
    },
    handleImageClick(index) {
      this.currentImageIndex = index
      this.initViewer()
    },
    initViewer() {
      const that = this
      if (that.viewer) {
        that.viewer.destroy()
      }

      that.viewer = viewerApi({
        images: this.previewSrcList,
        options: {
          initialViewIndex: this.currentImageIndex,
          inline: false, // 启用 inline 模式
          button: true, // 显示右上角关闭按钮
          navbar: true, // 显示缩略图导航
          title: true, // 显示当前图片的标题
          toolbar: true, // 显示工具栏
          tooltip: true, // 显示缩放百分比
          movable: true, // 图片是否可移动
          zoomable: true, // 图片是否可缩放
          rotatable: false, // 图片是否可旋转
          scalable: false, // 图片是否可翻转
          transition: true, // 使用 CSS3 过度
          fullscreen: false, // 播放时是否全屏
          keyboard: true, // 是否支持键盘
          minZoomRatio: 0.1,
          zoomRatio: 0.5,
          zIndex: 9999,
          hidden(e) {
            console.log('hidden')
            that.viewer = null
          },
          ready(e) {
            console.log('ready')
          },
          viewed(e) {
            console.log('viewed', e)
          },
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
$img-bg-color: #f5f7fa;
// 修改dialog内容区域高度和滚动
::v-deep .el-dialog__body {
  max-height: 75vh !important;
  padding: 20px 20px 10px;
  overflow-y: auto;
}

// 修改dialog宽度
::v-deep .el-dialog {
  min-width: 700px; // 确保在小屏幕上也有足够宽度
}

// 详情信息区域样式 - 改为两列布局
.detail-info {
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
}

// 详情项样式 - 设置为50%宽度
.detail-item {
  padding: 8px 0px;
  display: flex;
  width: 50%;
  box-sizing: border-box;
}

// 标签样式
.detail-label {
  font-weight: bold;
  width: 100px;
  text-align: right;
  padding-right: 15px;
  color: #606266;
  flex-shrink: 0; /* 防止label缩小 */
}

// 值样式
.detail-value {
  color: #303133;
  word-break: break-all; /* 确保长文本可以换行 */
  flex: 1;
}

// 图片区域样式
.damage-images {
  position: relative;
  background-color: $img-bg-color;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  overflow: hidden;
  ::v-deep .el-carousel-item {
    background-color: $img-bg-color;
  }
  ::v-deep .el-image {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $img-bg-color;
  }
  .count {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    z-index: 100;
    padding: 0 10px;
    font-size: 18px;
    line-height: 27px;
    border-radius: 13px;
  }
}

// 适配小屏幕
@media screen and (max-width: 768px) {
  .detail-item {
    width: 100%; // 在小屏幕上改为单列
  }
}
</style> 