<!--
 * @Author: guowy
 * @Date: 2020-09-01 17:56:31
 * @LastEditors: guowy
 * @LastEditTime: 2020-09-01 17:57:00
-->
## VS Code 设置建议
  VS Code内搜索安装如下扩展：
 （有些扩展存在多个同名的，注意根据扩展ID和发布者做区分）

  - 安装如下必备扩展

    | 扩展名                                                     | 扩展ID                       | 发布者        |
    | --------------------------------------------------------- | ---------------------------- | ------------ |
    | ESLint                                                    | dbaeumer.vscode-eslint       | <PERSON> |
    | Debugger for Chrome                                       | msjsdiag.debugger-for-chrome | Microsoft    |
    | EditorConfig for VS Code                                  | editorconfig.editorconfig    | EditorConfig |
    | Prettier - Code formatter                                 | esbenp.prettier-vscode       | Prettier     |
    | Vetur                                                     | octref.vetur                 | Pine Wu      |
    | Import Cost                                               |                              | Wix          |
    | Chinese (Simplified) Language Pack for Visual Studio Code |                              | Microsoft    |

  - 安装如下建议扩展

    | 扩展名                           | 扩展ID                                | 发布者            |
    | ------------------------------- | ------------------------------------- | ---------------- |
    | Visual Studio IntelliCode       |                                       | Microsoft        |
    | Auto Complete Tag               | formulahendry.auto-complete-tag       | Jun Han          |
    | Auto Import - ES6, TS, JSX, TSX | nucllear.vscode-extension-auto-import | Sergey Korenuk   |
    | Path Autocomplete               | ionutvmi.path-autocomplete            | Mihai Vilcu      |
    | Open file From Path             | jack89ita.open-file-from-path         | jack89ita        |
    | Debugger for Firefox            | firefox-devtools.vscode-firefox-debug | Firefox DevTools | 
    | Sort package.json               | unional.vscode-sort-package-json      | unional          |

  - 文件图标主题建议使用 VSCode Icons，文件类型区分辨识度高
