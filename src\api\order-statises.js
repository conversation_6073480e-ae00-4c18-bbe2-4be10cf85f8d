import request from '@/utils/request'

/**
 * 获取巡查统计数据
 * @param {Object} params 请求参数
 * @param {string} [params.startTime] 开始时间，格式：2025-05-13T10:00:00
 * @param {string} [params.endTime] 结束时间，格式：2025-05-15T19:00:00
 */
export function getOrderStatises(params) {
  return request({
    url: '/order-statises',
    method: 'get',
    params,
  })
}

/**
 * 编辑巡查统计数据
 * @param {Object} params 请求参数
 * @param {string} [params.id] ID
 * @param {number} [params.mainMileage] 主干道里程
 * @param {number} [params.secondMileage] 次干道里程
 */
export function editOrderStatise(data) {
  return request({
    url: `/order-statises/${data.id}`,
    method: 'post',
    data,
  })
}
