<!--  -->
<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="请输入策略名称"
        class="filter-item"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        查询
      </el-button>
      <el-button class="filter-item" type="success" icon="el-icon-plus" @click="handleCreate">
        添加策略
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column align="center" label="策略名称" prop="name" />
      <el-table-column align="center" label="创建时间">
        <template slot-scope="{row}">
          {{ new Date(row.createTime) | parseTime('{yyyy}-{mm}-{dd}') }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="180">
        <template slot-scope="{row,$index}">
          <el-button type="text" @click="handleUpdate(row)">修改</el-button>
          <el-button type="text" @click="handleDelete(row,$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <create-dialog ref="createDialog" :form-data="temp" @refreshTable="refreshTable" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getTactics, deleteTactic } from '@/api/tactics'
import createDialog from './components/create-dialog.vue'

export default {

  components: { Pagination, createDialog },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: false,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
        name: '',
      },
      temp: {},
    }
  },

  computed: {},

  created() {
    this.getList()
  },

  mounted() {},

  methods: {
    getList() {
      this.listLoading = true
      const {
        pageSize, currentPage, name,
      } = this.listQuery
      const query = {
        page: currentPage - 1,
        size: pageSize,
        name: name.trim() === '' ? null : name,
      }
      getTactics(query).then((response) => {
        this.list = response.payload.content
        this.total = response.payload.totalElements
        this.listLoading = false
      })
    },
    refreshTable() {
      this.list = []
      this.getList()
    },
    handleFilter() {
      this.listQuery.currentPage = 1
      this.getList()
    },
    handleCreate() {
      this.temp = {}
      this.$refs.createDialog.show()
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.$refs.createDialog.show()
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该策略吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteTactic(row.id)
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.list.splice(index, 1)
      })
    },
  },
}

</script>
<style lang='less' scoped>
</style>
