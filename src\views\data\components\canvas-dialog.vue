<template>
  <!-- 标注Dialog 开始-->
  <el-dialog
    v-loading="loading"
    element-loading-text="数据加载中"
    element-loading-background="rgba(0, 0, 0, 0.5)"
    :title="title"
    :visible.sync="dialogVisible"
    width="100%"
    :fullscreen="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <!-- <template slot="title">
      <div class="dialog-t">
        <div>{{ title }}</div>
        <div>(说明：Ctrl + 鼠标滚轮放大缩小；Ctrl + 鼠标左键按住拖拽；<span v-if="annotate">鼠标左键按住拖动标注；鼠标单击选中标注框后进行移动或改变大小；鼠标单击选中标注框后按Delete进行删除；</span>)</div>
      </div>
    </template> -->
    <div ref="dialogContainer" class="dialog-container" tabindex="0" @keydown.stop="onShortcutKey">
      <div class="dialog-cont">
        <div v-if="annotate" class="cont-tool">
          <div class="cont-tool-big-title">请选择标注类型:</div>

          <div class="cont-tool-scroll">
            <el-collapse v-model="activeNames">
              <el-collapse-item title="城市道路病害" name="6">
                <el-row :gutter="30">
                  <el-col v-for="(csType) in cityRoadDisesaeType" :key="csType.engName" :span="csType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType !== 6 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 6 ? 'info' : ''"
                      :effect="roadType != 6 ? 'plain' : csType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType == 6 && handleTypeChange(csType)"
                    >
                      {{ csType.chineseName }}
                      <template v-if="csType.hotkey">({{ csType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <!-- 沥青路面类型时： 慢行病害位置显示在 城市管理问题 上边-->
              <el-collapse-item v-if="roadType !== 1 && roadType !== 6" title="慢行病害" name="9">
                <el-row :gutter="30">
                  <el-col v-for="(pType) in slowPatrolType" :key="pType.engName" :span="pType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType != 9 && roadType != 1 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 9 && roadType != 1 ? 'info' : ''"
                      :effect="roadType != 9 && roadType != 1 ? 'plain' : pType.engName === typeValue ? 'dark' : 'plain'"
                      @click="(roadType == 9 || roadType == 1) && handleTypeChange(pType)"
                    >
                      {{ pType.chineseName }}
                      <template v-if="pType.hotkey">({{ pType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="沥青路面病害" name="1">
                <el-row :gutter="30">
                  <el-col v-for="(pType) in pitchDiseaseType" :key="pType.engName" :span="pType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType != 1 && roadType != 10 && roadType != 9 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 1 && roadType != 10 && roadType != 9 ? 'info' : ''"
                      :effect="roadType != 1 && roadType != 10 && roadType != 9 ? 'plain' : pType.engName === typeValue ? 'dark' : 'plain'"
                      @click="(roadType == 1 || roadType == 10 || roadType == 9) && handleTypeChange(pType)"
                    >
                      {{ pType.chineseName }}
                      <template v-if="pType.hotkey">({{ pType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="水泥路面病害" name="2">
                <el-row :gutter="30">
                  <el-col v-for="(cType) in cementDisesaeType" :key="cType.engName" :span="cType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType != 2 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 2 ? 'info' : ''"
                      :effect="roadType != 2 ? 'plain' : cType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType == 2 && handleTypeChange(cType)"
                    >
                      {{ cType.chineseName }}
                      <template v-if="cType.hotkey">({{ cType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="砂石路面病害" name="13">
                <el-row :gutter="30">
                  <el-col v-for="(ssType) in sandstoneDisesaeType" :key="ssType.engName" :span="ssType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType !== 13 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 13 ? 'info' : ''"
                      :effect="roadType != 13 ? 'plain' : ssType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType == 13 && handleTypeChange(ssType)"
                    >
                      {{ ssType.chineseName }}
                      <template v-if="ssType.hotkey">({{ ssType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="道路资产" name="3">
                <el-row :gutter="30">
                  <el-col v-for="(rType) in allRoadAssetsType" :key="rType.engName" :span="rType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType == 10 ? 'not-allowed' : 'pointer'}"
                      :type="roadType == 10 ? 'info' : ''"
                      :effect="rType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType != 10 && handleTypeChange(rType)"
                    >
                      {{ rType.chineseName }}
                      <template v-if="rType.hotkey">({{ rType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="路面风险" name="4">
                <el-row :gutter="30">
                  <el-col v-for="(mType) in allRoadForeignMatter" :key="mType.engName" :span="mType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType == 10 ? 'not-allowed' : 'pointer'}"
                      :type="roadType == 10 ? 'info' : ''"
                      :effect="mType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType != 10 && handleTypeChange(mType)"
                    >
                      {{ mType.chineseName }}
                      <template v-if="mType.hotkey">({{ mType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="沿线设施损坏" name="5">
                <el-row :gutter="30">
                  <el-col v-for="(mType) in allAlongLine" :key="mType.engName" :span="mType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType == 10 ? 'not-allowed' : 'pointer'}"
                      :type="roadType == 10 ? 'info' : ''"
                      :effect="mType.engName === typeValue ? 'dark' : 'plain'"
                      @click="roadType != 10 && handleTypeChange(mType)"
                    >
                      {{ mType.chineseName }}
                      <template v-if="mType.hotkey">({{ mType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-if="roadType == 1 || roadType == 6" title="慢行病害" name="9">
                <el-row :gutter="30">
                  <el-col v-for="(pType) in slowPatrolType" :key="pType.engName" :span="pType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType != 9 && roadType != 1 && roadType != 6 ? 'not-allowed' : 'pointer'}"
                      :type="roadType != 9 && roadType != 1 && roadType != 6 ? 'info' : ''"
                      :effect="roadType != 9 && roadType != 1 && roadType != 6 ? 'plain' : pType.engName === typeValue ? 'dark' : 'plain'"
                      @click="(roadType == 9 || roadType == 1 || roadType == 6) && handleTypeChange(pType)"
                    >
                      {{ pType.chineseName }}
                      <template v-if="pType.hotkey">({{ pType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item title="城市管理问题" name="10">
                <el-row :gutter="30">
                  <el-col v-for="(cType) in cityManagementType" :key="cType.engName" :span="cType.chineseName.length > 8 ? 24 : 12">
                    <el-tag
                      :style="{cursor: roadType !== 10 && roadType !== 1 && roadType !== 6 && roadType !== 9 ? 'not-allowed' : 'pointer'}"
                      :type="roadType !== 10 && roadType !== 1 && roadType !== 6 && roadType !== 9 ? 'info' : ''"
                      :effect="cType.engName === typeValue ? 'dark' : 'plain'"
                      @click="(roadType == 10 || roadType == 1 || roadType === 6 || roadType === 9) && handleTypeChange(cType)"
                    >
                      {{ cType.chineseName }}
                      <template v-if="cType.hotkey">({{ mType.hotkey }})</template>
                    </el-tag>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        <div class="cont-main" :class="[annotate ? 'cont-main-290' : '']">
          <div class="operation-instruction">（说明：① Ctrl + 鼠标滚轮放大缩小；② Ctrl + 鼠标左键按住拖拽；<span v-if="annotate">③ 鼠标左键按住拖动标注；④ 鼠标单击选中标注框后进行移动或改变大小；⑤ 鼠标单击选中标注框后按Delete进行删除；</span>）</div>
          <div v-loading="canvasLoading || isAnnotating" :element-loading-text="isAnnotating ? '正在更新标注中...' : ''" :style="{width: cW + 'px', height: cH + 'px', position: 'relative'}">
            <canvas
              id="bigCanvas"
              ref="bigCanvas"
              tabindex="0"
              :width="cW"
              :height="cH"
            />
            <div v-if="dialogDataObj && dialogDataObj.pileNum" class="route-num">
              <div>{{ dialogDataObj.pileNum.split('+')[0] }}</div>
              <div>+{{ dialogDataObj.pileNum.split('+')[1] }}</div>
            </div>
            <div v-if="dialogDataObj && dialogDataObj.picState === 2" class="empty-div">图片已归档，请联系管理员解冻</div>
            <div v-if="dialogDataObj && dialogDataObj.picState === 3" class="empty-div">您查看的图片时间点是：{{ dialogDataObj.photoTime ? dialogDataObj.photoTime.replace('T', ' ') : '未知' }}，当前图片续传到达时间：{{ dialogDataObj.deviceState && dialogDataObj.deviceState.schedulerFrameFileTimestamp ? dialogDataObj.deviceState.schedulerFrameFileTimestamp.replace('T', ' ') : '未知' }}，还有{{ dialogDataObj.deviceState && dialogDataObj.deviceState.frameFileFailureNums ? dialogDataObj.deviceState.frameFileFailureNums : '未知' }}张图片尚未续传成功，请等待或联系管理员进一步查看。</div>
            <el-card ref="areaFrame" class="area-float-frame">
              <el-form @submit.native.prevent>
                <el-form-item label="病害面积（m²）" prop="area">
                  <el-input v-model.trim="curRectArea" size="small" placeholder="0<面积值<=50" @keydown.native.stop="onAreaKeyDown" @keyup.native.stop="onAreaKeyup" />
                </el-form-item>
              </el-form>
              <div v-if="tipShow" style="font-size: 12px; color: red">值不能为空且大于0小于等于50</div>
            </el-card>
          </div>

          <div class="cont-main-bottom">
            <div :style="{width: download ? radioType === 1 ? 'calc(100% - 530px)' : 'calc(100% - 218px)' : '100%'}">
              <!-- <el-row :gutter="8">
                <el-col :span="5">photoTime: {{ dialogDataObj.photoTime }}</el-col>
                <el-col v-if="radioType === 1" :span="10">picture：{{ dialogDataObj.picture }}</el-col>
                <el-col v-if="radioType === 1 && annotate && rectCoordinate.length" :span="6">左上点坐标{x:{{ rectCoordinate[0] }}，y:{{ rectCoordinate[1] }}}、右下点坐标{x:{{ rectCoordinate[2] }}，y:{{ rectCoordinate[3] }}}</el-col>
                <template v-if="radioType === 2">
                  <el-col :span="5">bootStartTime:{{ dialogDataObj.bootStartTime }}</el-col>
                  <el-col :span="5">inferTimestamp:{{ dialogDataObj.inferTimestamp }}</el-col>
                  <el-col :span="3">gpsLongitude:{{ dialogDataObj.gpsLongitude }}</el-col>
                  <el-col :span="3">gpsLatitude:{{ dialogDataObj.gpsLatitude }}</el-col>
                  <el-col :span="2">gpsSpeed:{{ dialogDataObj.gpsSpeed }}</el-col>
                  <el-col :span="4">gpsSn:{{ dialogDataObj.gpsSn }}</el-col>
                  <el-col :span="4">gpsQuality:{{ dialogDataObj.gpsQuality }}</el-col>
                  <el-col :span="16">originalImagePath:{{ dialogDataObj.originalImagePath }}</el-col>
                  <el-col :span="9">objectStorageUrlPrefix:{{ dialogDataObj.objectStorageUrlPrefix }}</el-col>
                  <el-col :span="2">scheduler:{{ dialogDataObj.scheduler }}</el-col>
                </template>
              </el-row> -->
              <div :style="[{'line-height': 1.5}, {'padding-left': annotate ? '0px' : '10px'}]">
                <!-- 抓拍图集 -->
                <template v-if="radioType === 5 && dialogDataObj">
                  describe: {{ dialogDataObj.describe || '' }}. &nbsp;&nbsp;&nbsp;&nbsp;
                  gpsLongitude:{{ dialogDataObj.gpsLongitude || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsLatitude:{{ dialogDataObj.gpsLatitude || '' }}.nbsp;&nbsp;&nbsp;&nbsp;
                </template>
                <!-- 去重图片 -->
                <template v-if="radioType === 1 && dialogDataObj">&nbsp;&nbsp;&nbsp;&nbsp;picture: {{ dialogDataObj.picture || '' }}.</template>
                <!-- 全量图片 -->
                <template v-if="radioType === 1 && annotate && rectCoordinate.length" :span="6">&nbsp;&nbsp;&nbsp;&nbsp;左上点坐标{x:{{ rectCoordinate[0] }}, y:{{ rectCoordinate[1] }}}、右下点坐标{x:{{ rectCoordinate[2] }}, y:{{ rectCoordinate[3] }}}. </template>
                <template v-if="radioType === 2 && dialogDataObj">
                  &nbsp;&nbsp;&nbsp;&nbsp;bootStartTime:{{ dialogDataObj.bootStartTime || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;inferTimestamp:{{ dialogDataObj.inferTimestamp || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsLongitude:{{ dialogDataObj.gpsLongitude || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsLatitude:{{ dialogDataObj.gpsLatitude || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsSpeed:{{ dialogDataObj.gpsSpeed || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsSn:{{ dialogDataObj.gpsSn || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;gpsQuality:{{ dialogDataObj.gpsQuality || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;zAcclspeed:{{ dialogDataObj.zAcclspeed || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;originalImagePath:{{ dialogDataObj.originalImagePath || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;objectStorageUrlPrefix:{{ dialogDataObj.objectStorageUrlPrefix || '' }}.
                  &nbsp;&nbsp;&nbsp;&nbsp;scheduler:{{ dialogDataObj.scheduler || '' }}.
                </template>
              </div>
            </div>
            <div>
              <el-button v-if="annotate" :loading="submitLoading" type="primary" class="submit-btn" :disabled="canvasLoading || isAnnotating" @click="handleSubmit(false)">保存当前标注（Ctrl+s）</el-button>
              <template v-if="download">
                <el-button type="primary" :loading="downWithoutLoading" @click="downloadWithoutCenterLine">下载标注图</el-button>
                <el-button type="primary" @click="handleDownloadPic">下载原图</el-button>
                <el-button v-if="radioType === 1" type="primary" @click="handleDownloadModelPic">下载标注</el-button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
  <!-- 标注Dialog 结束-->
</template>

<script>
// import { draw, sendThis } from '@/utils/draw'
import CanvasMixin from '@/mixin/canvas'
import { annotateDamages } from '@/api/data'
import { exportPic, exportFile } from '@/utils'
import _ from 'lodash'
import UndoMixin from '../mixin/undo'

const ROAD_TYPE_NAMES = {
  1: ['1', '3', '4', '5', '9', '10'], // 沥青路面
  2: ['2', '3', '4', '5'], // 水泥路面
  13: ['13', '3', '4', '5'], // 砂石路面
  9: ['9', '3', '4', '5', '1', '10'], // 慢巡
  10: ['1', '10'], // 城市管理问题
  6: ['6', '3', '4', '5', '9', '10'], // 城市道路病害
  default: ['1', '3', '4', '5'],
}
export default {
  // 将映射关系提取为静态常量
  mixins: [CanvasMixin, UndoMixin],
  props: {
    annotate: { // 是否需要人工标注
      type: Boolean,
      default: false,
    },
    loading: { // 图集加载中
      type: Boolean,
      default: false,
    },
    download: { // 下载原图
      type: Boolean,
      default: false,
    },
    dataSource: { // 数据源
      type: Number,
      default: 0, // 0 || null--道路图集，1--景观图集
    },
  },
  data() {
    return {
      title: '查看大图',
      dialogVisible: false,
      dialogDataObj: {
        picState: 1,
        picture: '',
        photoTime: '',
        damages: [],
        coordinates: [],
        pileNum: '',
        pictureUrl: '',
        deviceState: {},
        describe: '',
        gpsLongitude: '',
        gpsLatitude: '',
        bootStartTime: '',
        inferTimestamp: '',
        gpsSpeed: '',
        gpsSn: '',
        gpsQuality: '',
        zAcclspeed: '',
        originalImagePath: '',
        objectStorageUrlPrefix: '',
        scheduler: '',
      },
      bigCanvas: null,
      bigCtx: null,
      canvasLoading: false,
      typeValue: '',
      flag: false,
      downX: 0, // 鼠标点击图片落下时的位置（X）
      downY: 0, // 鼠标点击图片落下时的位置（Y）
      rectWidth: 0, // 矩形框的宽
      rectHeight: 0, // 矩形框的高
      allStrokeRect: [], // 画的所有的矩形坐标长度数据存储在数组中
      img: null,
      cW: '640',
      cH: '360',
      curRectIndex: null,
      dialogImgIndex: null,
      pageImage: {
        imgX: 0, // canvas图像距离左上角 x轴距离
        imgY: 0, // canvas图像距离左上角 y轴距离
        imgScale: 1, // canvas实际默认为1
        minImgScale: 1, // canvas实际默认为1最小为1
        maxImgScale: 4, // canvas实际默认为1最大为4
        unit: 100, // 实际和展示canvas 中间的转换比例单位
        scale: 100, // 展示canvas比例
      },
      // 标注状态
      annotationStatus: {
        isAnnotating: false, // 是否正在标注
        annotatingIndex: null, // 正在标注的图片索引
      },
      beforePos: {},
      afterPos: {},
      lockInit: true,
      draggle: false,
      radioType: 1, // 1--去重图片，2--全量图片
      hasAnnotated: false, // 是否已经对图片的标注进行修改
      curRectArea: null,
      key2Type: {},
      tipShow: false,
      roadType: 1, // 1--沥青，2--水泥
      roadLevel: null,
      rectCoordinate: [],
      pitchDiseaseType: [], // 沥青路面病害
      cementDisesaeType: [], // 水泥路面病害
      sandstoneDisesaeType: [], // 砂石路面病害
      cityRoadDisesaeType: [], // 城市道路病害
      submitLoading: false, // 保存按钮的loading
      downWithoutLoading: false, // 下载无中心线标注图的loading
      abortController: null, // 取消图片加载
      // 创建节流函数
      throttledPrev: _.throttle(() => {
        this.$emit('onPrev')
      }, 500),
      throttledNext: _.throttle(() => {
        this.$emit('onNext')
      }, 500),
    }
  },
  computed: {
    activeNames: {
      get() {
        return ROAD_TYPE_NAMES[this.roadType] || ROAD_TYPE_NAMES.default
      },
      set(newValue) {
        // 空setter，仅防止警告
      },
    },
    defaultTypeValue() {
      // 根据路面类型返回对应的默认类型值
      switch (this.roadType) {
      case 9: // 慢巡
        return this.slowPatrolType[0]?.engName || ''
      case 10: // 城管
        return this.cityManagementType[0]?.engName || ''
      default:
        return this.allDiseaseType[0]?.engName || ''
      }
    },
    isAnnotating() {
      if (this.annotationStatus.isAnnotating && this.annotationStatus.annotatingIndex === this.dialogImgIndex) {
        return true
      }
      return false
    },
  },
  watch: {
    /**
     *监听imgScale变化 更新页面上的显示比例 unit = imgScale * unit
     */
    'pageImage.imgScale': {
      handler(val) {
        if (val && !this.lockInit) {
          if (val > this.pageImage.maxImgScale) { // 当放大到最大倍数时 不能再放大
            val = this.pageImage.maxImgScale
          } else if (val < this.pageImage.minImgScale) {
            val = this.pageImage.minImgScale
          } else {
            this.realPosChange()
          }
          this.pageImage.scale = Number((val * this.pageImage.unit).toFixed())
        }
      },
      deep: true,
    },
  },
  beforeDestroy() {
    // 组件销毁前取消节流函数
    this.throttledPrev.cancel()
    this.throttledNext.cancel()
  },
  methods: {
    calcImage(img) {
      if (img.width > this.cW) {
        this.pageImage.imgScale = this.cW / img.width
        if (img.height * this.pageImage.imgScale > this.cH) {
          this.pageImage.imgScale = this.cH / img.height
        }
      } else if (img.height > this.cH) {
        this.pageImage.imgScale = this.cH / img.height
      } else if (this.cW > this.cH) {
        this.pageImage.imgScale = this.cH / img.height
      } else {
        this.pageImage.imgScale = this.cW / img.width
      }
      //  然后根据计算出的imgScale 为当前100%展示的基准。
      this.pageImage.unit = 100 / Number(this.pageImage.imgScale.toFixed(4)) // 转换比例生成 保存4位小数 更精确
      this.pageImage.maxImgScale = Number((this.pageImage.imgScale * 4).toFixed(4)) // 放大最大2倍数生成
      this.pageImage.minImgScale = Number((this.pageImage.imgScale * 1).toFixed(4)) // 缩小最小0.2倍数生成
      // 下面参数是canvas拖动前后需要

      if (img.width * this.pageImage.imgScale < this.cW) {
        this.pageImage.imgX = (this.cW - img.width * this.pageImage.imgScale) / 2
      } else if (img.height * this.pageImage.imgScale < this.cH) {
        this.pageImage.imgY = (this.cH - img.height * this.pageImage.imgScale) / 2
      }

      // eslint-disable-next-line no-multi-assign
      this.beforePos = this.afterPos = {
        x: this.pageImage.imgX,
        y: this.pageImage.imgY,
      }
    },
    async loadImage(signal) {
      return new Promise((resolve, reject) => {
        this.img = new Image()
        this.img.crossOrigin = 'anonymous'
        // 监听abort事件
        if (signal) {
          signal.addEventListener('abort', () => {
            this.img.src = '' // 取消图片加载
            reject(new DOMException('Aborted', 'AbortError'))
          })
        }

        this.img.onload = () => {
          this.calcImage(this.img)
          resolve()
        }

        this.img.onerror = (error) => {
          console.error('图片加载失败:', error)
          reject(error)
        }

        // 设置 src 要在绑定事件之后
        if (this.dialogDataObj && this.dialogDataObj.pictureUrl) {
          this.img.src = this.dialogDataObj.pictureUrl
        } else {
          reject(new Error('无图片URL'))
        }
      })
    },
    updateImageUrl() {
      if (this.img && this.dialogDataObj && this.dialogDataObj.pictureUrl) {
        this.img.setAttribute('crossOrigin', 'anonymous')
        this.img.src = this.dialogDataObj.pictureUrl
      }
    },
    initImage(type = true) {
      const that = this
      const img = new Image()
      img.src = this.img.src
      console.log(img, 'img')
      return new Promise((resolve, reject) => {
        img.onload = function () {
          if (type) {
            that.calcImage(img)
          }
          resolve()
        }
      })
    },
    drawImageAndRect() {
      const that = this
      this.bigCtx.clearRect(0, 0, this.cW, this.cH)
      that.bigCtx.drawImage(
        that.img,
        0, 0,
        that.img.width, that.img.height,
        that.pageImage.imgX, that.pageImage.imgY,
        that.img.width * that.pageImage.imgScale, that.img.height * that.pageImage.imgScale,
      )
      const damages = that.dialogDataObj && (that.dialogDataObj.damages || that.dialogDataObj.coordinates) || []
      // 全量图片下，单图damageList中的病害label为`prop-road-width`时，表示路宽识别，文字明确显示：路宽 x.xxm
      let damageListArr = null
      if (this.radioType === 2 && that.dialogDataObj && that.dialogDataObj.damageList) {
        try {
          damageListArr = JSON.parse(that.dialogDataObj.damageList)
        } catch (e) {
          console.error('解析damageList失败:', e)
        }
      }

      if (damages && damages.length > 0) {
        damages.forEach((damage, $dIndex) => {
          let txt = ''
          let color = ''
          const typeArr = this.allIdentifyType.filter((item) => item.engName === damage.type)
          if (typeArr.length > 0) {
            color = typeArr[0].color
            txt = typeArr[0].chineseName
          } else if (damageListArr && damageListArr[$dIndex].label === 'prop-road-width') {
            color = '#0000FF'
            txt = `路宽 ${(damageListArr[$dIndex].size_m2).toFixed(2)}m`
          } else {
            color = '#9C9C9C'
            txt = '其他'
          }

          const coordinateArr = damage.coordinate.split(' ')
          const x = coordinateArr[0] * 1
          const y = coordinateArr[1] * 1
          const w = coordinateArr[2] - coordinateArr[0]
          const h = coordinateArr[3] - coordinateArr[1]
          let tX = coordinateArr[0] * 1
          let tY = coordinateArr[1] * 1 - 4
          const tWidth = coordinateArr[2] - coordinateArr[0] - 4

          that.bigCtx.lineWidth = 3
          that.bigCtx.strokeStyle = color
          that.bigCtx.setLineDash([])
          that.bigCtx.strokeRect(
            x * that.pageImage.imgScale + that.pageImage.imgX,
            y * that.pageImage.imgScale + that.pageImage.imgY,
            w * that.pageImage.imgScale,
            h * that.pageImage.imgScale,
          )

          // 病害文字
          that.bigCtx.font = 'bold 22px Arial'
          that.bigCtx.fillStyle = color
          // that.bigCtx.fillText(
          //   txt,
          //   tX * that.pageImage.imgScale + that.pageImage.imgX,
          //   tY * that.pageImage.imgScale + that.pageImage.imgY,
          //   tWidth * that.pageImage.imgScale,
          // )
          const txtW = that.bigCtx.measureText(txt).width
          if (x + txtW > that.img.width) {
            that.bigCtx.textAlign = 'right'
            tX = coordinateArr[2] * 1
            if (y < 25) {
              tY = coordinateArr[3] * 1 + 25
            } else {
              tY = coordinateArr[1] * 1 - 6
            }
          } else {
            that.bigCtx.textAlign = 'left'
            tX = coordinateArr[0] * 1
            if (y < 25) {
              tY = coordinateArr[3] * 1 + 25
            } else {
              tY = coordinateArr[1] * 1 - 6
            }
          }
          that.bigCtx.fillText(
            txt,
            tX * that.pageImage.imgScale + that.pageImage.imgX,
            tY * that.pageImage.imgScale + that.pageImage.imgY,
          )
          that.allStrokeRect.push({
            x,
            y,
            w,
            h,
            color,
            type: damage.type,
            txt,
            tX,
            tY,
            tWidth,
            area: damage.area,
            sourceType: damage.sourceType, // 1 AI识别  2 人工标注
            addStatus: false, // 是否新增框,对框做操作后置为true
          })

          // console.log('that.allStrokeRect', that.allStrokeRect);
        })
      }

      // 鼠标监听事件
      // sendThis(that)
      // draw(that.bigCanvas, that.allStrokeRect)

      that.bigCanvas.addEventListener('mousewheel', this.mouseWheelEvent)
      this.mouseDraggleEvent()

      if (that.annotate) {
        that.draw() // 调用此方法后会覆盖mouseDraggleEvent中绑定的事件
        that.drawCenterLine() // 画中线
      }
      this.abortController = null
      this.canvasLoading = false
    },
    drawCenterLine() {
      // 画中线
      const that = this
      that.bigCtx.beginPath()
      that.bigCtx.lineWidth = 2
      that.bigCtx.setLineDash([20, 20])
      that.bigCtx.strokeStyle = '#fff'
      that.bigCtx.moveTo(that.pageImage.imgX, (that.img.height * that.pageImage.imgScale) / 2 + that.pageImage.imgY)
      that.bigCtx.lineTo(that.pageImage.imgX + that.img.width * that.pageImage.imgScale, (that.img.height * that.pageImage.imgScale) / 2 + that.pageImage.imgY)
      that.bigCtx.stroke()
      that.bigCtx.closePath()
    },
    realPosChange() {
      this.pageImage.imgX = (1 - this.pageImage.imgScale) * this.afterPos.x + (this.beforePos.x - this.afterPos.x)
      this.pageImage.imgY = (1 - this.pageImage.imgScale) * this.afterPos.y + (this.beforePos.y - this.afterPos.y)

      if (this.annotate) {
        this.reDraw()
      } else {
        this.drawImageAndRect() // 重新绘制图片
      }
    },
    /**
     * 坐标互转
     * @param {*} x
     * @param {*} y
     */
    pointsToCanvas(x, y) {
      const box = this.bigCanvas.getBoundingClientRect()
      return {
        x: x - box.left - (box.width - this.bigCanvas.width) / 2,
        y: y - box.top - (box.height - this.bigCanvas.height) / 2,
      }
    },
    mouseWheelEvent(event) {
      if (event.ctrlKey) {
        // 取消浏览器默认的放大缩小网页行为
        event.preventDefault()
        this.lockInit = false
        this.beforePos = this.pointsToCanvas(event.clientX, event.clientY)
        this.afterPos = {
          x: Number(((this.beforePos.x - this.pageImage.imgX) / this.pageImage.imgScale).toFixed(2)),
          y: Number(((this.beforePos.y - this.pageImage.imgY) / this.pageImage.imgScale).toFixed(2)),
        }

        if (event.wheelDelta < 0 || event.detail < 0) {
          // 鼠标滚轮往下滚动 每次缩小10%)
          this.pageImage.imgScale -= 100 / this.pageImage.unit / 10
          if (this.pageImage.imgScale <= this.pageImage.minImgScale) { // 当缩小到最小倍数时 不能再缩小
            this.pageImage.imgScale = this.pageImage.minImgScale
          }
        } else if (event.wheelDelta > 0 || event.detail > 0) {
          // 鼠标滚轮往上滚动 每次放大10%
          this.pageImage.imgScale += 100 / this.pageImage.unit / 10
          if (this.pageImage.imgScale >= this.pageImage.maxImgScale) { // 当放大到最大倍数时 不能再放大
            this.pageImage.imgScale = this.pageImage.maxImgScale
          }
        }
      }
    },
    mouseDraggleEvent() {
      this.bigCanvas.onmousedown = (e) => {
        if (e.ctrlKey) {
          this.draggle = true
          this.beforePos = this.pointsToCanvas(e.clientX, e.clientY)
        }
      }
      this.bigCanvas.onmousemove = (e) => {
        if (this.draggle) {
          this.afterPos = this.pointsToCanvas(e.clientX, e.clientY)
          const x = this.afterPos.x - this.beforePos.x
          const y = this.afterPos.y - this.beforePos.y
          this.pageImage.imgX += x
          this.pageImage.imgY += y
          this.beforePos = JSON.parse(JSON.stringify(this.afterPos))
          this.drawImageAndRect()
        }
      }
      this.bigCanvas.onmouseup = (e) => {
        this.draggle = false
      }
    },
    async show(picture, index, radioType, roadType, roadLevel) {
      const that = this

      // 检查是否是当前正在标注的图片
      if (this.annotationStatus.isAnnotating && this.annotationStatus.annotatingIndex === index) {
        console.log('正在标注的图片', index, this.annotationStatus.annotatingIndex)
      }

      await that.clearDialogData()
      // 取消之前的请求
      if (this.abortController) {
        this.abortController.abort()
        this.abortController = null
      }
      this.abortController = new AbortController()
      const { signal } = this.abortController

      // 城市道路病害（roadType = 1 且 roadLevel = 6）需特殊处理，其病害筛选条件为 road_type=6且classification=1
      await that.getModelIdentifyTypes(roadLevel === 6 ? 6 : roadType)

      // 从所有道路类型中，筛选出沥青、水泥、砂石、城市道路相关的病害类型
      if (this.cementDisesaeType.length === 0) {
        this.cementDisesaeType = that.filterDiseaseType(1, 2)
      }
      if (this.pitchDiseaseType.length === 0) {
        this.pitchDiseaseType = that.filterDiseaseType(1, 1)
      }
      if (this.sandstoneDisesaeType.length === 0) {
        this.sandstoneDisesaeType = that.filterDiseaseType(1, 13)
      }
      if (this.cityRoadDisesaeType.length === 0) {
        this.cityRoadDisesaeType = that.filterDiseaseType(1, 6)
      }

      // 初始化快捷键对应类型
     
      // 显示canvasloading
      this.dialogVisible = true
      // 确保picture不为null或undefined
      this.dialogDataObj = picture || {
        picState: 1,
        picture: '',
        photoTime: '',
        damages: [],
        coordinates: [],
        pileNum: '',
        pictureUrl: '',
        deviceState: {},
        describe: '',
        gpsLongitude: '',
        gpsLatitude: '',
        bootStartTime: '',
        inferTimestamp: '',
        gpsSpeed: '',
        gpsSn: '',
        gpsQuality: '',
        zAcclspeed: '',
        originalImagePath: '',
        objectStorageUrlPrefix: '',
        scheduler: '',
      }
      if(this.dialogDataObj.picState === 1) { 
        this.canvasLoading = true
      }
      this.dialogImgIndex = index
      this.radioType = radioType
      // 城市道路病害（roadType = 1 且 roadLevel = 6）, 重置roadType = 6（前端自定义类型）
      this.roadType = roadLevel === 6 ? roadLevel : Number(roadType)
      this.roadLevel = Number(roadLevel)

      // console.log('this.allDiseaseType', this.allDiseaseType)
      // 如果typeValue为空则使用默认值
      this.typeValue ||= this.defaultTypeValue
      if(this.dialogDataObj.picState === 1) { 
        this.$nextTick(async () => {
          that.$refs.dialogContainer.focus()
          const lW = document.getElementsByClassName('cont-main')[0].clientWidth
        const lH = document.body.clientHeight - 200

        this.cW = parseInt(lW)
        this.cH = parseInt(lH)

        this.bigCanvas = document.getElementById('bigCanvas')
        this.bigCtx = this.bigCanvas.getContext('2d')

        if (picture.picState === 1) {
          try {
            const img = await this.loadImage(signal)
            // 只有在未取消的情况下才绘制
            if (!signal.aborted) {
              that.drawImageAndRect(img)
            }
          } catch (err) {
            console.log(err.name, 'err')
            // 只有非取消类错误才显示
            if (err.name === 'AbortError') {
              this.canvasLoading = false
              console.error('图片加载失败:', err.message)
            }
          }
        } else {
          this.bigCtx.clearRect(0, 0, this.cW, this.cH)
        }
      })
      }
    },
    handleClose() {
      this.dialogVisible = false
      // 恢复初始值
      this.clearDialogData()
      this.$refs.bigCanvas.removeEventListener('mousewheel', this.mouseWheelEvent)
      this.bigCanvas.onmousedown = null
      this.bigCanvas.onmousemove = null
      this.bigCanvas.onmouseup = null
      this.bigCanvas.onmouseout = null
      this.bigCanvas.onkeydown = null
      this.bigCtx.clearRect(0, 0, this.bigCanvas.width, this.bigCanvas.height)
      this.$emit('onClose')
    },
    async clearDialogData() {
      // this.typeValue = ''
      this.allStrokeRect = []
      this.dialogImgIndex = null
      this.pageImage = {
        imgX: 0, // canvas图像距离左上角 x轴距离
        imgY: 0, // canvas图像距离左上角 y轴距离
        imgScale: 1, // canvas实际默认为1
        minImgScale: 1, // canvas实际默认为1最小为1
        maxImgScale: 4, // canvas实际默认为1最大为4
        unit: 100, // 实际和展示canvas 中间的转换比例单位
        scale: 100, // 展示canvas比例
      }
      this.beforePos = {}
      this.afterPos = {}
      this.lockInit = true
      this.draggle = false
      this.curRectIndex = null
      this.curRectArea = null
      this.tipShow = false
      this.rectCoordinate = []
      this.onHideAreaFrame()
      
      // 重置dialogDataObj为默认对象
      this.dialogDataObj = {
        picState: 1,
        picture: '',
        photoTime: '',
        damages: [],
        coordinates: [],
        pileNum: '',
        pictureUrl: '',
        deviceState: {},
        describe: '',
        gpsLongitude: '',
        gpsLatitude: '',
        bootStartTime: '',
        inferTimestamp: '',
        gpsSpeed: '',
        gpsSn: '',
        gpsQuality: '',
        zAcclspeed: '',
        originalImagePath: '',
        objectStorageUrlPrefix: '',
        scheduler: '',
      }
    },
    /**
     * 重置标注状态  是否已经对图片的标注进行修改
     */
    resetAnnotationState() {
      this.hasAnnotated = false
    },
    /**
     * 保存当前标注
     * @param {Boolean} isUpdate 是否是更新标注
     */
    async handleSubmit(isUpdate = true) {
      const that = this
      if (this.canvasLoading || !this.annotate || !this.hasAnnotated) {
        return
      }
      
      // 检查dialogDataObj是否存在
      if (!this.dialogDataObj || !this.dialogDataObj.id) {
        this.$message.error('无法保存标注，缺少必要的图片信息')
        return
      }
      
      const updateImageIndex = this.dialogImgIndex
      // 标注正在保存中,用来快速及时切换更新图片
      this.annotationStatus.isAnnotating = true
      this.annotationStatus.annotatingIndex = updateImageIndex

      this.submitLoading = true
      // 提前保存dialogImgIndex，因为左右快捷键切换图片时dialogImgIndex会被更新，从而导致`updateSingle`中的i非旧的dialogImgIndex，单图标注出错

      const damages = []
      if (this.allStrokeRect.length > 0) {
        this.curRectIndex = null

        const tempAllStrokeRect = [...this.allStrokeRect]
        const newAllStrokeRect = []

        // 处理病害框 画在图片外的舍去，画超出的保留图片上的部分
        tempAllStrokeRect.forEach((rect, i) => {
          const ltP = { x: rect.x, y: rect.y } // 左上点
          const lbP = { x: rect.x, y: rect.y + rect.h } // 左下点
          const rbP = { x: rect.x + rect.w, y: rect.y + rect.h } // 右下点
          const rtP = { x: rect.x + rect.w, y: rect.y } // 右上点

          // 判断四个点的位置
          if ((rbP.x < 0) || (ltP.y > that.img.height) || (ltP.x > that.img.width) || (rbP.y < 0)) {
            // 这个框不在图片上，舍去
          } else if (ltP.x >= 0 && ltP.y >= 0 && rbP.x <= that.img.width && rbP.y <= that.img.height) {
            // 这个框完全在图片上
            const tempRect = { ...rect }
            // if (rbP.y <= that.img.height / 2) {
            //   // 框在中线上，舍去
            // } else if (ltP.y < that.img.height / 2) {
            //   tempRect.y = that.img.height / 2
            //   tempRect.h = rbP.y - that.img.height / 2

            //   newAllStrokeRect.push(tempRect)
            // } else {
            //   newAllStrokeRect.push(tempRect)
            // }
            newAllStrokeRect.push(tempRect)
          } else {
            const tempRect = { ...rect }
            if (ltP.x < 0) {
              tempRect.x = 0
              tempRect.w = rbP.x
              tempRect.tWidth = rbP.x
            }
            if (ltP.y < 0) {
              tempRect.y = 0
              tempRect.h = rbP.y
            }
            if (rbP.y > that.img.height) {
              tempRect.h = that.img.height - tempRect.y
            }
            if (rbP.x > that.img.width) {
              tempRect.w = that.img.width - tempRect.x
              tempRect.tWidth = that.img.width - tempRect.x
            }
            newAllStrokeRect.push(tempRect)
          }
        })

        that.allStrokeRect = newAllStrokeRect

        this.reDraw()

        // 面积框不显示，默认传1，不再判断是否输入面积
        // const flag = this.allStrokeRect.every((item) => {
        //   const isDiseaseType = that.allDiseaseType.some(type => type.engName === item.type)
        //   if (isDiseaseType) {
        //     if (item.area) {
        //       return true
        //     }
        //     return false
        //   }
        //   return true
        // })
        // if (!flag) {
        //   this.$message({
        //     dangerouslyUseHTMLString: true,
        //     message: '标注的<strong>"路面病害"</strong>中存在没有面积值，请检查并填入面积值.',
        //     type: 'error',
        //     duration: 5000,
        //   })
        //   return
        // }
        this.allStrokeRect.forEach((rect) => {
          const obj = {
            type: rect.type,
            coordinate: `${rect.x.toFixed(1)} ${rect.y.toFixed(1)} ${(rect.x + rect.w).toFixed(1)} ${(rect.y + rect.h).toFixed(1)}`,
            sourceType: rect.sourceType,
            addStatus: rect.addStatus,
          }

          if (rect.area) {
            obj.area = parseFloat(rect.area)
          }
          damages.push(obj)
        })
      }

      const postData = {
        id: this.dialogDataObj.id,
        dataSource: this.dataSource,
        damages,
      }

      annotateDamages(postData).then(({ status, payload }) => {
        if (status === 200) {
          if (updateImageIndex === this.dialogImgIndex && isUpdate) {
            // 如果当前图片是正在标注的图片，则重新绘制图片
            this.show(payload, this.dialogImgIndex, this.radioType, this.roadType, this.roadLevel)
          }

          this.$message({
            message: '标注提交成功',
            type: 'success',
          })

          this.$emit('updateSingle', { newData: payload, i: updateImageIndex })

          // addStatus代表是不是新增的病害，保存后将addStatus全部重置为false
          this.allStrokeRect.forEach((rect) => {
            rect.addStatus = false
          })
        }
      }).finally(() => {
        this.hasAnnotated = false
        this.submitLoading = false
        this.annotationStatus.isAnnotating = false
      })
    },
    handleTypeChange(typeObj) {
      console.log('左侧点击', this.curRectIndex, typeObj.engName)
      if (this.curRectIndex !== null) {
        // const data = {
        //   type: 'changeType',
        //   frame: this.allStrokeRect[this.curRectIndex],
        //   index: this.curRectIndex,
        // }
        console.log('data', this.w)
        if (this.allStrokeRect[this.curRectIndex].type !== typeObj.engName) {
          this.allStrokeRect[this.curRectIndex].sourceType = 2
          this.allStrokeRect[this.curRectIndex].area = 1
        }
        this.allStrokeRect[this.curRectIndex].type = typeObj.engName
        this.allStrokeRect[this.curRectIndex].color = typeObj.color
        this.allStrokeRect[this.curRectIndex].txt = typeObj.chineseName

        const tempArr = this.allDiseaseType.filter((item) => item.engName === typeObj.engName)
        if (tempArr.length <= 0) {
          delete this.allStrokeRect[this.curRectIndex].area
        }
        // console.log(this.w)
        this.reDraw(this.curRectIndex)
        this.hasAnnotated = true

        // // console.log(this.w[this.curRectIndex]);

        // this.addMemberUndo(data)
        // draw(this.bigCanvas, this.w, this.curRectIndex)
      } else {
      // console.log('!null--');

        this.typeValue = typeObj.engName
      }
    },
    // 画布中绘制矩形及移动画布
    draw(i) {
      const that = this
      // 变量初始化
      let sX = 0 // 鼠标X坐标
      let sY = 0 // 鼠标Y坐标
      /*
      *鼠标移动进行第一层判断, 区分情况: 无矩形, 已有矩形无选中, 已有选中矩形
      */
      that.bigCanvas.onmousemove = function (em) {
        sX = em.offsetX
        sY = em.offsetY
        let iem // 鼠标移动时临时存储当前鼠标所在矩形的下标

        if (that.allStrokeRect.length === 0) { // **** 无矩形 ****
          // 绘制新矩形
          that.newDraw()
        } else if (i === undefined) { // **** 已有矩形无选中 ****
          // 判断鼠标位置
          that.allStrokeRect.forEach((value, index, array) => {
            const scaleW = value.w * that.pageImage.imgScale
            const scaleH = value.h * that.pageImage.imgScale
            const scaleX = value.x * that.pageImage.imgScale + that.pageImage.imgX
            const scaleY = value.y * that.pageImage.imgScale + that.pageImage.imgY
            if (scaleW > 0 && scaleH > 0 && sX > scaleX && sX < scaleX + scaleW && sY > scaleY && sY < scaleY + scaleH) {
              // 鼠标在右下方向生成的矩形中
              iem = index
              that.judgeDraw(iem)
            }
            if (scaleW < 0 && scaleH > 0 && sX < scaleX && sX > scaleX + scaleW && sY > scaleY && sY < scaleY + scaleH) {
              // 鼠标在左下方向生成的矩形中
              iem = index
              that.judgeDraw(iem)
            }
            if (scaleW > 0 && scaleH < 0 && sX > scaleX && sX < scaleX + scaleW && sY < scaleY && sY > scaleY + scaleH) {
              // 鼠标在右上方向生成的矩形中
              iem = index
              that.judgeDraw(iem)
            }
            if (scaleW < 0 && scaleH < 0 && sX < scaleX && sX > scaleX + scaleW && sY < scaleY && sY > scaleY + scaleH) {
              // 鼠标在左上方向生成的矩形中
              iem = index
              that.judgeDraw(iem)
            }
            if (iem === undefined) {
              // 鼠标不在矩形中
              that.newDraw()
            }
          })
          that.curRectIndex = null
        } else { // **** 已有选中矩形 ****
          // 判断鼠标位置

          for (let index = 0; index < that.allStrokeRect.length; index++) {
            const value = that.allStrokeRect[index]

            const scaleW = value.w * that.pageImage.imgScale
            const scaleH = value.h * that.pageImage.imgScale
            const scaleX = value.x * that.pageImage.imgScale + that.pageImage.imgX
            const scaleY = value.y * that.pageImage.imgScale + that.pageImage.imgY

            if (sX < scaleX + 5 && sX > scaleX - 5 && sY < scaleY + 5 && sY > scaleY - 5) {
              // ***  鼠标在起点角  ***
              if (index === i) {
                that.changeDraw(i, 1)
                break
              }
            } else if (sX < scaleX + scaleW + 5 && sX > scaleX + scaleW - 5 && sY < scaleY + 5 && sY > scaleY - 5) {
              // ***  鼠标在起点横向角  ***
              if (index === i) {
                that.changeDraw(i, 2)
                break
              }
            } else if (sX < scaleX + 5 && sX > scaleX - 5 && sY < scaleY + scaleH + 5 && sY > scaleY + scaleH - 5) {
              // ***  鼠标在起点纵向角  ***
              if (index === i) {
                that.changeDraw(i, 3)
                break
              }
            } else if (sX < scaleX + scaleW + 5 && sX > scaleX + scaleW - 5 && sY < scaleY + scaleH + 5 && sY > scaleY + scaleH - 5) {
              // ***  鼠标在终点角  ***
              if (index === i) {
                that.changeDraw(i, 4)
                break
              }
            } else if (scaleW > 0 && scaleH > 0 && sX > scaleX && sX < scaleX + scaleW && sY > scaleY && sY < scaleY + scaleH) {
              // ***  鼠标在右下方向生成的矩形中  ***
              iem = index
              that.judgeDraw(index)
              break
            } else if (scaleW < 0 && scaleH > 0 && sX < scaleX && sX > scaleX + scaleW && sY > scaleY && sY < scaleY + scaleH) {
              // ***  鼠标在左下方向生成的矩形中  ***
              iem = index
              that.judgeDraw(index)
              break
            } else if (scaleW > 0 && scaleH < 0 && sX > scaleX && sX < scaleX + scaleW && sY < scaleY && sY > scaleY + scaleH) {
              // ***  鼠标在右上方向生成的矩形中  ***
              iem = index
              that.judgeDraw(index)
              break
            } else if (scaleW < 0 && scaleH < 0 && sX < scaleX && sX > scaleX + scaleW && sY < scaleY && sY > scaleY + scaleH) {
              // ***  鼠标在左上方向生成的矩形中  ***
              iem = index
              that.judgeDraw(index)
              break
            } else if (iem === undefined) {
              // *** 鼠标不在矩形中 ***
              that.newDraw()
              // that.curRectIndex = null
            }
          }
        }

        /* 鼠标移出画布区域时保存选中矩形下标(如有) */
        that.bigCanvas.onmouseout = function (eo) {
          if (i !== undefined) {
            // 初始化
            that.draw(i)
          }
        }
      }
    },
    /* 绘制新矩形或者移动 */
    newDraw() {
      const that = this
      // 初始化变量
      let start = false // 画框状态, false时不执行画框操作
      let sX = 0 // 起点X坐标
      let sY = 0 // 起点Y坐标

      /* 按下鼠标左键 */
      that.bigCanvas.onmousedown = function (ed) {
        console.log('鼠标按下')
        if (!ed.ctrlKey) { // 进入标注模式
          that.bigCanvas.style.cursor = 'crosshair'

          /* 使用变量 */
          start = true
          sX = ed.offsetX
          sY = ed.offsetY

          /* 重置按键监听, 防止选中取消后仍可删除 */
          that.delDraw(null)

          /* 鼠标移动 */
          that.bigCanvas.onmousemove = function (em) {
            if (start) {
              // console.log(' 绘制新矩形或者移动');
              that.bigCanvas.style.cursor = 'crosshair'
              // 重新绘制
              that.reDraw()
              // 设置边框为虚线
              const typeArr = that.allIdentifyType.filter((item) => item.engName === that.typeValue)

              that.bigCtx.strokeStyle = typeArr[0].color
              that.bigCtx.beginPath()
              that.bigCtx.setLineDash([8, 8])
              that.bigCtx.rect(sX, sY, em.offsetX - sX, em.offsetY - sY)
              that.bigCtx.stroke()

              // console.log(that.allIdentifyType);
            }
          }

          /* 鼠标抬起 */
          that.bigCanvas.onmouseup = function (eu) {
            const typeArr = that.allIdentifyType.filter((item) => item.engName === that.typeValue)
            if (start && Math.abs(eu.offsetX - sX) > 10 && Math.abs(eu.offsetY - sY) > 10) {
              // 画的方向不定，确定左上角坐标 ltx lty
              const ltx = eu.offsetX > sX ? sX : eu.offsetX
              const lty = eu.offsetY > sY ? sY : eu.offsetY
              // 改变矩形数组
              const frame = {
                x: (ltx - that.pageImage.imgX) / that.pageImage.imgScale,
                y: (lty - that.pageImage.imgY) / that.pageImage.imgScale,
                w: (Math.abs(eu.offsetX - sX)) / that.pageImage.imgScale,
                h: (Math.abs(eu.offsetY - sY)) / that.pageImage.imgScale,
                color: typeArr[0].color,
                type: that.typeValue,
                txt: typeArr[0].chineseName,
                tX: (ltx - that.pageImage.imgX) / that.pageImage.imgScale,
                tY: (lty - that.pageImage.imgY) / that.pageImage.imgScale,
                tWidth: (eu.offsetX - sX) / that.pageImage.imgScale,
                area: 1, // 不显示面积框，默认
                sourceType: 2,
                addStatus: true,
              }

              that.allStrokeRect.push(frame)
              const index = that.allStrokeRect.length - 1
              // 重新绘制
              that.reDraw(index)
              // 改变画框状态
              start = false
              // 初始化
              that.draw(index)

              that.delDraw(index)

              that.hasAnnotated = true

              that.addMemberUndo({ index, type: 'addFrame', frame })
            } else {
              that.curRectIndex = null
              that.onHideAreaFrame()
              // 重新绘制
              that.reDraw()
              // 改变画框状态
              start = false
              // 初始化
              that.draw()
            }

            that.bigCanvas.style.cursor = 'default'
          }

          /* 鼠标离开矩形区 */
          that.bigCanvas.onmouseout = function (eo) {
            const typeArr = that.allIdentifyType.filter((item) => item.engName === that.typeValue)
            if (start && Math.abs(eo.offsetX - sX) > 10 && Math.abs(eo.offsetY - sY) > 10) {
              // 画的方向不定，确定左上角坐标 ltx lty
              const ltx = eo.offsetX > sX ? sX : eo.offsetX
              const lty = eo.offsetY > sY ? sY : eo.offsetY
              // 改变矩形数组
              const frame = {
                x: (ltx - that.pageImage.imgX) / that.pageImage.imgScale,
                y: (lty - that.pageImage.imgY) / that.pageImage.imgScale,
                w: (Math.abs(eo.offsetX - sX)) / that.pageImage.imgScale,
                h: (Math.abs(eo.offsetY - sY)) / that.pageImage.imgScale,
                color: typeArr[0].color,
                type: that.typeValue,
                txt: typeArr[0].chineseName,
                tX: (ltx - that.pageImage.imgX) / that.pageImage.imgScale,
                tY: (lty - that.pageImage.imgY) / that.pageImage.imgScale,
                tWidth: (eo.offsetX - sX) / that.pageImage.imgScale,
                area: 1, // 不显示面积框，默认
              }

              that.allStrokeRect.push(frame)
              // 重新绘制
              that.reDraw(that.allStrokeRect.length - 1)
              // 改变画框状态
              start = false
              // 初始化
              that.draw(that.allStrokeRect.length - 1)

              that.delDraw(that.allStrokeRect.length - 1)

              that.hasAnnotated = true
            } else {
              that.curRectIndex = null
              that.onHideAreaFrame()
              // 重新绘制
              that.reDraw()
              // 改变画框状态
              start = false
              // 初始化
              that.draw()
            }

            that.bigCanvas.style.cursor = 'default'
          }
        } else { // 按住Crtl键进入移动图片模式
          that.draggle = true
          that.beforePos = that.pointsToCanvas(ed.clientX, ed.clientY)

          that.reDraw()

          that.bigCanvas.onmousemove = (em) => {
            if (that.draggle) {
              that.afterPos = that.pointsToCanvas(em.clientX, em.clientY)
              const x = that.afterPos.x - that.beforePos.x
              const y = that.afterPos.y - that.beforePos.y
              that.pageImage.imgX += x
              that.pageImage.imgY += y
              that.beforePos = JSON.parse(JSON.stringify(that.afterPos))
              that.reDraw()
            }
          }

          that.bigCanvas.onmouseup = (eu) => {
            that.draggle = false
            that.draw()
          }
        }
      }
    },
    /* 选中矩形, 重绘矩形, 并分发后续事件 */
    judgeDraw(iem) {
      const that = this

      // 初始化变量
      let sX = 0 // 起点X坐标
      let sY = 0 // 起点Y坐标

      /* 按下鼠标左键 */
      that.bigCanvas.onmousedown = function (ed) {
        sX = ed.offsetX
        sY = ed.offsetY
        console.log('选中矩形', iem)
        // 更改选中状态, 重绘矩形
        that.reDraw(iem)

        /* 当仅点击选中矩形便抬起鼠标后, 重新初始化画布 */
        that.bigCanvas.onmouseup = function () {
          console.log('当仅点击选中矩形便抬起鼠标后, 重新初始化画布')
          // 重绘矩形
          that.reDraw(iem)

          // 初始化
          that.draw(iem)
        }

        /* 按住拖动鼠标, 移动选中矩形 */
        that.moveDraw(iem, sX, sY)

        /* 监听键盘, 点击后可以控制删除, 由于移动矩形事件已经监听了onmousemove, 所以在移动矩形方法中仍有一次调用 */
        that.delDraw(iem)
      }
    },
    /* 编辑矩形四个角 */
    changeDraw(i, site) {
      const that = this
      that.bigCanvas.style.cursor = 'pointer'
      // site: 操作矩形角的位置, 1-起点 2-起点横向 3-起点纵向 4-终点
      const mark = that.allStrokeRect[i]

      that.bigCanvas.onmousedown = function (ed) {
        // 保存鼠标落下位置的X, Y坐标, firefox中鼠标移动后ed.offsetX ed.offsetY会变成 0, 需要使用临时参数存储起来
        const sX = ed.offsetX // 起点X坐标
        const sY = ed.offsetY // 起点Y坐标

        /* 移动鼠标 */
        that.bigCanvas.onmousemove = function (em) {
          console.log('changeDraw--编辑矩形四个角-onmousemove', site)
          // 计算绘制数据
          let iframe = {}
          // eslint-disable-next-line default-case
          switch (site) {
          case 1:
            iframe = {
              x: (em.offsetX - that.pageImage.imgX) / that.pageImage.imgScale,
              y: (em.offsetY - that.pageImage.imgY) / that.pageImage.imgScale,
              w: mark.w - ((em.offsetX - sX) / that.pageImage.imgScale),
              h: mark.h - ((em.offsetY - sY) / that.pageImage.imgScale),
              color: mark.color,
              type: mark.type,
              txt: mark.txt,
              tX: (em.offsetX - that.pageImage.imgX) / that.pageImage.imgScale,
              tY: (em.offsetY - that.pageImage.imgY) / that.pageImage.imgScale,
              tWidth: mark.w - ((em.offsetX - sX) / that.pageImage.imgScale),
              addStatus: true,
            }
            if (mark.w - ((em.offsetX - sX) / that.pageImage.imgScale) <= 10) {
              iframe.x = mark.x + mark.w - 10
              iframe.w = 11
              iframe.tX = mark.x + mark.w - 10
            }
            if (mark.h - ((em.offsetY - sY) / that.pageImage.imgScale) <= 10) {
              iframe.y = mark.y + mark.h - 10
              iframe.h = 11
              iframe.tY = mark.y + mark.h - 10
            }
            // if (mark.area) {
            //   iframe.area = mark.area
            // }
            break
          case 2:
            iframe = {
              x: mark.x,
              y: mark.y + ((em.offsetY - sY) / that.pageImage.imgScale),
              w: mark.w + ((em.offsetX - sX) / that.pageImage.imgScale),
              h: mark.h - ((em.offsetY - sY) / that.pageImage.imgScale),
              color: mark.color,
              type: mark.type,
              txt: mark.txt,
              tX: mark.x,
              tY: mark.y + ((em.offsetY - sY) / that.pageImage.imgScale),
              tWidth: mark.w + ((em.offsetX - sX) / that.pageImage.imgScale),
              addStatus: true,
            }
            if (mark.w - ((sX - em.offsetX) / that.pageImage.imgScale) <= 10) {
              iframe.w = 10
            }
            if (mark.h - ((em.offsetY - sY) / that.pageImage.imgScale) <= 10) {
              iframe.y = mark.y + mark.h - 10
              iframe.h = 10
              iframe.tY = mark.y + mark.h - 10
            }
            // if (mark.area) {
            //   iframe.area = mark.area
            // }
            break
          case 3:
            iframe = {
              x: mark.x + ((em.offsetX - sX) / that.pageImage.imgScale),
              y: mark.y,
              w: mark.w - ((em.offsetX - sX) / that.pageImage.imgScale),
              h: mark.h + ((em.offsetY - sY) / that.pageImage.imgScale),
              color: mark.color,
              type: mark.type,
              txt: mark.txt,
              tX: mark.x + ((em.offsetX - sX) / that.pageImage.imgScale),
              tY: mark.tY,
              tWidth: mark.w - ((em.offsetX - sX) / that.pageImage.imgScale),
              addStatus: true,
            }
            if (mark.w - ((em.offsetX - sX) / that.pageImage.imgScale) <= 10) {
              iframe.x = mark.x + mark.w - 10
              iframe.tX = mark.x + mark.w - 10
              iframe.w = 10
            }
            if (mark.h - ((sY - em.offsetY) / that.pageImage.imgScale) <= 10) {
              iframe.h = 10
            }
            // if (mark.area) {
            //   iframe.area = mark.area
            // }
            break
          case 4:
            iframe = {
              x: mark.x,
              y: mark.y,
              w: mark.w + ((em.offsetX - sX) / that.pageImage.imgScale),
              h: mark.h + ((em.offsetY - sY) / that.pageImage.imgScale),
              color: mark.color,
              type: mark.type,
              txt: mark.txt,
              tX: mark.tX,
              tY: mark.tY,
              tWidth: mark.w + ((em.offsetX - sX) / that.pageImage.imgScale),
              addStatus: true,
            }
            if (mark.w - ((sX - em.offsetX) / that.pageImage.imgScale) <= 10) {
              iframe.w = 10
            }
            if (mark.h - ((sY - em.offsetY) / that.pageImage.imgScale) <= 10) {
              iframe.h = 10
            }
            // if (mark.area) {
            //   iframe.area = mark.area
            // }
            break
          }
          iframe.sourceType = 2
          iframe.area = 1 // 人工修改过后面积默认是1

          that.allStrokeRect.splice(i, 1, iframe)

          // 重新绘制
          that.reDraw(i)

          that.hasAnnotated = true
        }

        /* 鼠标离开矩形区 */
        that.bigCanvas.onmouseout = function (eo) {
          console.log('离开')
          // 重新绘制
          that.reDraw()
          // 初始化
          that.draw()
        }
      }
    },
    /* 移动矩形 */
    moveDraw(i, sX, sY) {
      const that = this
      const mark = that.allStrokeRect[i]
      let iframe = {}
      that.bigCanvas.onmousemove = function (em) {
        that.bigCanvas.style.cursor = 'move'

        const mX = em.offsetX
        const mY = em.offsetY
        iframe = {
          x: mark.x + ((mX - sX) / that.pageImage.imgScale),
          y: mark.y + ((mY - sY) / that.pageImage.imgScale),
          w: mark.w,
          h: mark.h,
          color: mark.color,
          type: mark.type,
          txt: mark.txt,
          tX: mark.tX + ((mX - sX) / that.pageImage.imgScale),
          tY: mark.tY + ((mY - sY) / that.pageImage.imgScale),
          tWidth: mark.tWidth,
          sourceType: mX !== sX || mY !== sY ? 2 : mark.sourceType, // 1 AI识别  2 人工标注
          addStatus: true,
        }
        if (mark.area) {
          iframe.area = mark.area
        }

        if (mX !== sX || mY !== sY) {
          iframe.area = 1
        } else if (mark.area) iframe.area = mark.area

        // console.log('iframe', iframe);
        that.allStrokeRect.splice(i, 1, iframe)
        /* 监听键盘, 使矩形在移动后仍可删除, 在点击未移动过的矩形时仍有一次监听 */
        that.delDraw(i)
        // 重新绘制
        that.reDraw(i)

        that.hasAnnotated = true
      }

      that.bigCanvas.onmouseup = function () {
        that.bigCanvas.style.cursor = 'default'
        // 重绘矩形
        that.reDraw(i)
        // 初始化
        that.draw(i)
      }
    },
    /* 删除矩形 */
    delDraw(i) {
      const that = this
      /* 按键事件 */
      if (i === null) {
        // i为null时阻止按键监听事件冒泡
        that.bigCanvas.onkeydown = function (k) {
          return false
        }
        that.bigCanvas.blur()
      } else {
        // 监听按键事件
        that.bigCanvas.focus()
        that.bigCanvas.onkeydown = function (k) {
          const key = k.keyCode || k.which
          // 安 Delete键删除
          if (key === 46 && i !== null) {
            if (that.allStrokeRect.length >= 1) {
              console.log('删除--------------', i)
              // 删除数组元素
              that.allStrokeRect.splice(i, 1)
              // 重绘矩形
              that.reDraw()
            } else {
              /* 矩形数组长度为0, 已将矩形框全部删除 */
              that.bigCtx.clearRect(0, 0, that.bigCanvas.width, that.bigCanvas.height)
            }
            // 重置监听状态, 防止删除完毕后, 按键监听不消失
            that.delDraw(null)
            // 重绘矩形
            that.reDraw()
            // 初始化
            that.draw()

            that.curRectIndex = null

            that.hasAnnotated = true
            that.bigCanvas.focus()
          }
        }
      }
    },
    /* 重绘所有矩形 */
    reDraw(i) {
      const that = this
      that.bigCtx.clearRect(0, 0, that.bigCanvas.width, that.bigCanvas.height)
      that.bigCtx.drawImage(
        that.img,
        0, 0,
        that.img.width, that.img.height,
        that.pageImage.imgX, that.pageImage.imgY,
        that.img.width * that.pageImage.imgScale, that.img.height * that.pageImage.imgScale,
      )
      // 画中线
      that.drawCenterLine()
      that.rectCoordinate = []
      that.onHideAreaFrame()
      that.bigCtx.lineWidth = 3
      that.allStrokeRect.forEach((value, index, array) => {
        // console.log(value);
        if (i === undefined || index !== i) { // 绘制未选中部分
          that.bigCtx.beginPath()
          that.bigCtx.setLineDash([]) // 设置边框为实线
          that.bigCtx.strokeStyle = value.color
          that.bigCtx.rect(
            value.x * that.pageImage.imgScale + that.pageImage.imgX,
            value.y * that.pageImage.imgScale + that.pageImage.imgY,
            value.w * that.pageImage.imgScale,
            value.h * that.pageImage.imgScale,
          )
          that.bigCtx.stroke()

          // 病害文字
          that.bigCtx.font = 'bold 22px Arial'
          that.bigCtx.fillStyle = value.color
          // that.bigCtx.fillText(
          //   value.txt,
          //   value.tX * that.pageImage.imgScale + that.pageImage.imgX,
          //   value.tY * that.pageImage.imgScale + that.pageImage.imgY,
          //   value.tWidth * that.pageImage.imgScale,
          // )
          const txtW = that.bigCtx.measureText(value.txt).width
          let tX = 0
          let tY = 0
          if (value.x + txtW > that.img.width) {
            that.bigCtx.textAlign = 'right'
            tX = value.x + value.w
            if (value.y < 25) {
              tY = value.y + value.h + 25
            } else {
              tY = value.y - 6
            }
          } else {
            that.bigCtx.textAlign = 'left'
            tX = value.x
            if (value.y < 25) {
              tY = value.y + value.h + 25
            } else {
              tY = value.y - 6
            }
          }
          that.bigCtx.fillText(
            value.txt,
            tX * that.pageImage.imgScale + that.pageImage.imgX,
            tY * that.pageImage.imgScale + that.pageImage.imgY,
          )
        } else { // 绘制已选中部分
          /* 绘制方框 */
          that.bigCtx.beginPath()
          that.bigCtx.setLineDash([8, 8]) // 设置边框为实线
          that.bigCtx.strokeStyle = value.color
          that.bigCtx.rect(
            value.x * that.pageImage.imgScale + that.pageImage.imgX,
            value.y * that.pageImage.imgScale + that.pageImage.imgY,
            value.w * that.pageImage.imgScale,
            value.h * that.pageImage.imgScale,
          )
          that.bigCtx.fillStyle = 'RGBA(102,102,102,0.2)'
          that.bigCtx.fillRect(
            value.x * that.pageImage.imgScale + that.pageImage.imgX,
            value.y * that.pageImage.imgScale + that.pageImage.imgY,
            value.w * that.pageImage.imgScale,
            value.h * that.pageImage.imgScale,
          )
          that.bigCtx.stroke()
          // 绘制四个角的圆圈
          that.bigCtx.beginPath()
          that.bigCtx.strokeStyle = value.color
          that.bigCtx.arc(
            value.x * that.pageImage.imgScale + that.pageImage.imgX,
            value.y * that.pageImage.imgScale + that.pageImage.imgY,
            4, 0, Math.PI * 2,
          )
          that.bigCtx.fillStyle = value.color
          that.bigCtx.fill()// 画起点实心圆
          that.bigCtx.stroke()
          that.bigCtx.beginPath()
          that.bigCtx.arc(
            value.x * that.pageImage.imgScale + that.pageImage.imgX,
            (value.y * that.pageImage.imgScale + that.pageImage.imgY) + (value.h * that.pageImage.imgScale),
            4, 0, Math.PI * 2,
          )
          that.bigCtx.fillStyle = value.color
          that.bigCtx.fill()// 画起点纵向实心圆
          that.bigCtx.stroke()
          that.bigCtx.beginPath()
          that.bigCtx.arc(
            (value.x * that.pageImage.imgScale + that.pageImage.imgX) + (value.w * that.pageImage.imgScale),
            (value.y * that.pageImage.imgScale + that.pageImage.imgY) + value.h * that.pageImage.imgScale,
            4, 0, Math.PI * 2,
          )
          that.bigCtx.fillStyle = value.color
          that.bigCtx.fill()// 画起点横向实心圆
          that.bigCtx.stroke()
          that.bigCtx.beginPath()
          that.bigCtx.arc(
            (value.x * that.pageImage.imgScale + that.pageImage.imgX) + (value.w * that.pageImage.imgScale),
            (value.y * that.pageImage.imgScale + that.pageImage.imgY),
            4, 0, Math.PI * 2,
          )
          that.bigCtx.fillStyle = value.color
          that.bigCtx.fill()// 画终点实心圆
          that.bigCtx.stroke()

          // 病害文字
          that.bigCtx.font = 'bold 22px Arial'
          that.bigCtx.fillStyle = value.color
          // that.bigCtx.fillText(
          //   value.txt,
          //   value.tX * that.pageImage.imgScale + that.pageImage.imgX,
          //   value.tY * that.pageImage.imgScale + that.pageImage.imgY,
          //   value.tWidth * that.pageImage.imgScale,
          // )
          const txtW = that.bigCtx.measureText(value.txt).width
          let tX = 0
          let tY = 0
          if (value.x + txtW > that.img.width) {
            that.bigCtx.textAlign = 'right'
            tX = value.x + value.w
            if (value.y < 25) {
              tY = value.y + value.h + 25
            } else {
              tY = value.y - 6
            }
          } else {
            that.bigCtx.textAlign = 'left'
            tX = value.x
            if (value.y < 25) {
              tY = value.y + value.h + 25
            } else {
              tY = value.y - 6
            }
          }
          that.bigCtx.fillText(
            value.txt,
            tX * that.pageImage.imgScale + that.pageImage.imgX,
            tY * that.pageImage.imgScale + that.pageImage.imgY,
          )

          // 回显选中的矩形的病害类型
          that.typeValue = value.type
          that.curRectIndex = i

          // const tempDiseaseArr = that.allDiseaseType.filter((item) => item.engName === value.type)
          // if (tempDiseaseArr.length > 0) {
          //   that.curRectArea = value.area
          //   that.$refs.areaFrame.$el.style.display = 'block'
          //   that.$refs.areaFrame.$el.style.left = `${value.x * that.pageImage.imgScale + that.pageImage.imgX + value.w * that.pageImage.imgScale + 10}px`
          //   that.$refs.areaFrame.$el.style.top = `${value.y * that.pageImage.imgScale + that.pageImage.imgY}px`
          //   that.tipShow = false
          // } else {
          //   that.curRectArea = null
          //   that.onHideAreaFrame()
          // }

          // 显示矩形坐标
          that.rectCoordinate = [value.x.toFixed(1), value.y.toFixed(1), (value.x + value.w).toFixed(1), (value.y + value.h).toFixed(1)]
          // console.log(that.rectCoordinate)
        }
      })
    },
    onHideAreaFrame() {
      if (this.$refs.areaFrame) {
        this.$refs.areaFrame.$el.style.display = 'none'
        this.$refs.areaFrame.$el.style.left = '0px'
        this.$refs.areaFrame.$el.style.top = '0px'
        this.tipShow = false
      }
    },
    onAreaKeyup(event) {
      event.stopPropagation()
      if (this.curRectArea) {
        let str = this.curRectArea.toString()
        str = str
          .replace(/[^\d.]/g, '') // 只能输入数字
          .replace(/\.{2,}/g, '.') // 出现多个点时只保留第一个
        // 第一位不让输小数点
        if (str == '.') {
          str = ''
        }
        // 如果第一位是0，第二位必须大于0或者小数点
        if (str.substring(0, 1) == 0) {
          if (str.substring(1, 2) > 0) {
            str = str.substring(1, 2)
          } else if (
            str.substring(1, 2) === 0
          || str.substring(1, 2) === '0'
          ) {
            str = '0'
          }
        } else {
        // 如果第一位数字大于0（不等于0肯定就大于0），仅需考虑第二位是小数点的情况
          if (str.indexOf('.') !== -1) {
            if (str.substring(0, 1) > 0) {
              console.log('第一位大于0')
            } else {
              console.log('第一位等于0')
              if (str.substring(2, 3) > 0) {
                console.log('小数点后第一位大于0')
              } else {
                console.log('小数点后第一位等于0')
                str = '0.'
              }
            }
          } else {
            console.log('没有小数点，正常输入')
          }
        }
        this.curRectArea = str
      }

      const reg = /^[0-9]+.?[0-9]*$/
      if (reg.test(this.curRectArea) && parseFloat(this.curRectArea) > 0 && parseFloat(this.curRectArea) <= 50) {
        this.onSaveAreaToRect()
        this.tipShow = false
      } else {
        if (this.allStrokeRect[this.curRectIndex].area) {
          delete this.allStrokeRect[this.curRectIndex].area
        }
        this.tipShow = true
      }
    },
    onAreaKeyDown(event) {
      event.stopPropagation()
      if (event.ctrlKey && event.keyCode === 83) {
        this.handleSubmit()
        event.preventDefault()
      }
    },
    // 保存点击到当前选中的标注中
    onSaveAreaToRect() {
      if (this.curRectIndex !== null) {
        this.allStrokeRect[this.curRectIndex].area = this.curRectArea
        this.hasAnnotated = true
      }
    },
    // 快捷键切换标注类型, 保存
    onShortcutKey(e) {
      if (this.loading) return
      const that = this
      // 事件对象兼容
      const e1 = e || window.event
      e1.stopPropagation()

      if (e1.ctrlKey && e1.keyCode === 83) { // 保存 ctrl + s
        that.handleSubmit(false)
        e1.preventDefault()
        e1.returnValue = false
        return
      }

      const tempTypeArr = that.allIdentifyType.filter((item, i) => item.hotkey == e1.key)
      if (tempTypeArr.length > 0) {
        that.handleTypeChange(tempTypeArr[0])
      }

      // 键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40
      if (e1 && e1.keyCode === 37) {
        // 按下左箭头，使用节流函数
        this.throttledPrev()
      } else if (e1 && e1.keyCode === 39) {
        // 按下右箭头，使用节流函数
        this.throttledNext()
      }

      if (e1 && e1.keyCode === 27) {
        that.handleClose()
      }
    },
    // 将26个字母配给标注类型
    onInitKeyCode2Type() {
      const that = this
      that.key2Type = {}
      let initialKeyCode = 64
      that.allDiseaseType.forEach((dType, dIndex) => {
        initialKeyCode += 1
        that.key2Type[initialKeyCode] = dType
      })
      that.allRoadAssetsType.forEach((rType, rIndex) => {
        initialKeyCode += 1
        that.key2Type[initialKeyCode] = rType
      })

      that.allRoadForeignMatter.forEach((mType, mIndex) => {
        initialKeyCode += 1
        that.key2Type[initialKeyCode] = mType
      })
    },
    handleDownloadPic() { // 下载原图
      if (this.dialogDataObj && this.dialogDataObj.pictureUrl) {
        exportPic(this.dialogDataObj.pictureUrl, this.dialogDataObj.picture || this.dialogDataObj.originalImagePath || '未命名图片')
      } else {
        this.$message.error('没有可下载的图片')
      }
    },
    handleDownloadModelPic() {
      // console.log(this.dialogDataObj.id)
      if (this.dialogDataObj && this.dialogDataObj.id) {
        exportFile(`task-photo-remove-dups/downloadSingleXml?id=${this.dialogDataObj.id}`)
      } else {
        this.$message.error('没有可下载的标注数据')
      }
    },
    /**
     * 下载不含中心线的标注图
     */
    downloadWithoutCenterLine() {
      const that = this

      // 检查图像是否加载
      if (!that.img || !that.img.complete) {
        that.$message.error('图像尚未加载完成，请稍后再试')
        return
      }

      this.downWithoutLoading = true

      // 确保图像已完全加载
      const loadImage = (src) => new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous' // 处理跨域
        img.onload = () => resolve(img)
        img.onerror = (e) => reject(e)
        img.src = src
      })

      // 使用Promise处理异步图像加载
      loadImage(that.img.src).then((img) => {
        // 创建一个新的离屏canvas
        const offscreenCanvas = document.createElement('canvas')

        // 检查图像尺寸，如果太大则进行限制（以防止浏览器崩溃）
        const maxSize = 5000 // 最大尺寸限制
        let { width } = img
        let { height } = img
        let scale = 1

        if (width > maxSize || height > maxSize) {
          scale = Math.min(maxSize / width, maxSize / height)
          width *= scale
          height *= scale
        }

        offscreenCanvas.width = width
        offscreenCanvas.height = height
        const offCtx = offscreenCanvas.getContext('2d')

        // 绘制图像 - 根据计算的尺寸
        offCtx.drawImage(img, 0, 0, width, height)

        // 绘制标注框和文字，但不绘制中心线
        if (that.allStrokeRect && that.allStrokeRect.length > 0) {
          // 设置线宽和字体
          offCtx.lineWidth = 3
          offCtx.font = 'bold 22px Arial'

          // 遍历所有标注框并绘制
          that.allStrokeRect.forEach((damage) => {
            // 检查标注框是否有效
            if (!damage || typeof damage.x !== 'number'
                || typeof damage.y !== 'number'
                || typeof damage.w !== 'number'
                || typeof damage.h !== 'number') {
              return // 跳过无效的标注框
            }

            // 设置颜色
            offCtx.strokeStyle = damage.color || '#FF0000' // 默认红色
            offCtx.fillStyle = damage.color || '#FF0000'

            // 应用缩放比例到坐标
            const scaledX = damage.x * scale
            const scaledY = damage.y * scale
            const scaledW = damage.w * scale
            const scaledH = damage.h * scale

            // 绘制矩形框 - 考虑缩放因子
            offCtx.beginPath()
            offCtx.setLineDash([]) // 实线
            offCtx.strokeRect(scaledX, scaledY, scaledW, scaledH)

            // 绘制文字
            const text = damage.txt || '未知'
            const txtW = offCtx.measureText(text).width
            let tX = 0
            let tY = 0

            if (scaledX + txtW > width) {
              offCtx.textAlign = 'right'
              tX = scaledX + scaledW
              if (scaledY < 25) {
                tY = scaledY + scaledH + 25
              } else {
                tY = scaledY - 6
              }
            } else {
              offCtx.textAlign = 'left'
              tX = scaledX
              if (scaledY < 25) {
                tY = scaledY + scaledH + 25
              } else {
                tY = scaledY - 6
              }
            }

            offCtx.fillText(text, tX, tY)
          })
        }

        // 将canvas转换为图片并下载
        try {
          const dataUrl = offscreenCanvas.toDataURL('image/png')
          const downloadLink = document.createElement('a')
          downloadLink.href = dataUrl
          const filename = that.dialogDataObj && (that.dialogDataObj.picture
                          || that.dialogDataObj.originalImagePath)
                          || `标注图_${new Date().getTime()}`
          downloadLink.download = `${filename.split('.')[0]}_标注图.png`
          document.body.appendChild(downloadLink)
          downloadLink.click()
          document.body.removeChild(downloadLink)
          that.$message.success('标注图下载成功')
        } catch (error) {
          console.error('下载失败:', error)
          that.$message.error(`下载失败: ${error.message}`)
        }
      }).catch((error) => {
        console.error('图像加载失败:', error)
        that.$message.error('标注图下载失败，请重试')
      }).finally(() => {
        that.downWithoutLoading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
    // margin-top: 3vh!important;
    margin-top: 0!important;

    &.is-fullscreen {
      overflow: hidden;
    }

    .el-dialog__header {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25)!important;
      position: relative;

      .el-dialog__title {
        font-weight: bold!important;
      }

      .el-dialog__headerbtn {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #9b9b9b!important;
        top: 14px!important;

        .el-dialog__close {
          color: #fff;
          font-weight: bold;
        }
      }
    }

    .el-dialog__body {
      // background: #F5F6F7;
      padding: 30px 0;
    }
    // .el-dialog__headerbtn {
    //   top: 16px!important;
    //   font-size: 24px!important;
    // }
}

.dialog-t {
    display: flex;
    justify-content: space-between;
    padding-right: 30px;
    align-items: flex-end;

   div:nth-child(2){
        font-size: 12px;
        color: #9b9b9b;
    }
}

.dialog-container {
  .dialog-cont {
    display: flex;
    justify-content: space-between;

    .cont-main {
        // width: 80%;
        width: 100%;
        position: relative;

        .operation-instruction {
          margin-bottom: 10px;
        }

        &.cont-main-290 {
          // width: 72%;
          // min-width: calc(100% - 410px);
          // max-width: calc(100% - 410px);
          width: calc(100% - 290px);
        }
        ::v-deep .el-loading-mask {
          background-color: rgba(255, 255, 255, 0.8);
        }
        #bigCanvas {
            // width: 100%;
            // height: auto;
            // aspect-ratio: 16/9;
            // border-radius: 6px;
            border: 1px solid #F5F6F7;
            outline: none;
            background: #F5F6F7;
        }

        .empty-div {
          position: absolute;
          left: 0;
          top: 0;
          z-index: 88;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 18px;
          padding: 0 10px;
        }

        .route-num {
          width: 100px;
          height: 66px;
          position: absolute;
          z-index: 2001;
          right: 30px;
          top: 0;
          background-image: url('../../../assets/route-bg.png');
          background-position: 100% 100%;
          background-size: 100% 100%;

          &>div:first-child {
            width: 100%;
            height: 33px;
            line-height: 38px;
            text-align: center;
            color: #fff;
            font-size: 24px;
            font-weight: bold;
          }

          &>div:last-child {
            width: 100%;
            height: 33px;
            line-height: 31px;
            text-align: center;
            color: #028D75;
            font-size: 18px;
            font-weight: bold;
          }
        }
    }

    .cont-tool {
        // width: 28%;
        // min-width: 410px;
        // max-width: 410px;
        width: 290px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        // padding-bottom: 4px;
        // padding-right: 6px;
        height: auto;
        // margin: 0 20px 20px;

        .cont-tool-big-title {
          font-size: 16px;
          font-weight: bold;
          // padding-top: 5px;
          padding: 0 20px;
        }

        .cont-tool-scroll {
          height: calc(100vh - 104px);
          overflow-y: auto;
          margin-top: 8px;

          &::-webkit-scrollbar-track-piece {
            background: #fff;
          }

          &::-webkit-scrollbar {
            width: 0px;
          }

          &::-webkit-scrollbar-thumb {
            background: #F5F6F7;
            border-radius: 20px;
          }
        }

        .cont-tool-title {
            font-size: 16px;
            margin-top: 0px;
            // margin-bottom: 10px;
            // background: rgb(247, 247, 247);
            padding: 10px 0;
            background: #F5F6F7;
        }

        .el-row {
          margin: 0;
          padding: 0
        }
        .el-col {
          margin-bottom: 6px;
          padding-left: 10px !important;
          padding-right: 10px !important;
        }
        .el-tag {
          width: 100%;
          cursor: pointer;
          text-align: center;
          // padding: 0 5px;

        }
        .el-radio {
            padding: 15px 0;
        }

        .submit-btn {
            width: 100%;
            margin-top: 20px;
        }

        .area-div {
          font-size: 14px;
          padding-left: 10px;
          margin-top: 10px;

          .el-input {
            width: 65%;
          }
        }

        ::v-deep .el-collapse {
          border-color: transparent!important;

          .el-collapse-item__header {
            font-size: 16px;
            border-color: transparent!important;
            background: #F5F6F7;
            height: 30px;
            line-height: 30px;
            padding-left: 20px;
          }

          .el-collapse-item__wrap {
            border-color: transparent!important;
            padding: 10px 20px;
          }

          .el-collapse-item__arrow {
            transform: rotate(-90deg);

            &.is-active {
              transform: rotate(90deg);
            }
          }

          .el-collapse-item__content {
            padding-bottom: 0!important;
          }
        }

    }

    .area-float-frame {
      width: 140px;
      position:absolute;
      left: 0;
      top: 0;
      display: none;
      z-index: 9;
      ::v-deep .el-card__body {
        padding: 10px;
      }

      ::v-deep .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          font-weight:normal;
          line-height: 1.6;
        }

        .el-input__inner {
          padding: 0 10px;
        }

      }

    }
  }

  .cont-main-bottom {
    padding: 20px 10px 20px 0;
    display: flex;
    justify-content: space-between;

    &>div {
      flex-shrink: 0;
    }
    &>div:first-child>div {
      -webkit-touch-callout: text;
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
  }

}

</style>
