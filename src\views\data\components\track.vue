<template>
  <baidu-map
    class="bm-view"
    :center="{lng: 116.404, lat: 39.915}"
    :zoom="8"
    :scroll-wheel-zoom="true"
    @ready="handler"
  >
    <bm-point-collection
      :points="gpsPointList"
      shape="BMAP_POINT_SHAPE_CIRCLE"
      :color="GLOBAL_VARIATE.colorA"
      size="8"
      @mouseover="handleMouseOverpoint"
      @mouseout="handleMouseOutpoint"
    />

    <bm-marker v-if="endPoint.lng" ref="endMarker" :position="endPoint" :icon="{url:require('@/assets/line-end.png'),size:{width:64,height:64}, opts:{imageSize:{width:64,height:64},anchor: {width: 17, height:53}}}" />
    <!-- 起点marker -->
    <bm-marker v-if="startPoint.lng" ref="startMarker" :position="startPoint" :icon="{url:require('@/assets/line-start.png'),size:{width:64,height:64}, opts:{imageSize:{width:64,height:64}, anchor: {width: 17, height: 53}} }" />

    <div v-show="lineInfo.show" class="line-info-div">
      <div>距离：{{ lineInfo.distance }}m</div>
      <div>病害数量：{{ lineInfo.coordinates }}</div>
    </div>
  </baidu-map>
</template>
<script>
import { lightStyleJson } from '@/utils/map-style'

export default {
  props: {
    gpsPointList: {
      type: [],
    },
    page: {
      type: Number,
    },
  },
  data() {
    return {
      map: null,
      BMap: null,
      lineInfo: {
        show: false,
        coordinates: 0,
        distance: 0,
      },
      endPoint: {},
      startPoint: {},
    }
  },
  watch: {
    page(val) {
      console.log('page', val)
      // if (this.map) this.map.setViewport(this.gpsPointList)
    },
    // gpsPointList(val) {

    //   if (this.map) {
    //     console.log('watch gpsPointList', val);
    //     this.map.setViewport(this.gpsPointList)

    //   }
    // }
  },
  methods: {
    handler({ BMap, map }) {
      map.setViewport(this.gpsPointList)
      map.disableKeyboard()
      map.getContainer().style.backgroundColor = '#edf3f3' // 解决加载瓦片跟地图背景不一致
      map.setMapStyleV2({
        styleJson: lightStyleJson,
      })
      this.startPoint = this.gpsPointList[0]
      this.endPoint = this.gpsPointList[this.gpsPointList.length - 1]
      console.log('handler', this.gpsPointList)
      this.map = map
      this.BMap = BMap
    },
    handleMouseOverpoint({ point }) {
      const { lat, lng } = point
      const { coordinates, distance } = this.gpsPointList.find((item) => lat === item.lat && lng === item.lng)

      this.lineInfo.show = true
      this.lineInfo.coordinates = coordinates
      this.lineInfo.distance = distance

      console.log(this.lineInfo.coordinates)
    },
    handleMouseOutpoint() {
      this.lineInfo.show = false
    },
  },
}
</script>
<style lang="scss" scoped>
.bm-view {
  width: 100%;
  height: 100%;
  position: relative;

  .line-info-div {
      position: absolute;
      left: 0;
      top: 0;
      padding: 10px;
      background: rgba(255,255,255, 0.9);
      border-radius: 6px;

      div {
        padding: 5px 0;
        font-size: 12px;
        color: #303133;
        display: flex;
        justify-content: space-between;

        span {
          display: inline-block;
          width: 50%;
          flex-shrink: 0;
        }
      }
    }
}

</style>
