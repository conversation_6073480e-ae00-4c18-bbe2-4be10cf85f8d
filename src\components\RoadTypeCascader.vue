<template>
  <el-cascader
    ref="cascaderRef"
    v-model="innerValue"
    :options="options"
    :props="cascaderProps"
    :placeholder="placeholder"
    :append-to-body="false"
    :show-all-levels="showAllLevels"
    clearable
    filterable
    @change="handleChange"
  />
</template>
<script>
import { ROAD_TYPE_OPTIONS } from '@/utils/cd_constants'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'RoadTypeCascader',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择病害归属/道路'
    },
    forceRefresh: {
      type: Boolean,
      default: false
    },
    showAllLevels: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [], // 初始为空数组，将在updateOptions后更新
      innerValue: null
    }
  },
  computed: {
    ...mapState({
      roadsByBelongTo: state => state.roads.roadsByBelongTo,
      lastFetchTime: state => state.roads.lastFetchTime
    }),
    cascaderProps() {
      return {
        value: 'value',
        label: 'label',
        checkStrictly: true, // 严格模式，确保每个节点都是独立的
        expandTrigger: 'hover'
      }
    }
  },
  created() {
    // 组件创建时获取所有道路数据
    this.loadRoadData()
  },
  watch: {
    value(val) {
      this.innerValue = val
    },
    roadsByBelongTo: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.updateOptions()
        }
      },
      immediate: true
    },
    forceRefresh(val) {
      if (val) {
        this.refreshRoads().then(() => {
          this.$emit('update:forceRefresh', false)
        })
      }
    }
  },
  methods: {
    ...mapActions('roads', ['fetchAllRoads', 'refreshRoads']),
    
    // 加载道路数据
    async loadRoadData() {
      try {
        await this.fetchAllRoads()
      } catch (error) {
        console.error('获取道路数据失败:', error)
      }
    },
    
    // 更新级联选择器选项
    updateOptions() {
      this.options = ROAD_TYPE_OPTIONS.map(item => {

        console.log('item', this.roadsByBelongTo[item.value] || []);
        // 获取当前归属类型的道路数据，并格式化为级联选择器需要的格式
        const roads = (this.roadsByBelongTo[item.value] || []).map(road => ({
          label: road.roadName,
          value: road.roadName,
          leaf: true
        }))

        
        // 如果该归属类型下没有道路数据，添加一个"暂无数据"选项
        if (roads.length === 0) {
          roads.push({
            label: '暂无数据',
            value: '__NO_DATA__', // 使用特殊值标识
            disabled: true
          })
        }
        
        return {
          label: item.label,
          value: item.value,
          children: roads
        }
      })

      console.log('this.options', this.options);
    },
    
    handleChange(val) {
      console.log('val', val);
      // val: [belongTo, roadId]
      
      // 如果选择的是"暂无数据"选项，不触发事件
      if (val[1] === '__NO_DATA__') {
        return;
      }

      this.$emit('change', {
        belongTo: val[0],
        roadName: val[1]
      })

      // 关闭下拉框
      this.$refs.cascaderRef.dropDownVisible = false;
    }
  }
}
</script> 
<style scoped>
::v-deep .el-cascader-menu__wrap {
  min-height: 350px;
}

::v-deep .el-cascader__dropdown {
  width: auto !important;
}

::v-deep .el-scrollbar, 
::v-deep .el-cascader__suggestion-panel {
  width: auto !important;
}

::v-deep .el-cascader__suggestion-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 