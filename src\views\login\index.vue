<template>
  <div class="login-container" :class="title.includes('北京绿道') ? 'login-bjldxj' : ''">
    <div class="login-img">
      <img :src="bj">
      <div class="blue-excessive" />
      <img v-if="title === '数字道路一体化平台'" class="lt-logo" :src="ltLogoBj" alt="">
    </div>
    <div class="login-panel" :class="{'login-panel-center': !qrCodeUrl}">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        :class="{'form-martop68': qrCodeUrl}"
        auto-complete="on"
        label-position="top"
        :style="{marginTop: qrCodeUrl ? '68px' : ''}"
      >
        <div class="title-container">
          <div class="main-title">
            <img
              class="mt-logo"
              :src="logo"
              :class="{
                'logo-fx': title === '峰巡',
                'logo-yly': title === '养路云',
                'logo-xly': title === '巡路云',
                'logo-gj': title === 'AI巡检云平台',
                'logo-ssby': title === '路面智能巡检仪',
                'logo-ssby': title === '路面智能巡检仪',
                'logo-ltsk': title === '数字道路一体化平台',
                'logo-cd': title === '承德市政<br/>道桥养护巡查系统',
              }"
            >
            <div
              class="mt-title"
              :class="{
                'title-ssby': title === '路面智能巡检仪',
                'title-ltsk': title === '数字道路一体化平台',
                'title-cd': title === '承德市政<br/>道桥养护巡查系统',
              }"
              v-html="title"
            >
              <sup>{{ titleSuffix }}</sup>
            </div>
          </div>
          <div class="vice-title">{{ subtitle }}</div>
          <div class="little-title">登录</div>
        </div>

        <el-form-item prop="principal" label="账号">
          <el-input
            ref="username"
            v-model="loginForm.principal"
            placeholder="此处点击输入账号"
            name="username"
            type="text"
            tabindex="1"
            auto-complete="on"
          />
        </el-form-item>

        <el-form-item prop="password" label="密码">
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="此处点击输入密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            @keyup.enter.native="handleLogin"
          />
          <!-- <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span> -->
        </el-form-item>
        <el-form-item v-if="captcha" prop="captcha.response">
          <el-input v-model="loginForm.captcha.response" type="text" placeholder="验证码" @keyup.enter.native="handleLogin" />
          <span class="captcha-container">
            <img :src="'data:image/png;base64,'+captchaInfo.data" @click="getCaptcha">
          </span>
        </el-form-item>
        <el-button
          :loading="loading"
          style="width:100%;margin-bottom:30px;"
          @click.native.prevent="handleLogin"
        >
          登录
        </el-button>
      </el-form>

      <div v-if="qrCodeUrl && !doubleQrcode" class="qrcode-container">
        <div class="qrcode-box">
          <qrcodes :url="qrCodeUrl" :iconurl="qrCodeIcon" :wid="100" :hei="100" :imgwid="30" :imghei="30" />
        </div>

        <div class="qrcode-txt"><svg-icon icon-class="android" /><a @click="handleOpenUrl">Android下载</a></div>
      </div>
      <div v-if="qrCodeUrl && doubleQrcode" class="double-qrcode-container">
        <div>
          <div class="qrcode-box-d">
            <div id="qrcode1" ref="qrcode1" />
            <img :src="qrCodeIcon">
          </div>
          <div class="qrcode-txt-d"><svg-icon icon-class="android" /><a @click="handleOpenUrl">Android下载</a></div>
        </div>
        <div>
          <div class="qrcode-box-d">
            <div id="qrcode2" ref="qrcode2" />
            <img :src="qrCodeIcon">
          </div>
          <div class="qrcode-txt-d"><svg-icon icon-class="ipad" /><a @click="handleOpenPadUrl">Pad下载</a></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import qrcodes from 'vue_qrcodes'
import defaultLogo from '@/assets/logo.png'
import sfLogo from '@/assets/logo-sf.png'
import fxLogo from '@/assets/logo-fx.png'
import ylyLogo from '@/assets/login/logo-yly.png'
import gjLogo from '@/assets/login/logo-gj.png'
import xlyLogo from '@/assets/login/logo-xly.png'
import ssbyLogo from '@/assets/login/logo-ssby.png'
import ltskLogo from '@/assets/login/logo-ltsk.png'
import bjldxjLogo from '@/assets/login/logo-bjldxj.png'
import cdLogo from '@/assets/login/logo-cd.png' // 承德


import defaultBj from '@/assets/login/bj.jpg'
import ssbyBj from '@/assets/login/bj-ssby.jpg'
import ltskBj from '@/assets/login/bj-ltsk.jpg'
import bjldxjBj from '@/assets/login/bj-bjldxj.jpg'
import cdBj from '@/assets/login/bj-cd.jpg'

import ltskLogoBj from '@/assets/login/bj-logo-ltsk.png'

import { getAppDownloadUrl } from '@/api/system'
import QRCode from 'qrcodejs2'

import { captchaVerify, getCaptcha, getPublicKey } from '../../../node_modules/@robu/user/src/api'

export default {
  name: 'Login',
  components: { qrcodes },
  props: {
    captcha: {
      type: Boolean,
      default: false,
    },
    encrypt: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: '智能巡检',
      titleSuffix: 'TM',
      subtitle: '道路智能巡检系统',
      logo: defaultLogo,
      bj: defaultBj,
      ltLogoBj: ltskLogoBj,
      loginForm: {
        principal: null,
        password: null,
        captcha: {
          challenge: '',
          response: '',
        },
        publicKey: '',
      },
      loginRules: {
        principal: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
        'captcha.response': [{ required: this.captcha, message: '验证码不能为空', trigger: 'blur' }],
      },
      captchaInfo: {
        challenge: '', // 本次验证的唯一标识
        data: '', // 图片文件，base64编码
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      qrCodeUrl: '',
      qrCodeIcon: require('../../assets/login/logo.png'),
      doubleQrcode: false,
      padQrCodeUrl: '',
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
  },
  async created() {
    const { hostname, host } = window.location
    if (hostname === 'qinyi.yiluyun.robusoft.cn') {
      this.title = '翌路巡'
      this.logo = sfLogo
    } else if (hostname === 'fengxun.robusoft.cn' || host === '***************:10001') {
      this.title = '峰巡'
      this.logo = fxLogo
      this.qrCodeIcon = require('../../assets/login/logo-fx.png')

      this.getQrcodes()
    } else if (hostname === 'yangluyun.robusoft.cn') {
      this.title = '养路云'
      this.logo = ylyLogo
    } else if (hostname === 'ai.guojiaomap.com') {
      // ai.guojiaomap.com
      this.title = 'AI巡检云平台'
      this.logo = gjLogo
      this.qrCodeIcon = require('../../assets/login/logo-gj.png')

      await this.getQrcodes()
      this.padQrCodeUrl = 'https://www.pgyer.com/nJQN6m'
      this.doubleQrcode = false

      this.setDoubleQrcode()
    } else if (hostname === 'xunluyun.robusoft.cn') {
      // xunluyun.robusoft.cn
      this.title = '巡路云'
      this.logo = xlyLogo
      this.qrCodeIcon = require('../../assets/login/logo-xly.png')

      // this.getQrcodes()
      this.qrCodeUrl = 'https://www.pgyer.com/1z1wKW'
      this.padQrCodeUrl = 'https://www.pgyer.com/Eu3gA2'
      this.doubleQrcode = false
      this.setDoubleQrcode()
    } else if (hostname === 'xunjian.robusoft.cn') {
      // 盛世博业
      this.title = '路面智能巡检仪'
      this.subtitle = ''
      this.logo = ssbyLogo
      this.bj = ssbyBj
      this.qrCodeIcon = require('../../assets/login/logo-ssby.png')
      this.getQrcodes()
    } else if (hostname === 'unicom.robusoft.cn' || host === '***************:10002') {
      // 联通数科
      this.title = '数字道路一体化平台'
      this.titleSuffix = ''
      this.subtitle = ''
      this.bj = ltskBj
      this.logo = ltskLogo
    } else if (host === '***************:10003') {
      // 北京绿道巡检管护平台
      this.title = `北京绿道<br/>巡检管护平台`
      this.titleSuffix = ''
      this.subtitle = ''
      this.bj = bjldxjBj
      this.logo = bjldxjLogo
      this.doubleQrcode = true
      this.qrCodeUrl = 'https://www.pgyer.com/1z1wKW'
      this.padQrCodeUrl = 'https://www.pgyer.com/Eu3gA2'
      this.setDoubleQrcode()
    } else if (host === '***************:10005') {
      // 承德
      this.title = '承德市政<br/>道桥养护巡查系统'
      this.subtitle = ''
      this.logo = cdLogo
      this.bj = cdBj
      this.qrCodeUrl = 'https://www.pgyer.com/7rgNog8Q'
      this.qrCodeIcon = require('../../assets/cd-qricon.png')

    } else {
      this.title = '智能巡检'
      this.subtitle = ''
      this.logo = defaultLogo
      this.getQrcodes()
      

    }
  },
  mounted() {
    if (this.captcha) {
      this.getCaptcha()
    }
    if (this.encrypt) {
      this.getPublicKey()
    }
    this.bodyScale()
    window.addEventListener('resize', () => {
      this.bodyScale()
    })
  },
  destroyed() {
    window.removeEventListener('resize', () => {
      this.bodyScale()
    })
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          if (this.captcha) {
            this.captchaVerify()
          } else {
            this.toLogin()
          }
        }
      })
    },
    toLogin() {
      this.loading = true
      this.$store.dispatch('user/login', this.loginForm).then((res) => {
    
        localStorage.setItem('loginPath', this.$route.path)
        this.$router.push({ path: this.redirect || '/' })
        this.loading = false
      }).catch(() => {
        this.loading = false
        if (this.captcha) {
          this.getCaptcha()
        }
        if (this.encrypt) {
          this.getPublicKey()
        }
      })
    },
    getCaptcha() {
      getCaptcha().then(({ payload }) => {
        this.captchaInfo = payload || {
          challenge: '',
          data: '',
        }
        this.loginForm.captcha.challenge = payload.challenge || ''
      })
    },
    captchaVerify() {
      captchaVerify(this.loginForm.captcha).then((res) => {
        this.toLogin()
      }).catch((error) => {
        if (!error.message.includes('验证码输入错误')) {
          this.getCaptcha()
        }
      })
    },
    getPublicKey() {
      getPublicKey().then(({ payload }) => {
        this.loginForm.publicKey = payload || ''
      })
    },
    bodyScale() {
      const devicewidth = document.documentElement.clientWidth// 获取当前分辨率下的可是区域宽度
      const scale = devicewidth / 1920 // 分母——设计稿的尺寸
      document.body.style.zoom = scale// 放大缩小相应倍数
    },
    async getQrcodes() {
      let workUnit = ''
      switch (this.title) {
        case '峰巡':
          workUnit = '四川永峰科技有限公司'
          break
        case 'AI巡检云平台':
          workUnit = '国交空间信息技术（北京）有限公司'
          break
        case '路面智能巡检仪':
          workUnit = '盛世博业'
          break
        default:
          workUnit = ''
      }

      const params = {
        isCustomized: this.title !== '智能巡检',
        workUnit,
      }
      const { payload } = await getAppDownloadUrl(params)
      this.qrCodeUrl = payload.updateUrl
    },
    handleOpenUrl() {
      window.open(this.qrCodeUrl)
    },
    handleOpenPadUrl() {
      window.open(this.padQrCodeUrl)
    },
    setDoubleQrcode() {
      const that = this
      this.$nextTick(() => {
        if (that.doubleQrcode) {
          const qrcode1 = new QRCode(document.getElementById('qrcode1'), {
            text: this.qrCodeUrl,
            width: 70,
            height: 70,
            colorDark: '#333',
            colorLight: '#fff',
            correctLevel: QRCode.CorrectLevel.H,
          })
          const qrcode2 = new QRCode(document.getElementById('qrcode2'), {
            text: this.padQrCodeUrl,
            width: 70,
            height: 70,
            colorDark: '#333',
            colorLight: '#fff',
            correctLevel: QRCode.CorrectLevel.H,
          })
        }
      })
    },
  },
}
</script>

<style lang="scss">

  $bg: #283443;
  $light_gray: #fff;
  $cursor: #fff;

  @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-container .el-input input {
      color: $cursor;
    }
  }

  /* reset element-ui css */
  .login-container {
    .el-input {
      display: inline-block;
      height: 56px;
      width: 100%;

      input {
        background: #fff;
        border: 0px;
        -webkit-appearance: none;
        border-radius: 4px;
        padding: 12px 5px 12px 15px;
        color: #606266;
        height: 47px;
        caret-color: #606266;

        &:-webkit-autofill {
          box-shadow: 0 0 0px 1000px #fff inset !important;
          -webkit-text-fill-color: #606266 !important;
        }
      }
    }

    .el-form-item {
      border: none;
      background: none;
      border-radius: 0;
      margin-top: 20px;

      .el-form-item__label {
        color: #fff;
        font-size: 14px;
        font-weight: normal;
        line-height: 16px;
        font-family: Alibaba-PuHuiTi-R;

        &::before {
          display: none;
        }
      }
    }
  }
</style>

<style lang="scss" scoped>
  $bg: #2d3a4b;
  $dark_gray: #889aa4;
  $light_gray: #eee;

  .login-container {
    height: 100%;
    // min-height: 100%;
    width: 100%;
    // background-color: $bg;
    overflow: hidden;
    display: flex;

    .login-img {
      width: calc(100% - 574px);
      position: relative;

      .lt-logo {
        position: absolute;
        top: 30px;
        left: 30px;
        width: 267px;
        height: 69px;
        object-fit: cover;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .blue-excessive {
        width: 84px;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(1, 73, 124, .3);
        z-index: 1;
      }
    }

    .login-panel{
      width: 574px;
      background: #01497C;
    }

    .login-panel-center {
      display: flex;
      align-items: center;
      padding-bottom: 20px;
    }

    .login-form {
      position: relative;
      width: 412px;
      max-width: 100%;
      overflow: hidden;
      margin: 0 auto;
      .form-martop68 {
        margin-top: 68px;
      }
      .el-button {
        height: 46px;
        margin-top: 38px;
        color: #01497C;
        font-size: 16px;
        font-family: Alibaba-PuHuiTi-R;

        &:hover {
          background: #fff;
        }
      }
    }

    .tips {
      font-size: 14px;
      color: #fff;
      margin-bottom: 10px;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
    }

    .title-container {
      position: relative;
      color: #fff;
      font-family: Alibaba-PuHuiTi-B;

      .main-title {
        font-size: 52px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        .title-ssby {
          font-size: 43px;
        }
        .title-ltsk {
          font-size: 38px;
        }
        .title-cd {
          font-size: 35px;
        }

        img {
          display: block;
          width: 49px;
          height: 46px;
          margin-right: 22px;

          &.logo-fx {
            height: 49px;
            margin-right: 12px;
          }
          &.logo-yly {
            width: 60px;
            height: auto;
          }
          &.logo-xly {
            height: 49px;
            border-radius: 4px;
          }

          &.logo-gj {
            width: 36px;
            height: 36px;
            border-radius: 4px;
          }
          &.logo-ssby {
            width: 36px;
            height: 36px;
            border-radius: 4px;
          }

          &.logo-ltsk {
            width: 47px;
            height: 28px;
            border-radius: 4px;
          }
          &.logo-cd {
            width: 75px;
            height: 75px;
            border-radius: 4px;
            background-color: #fff;
            border: 1px solid #fff;
          }
        }
        sup {
          font-size: 25px;
          top: -1.3rem;
          left: 10px;
        }
      }

      .vice-title {
        font-size: 36px;
      }

      .little-title {
        font-size: 32px;
        margin-top: 80px;
      }
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .captcha-container {
      position: absolute;
      right: 0px;
      top: 2px;
      cursor: pointer;

      img {
        width: 100px;
        height: 43px;
      }
    }

    .qrcode-container {
      width: 412px;
      height: 104px;
      display: flex;
      align-items: center;
      margin: 96px auto 0;

      .qrcode-box {
        width: 104px;
        height: 104px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 106px;
        border-radius: 5px;
        background: #fff;

      }

      .svg-icon {
        width: 30px;
        height: 30px;
        margin-right: 16px;
      }

      .qrcode-txt {
        width: 231px;
        height: 49px;
        background: #fff;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 33px;
        color: #01497c;
        font-weight: bold;

        a:hover {
          text-decoration: underline;
        }
      }
    }

    .double-qrcode-container {
      width: 412px;
      // height: 104px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 86px auto 0;

      &>div {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .qrcode-box-d {
        width: 74px;
        height: 74px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 106px;
        border-radius: 5px;
        background: #fff;
        position: relative;

        img {
          position: absolute;
          display: inline-block;
          width: 30px;
          height: 30px;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .svg-icon {
        width: 35px;
        height: 35px;
        margin-right: 16px;
      }

      .qrcode-txt-d {
        width: 192px;
        height: 49px;
        background: #fff;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #01497c;
        font-weight: bold;
        margin-top: 20px;

        img {
          display: inline-block;
          width: 32px;
          height: 39px;
          margin-right: 15px;
        }

        a:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .login-bjldxj {
    .login-img .blue-excessive {
      background-color: rgba(1, 124, 91, 0.3) !important;
    }
    .login-panel {
      background: #017C5B;
    }
    .main-title {
      .mt-logo {
        width: 90px !important;
        height: auto !important;
      }
      .mt-title {
        font-size: 50px;
      }
    }
    .double-qrcode-container {
      .qrcode-txt-d {
        color: #333333;
      }
    }
  }
</style>
