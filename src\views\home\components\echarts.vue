<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-06-15 15:10:52
 * @LastEditors: <PERSON><PERSON>j
 * @LastEditTime: 2023-06-19 14:18:03
 * @Description:
-->
<template>
  <div :id="id" :data="data" style="width: 100%;height: 100%;" />
</template>

<script>
export default {
  props: ['id', 'data'],
  data() {
    return {
      chartGraph: null,
    }
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.drawGraph(this.id, newVal)
      },
      deep: true,
    },
  },
  mounted() {
    this.drawGraph(this.id, this.data)
  },
  beforeDestroy() {
    if (this.chartGraph) {
      this.chartGraph.clear()
    }
  },
  methods: {
    drawGraph(id, data) {
      const that = this
      const myChart = document.getElementById(id)
      if (myChart) {
        that.chartGraph = that.$echarts.init(myChart)
        that.chartGraph.setOption(data)
        window.addEventListener('resize', () => {
          that.chartGraph.resize()
        })
      }
    },
  },
}
</script>

<style>
</style>
