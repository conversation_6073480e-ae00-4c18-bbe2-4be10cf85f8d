<!--
 * @Author: guowy
 * @Date: 2020-08-26 14:22:32
 * @LastEditors: guowy
 * @LastEditTime: 2020-10-14 10:58:25
-->
<template>
  <el-date-picker
    v-model="rangeDate"
    :picker-options="getPickerOptions()"
    :editable="false"
    type="daterange"
    unlink-panels
    value-format="yyyy-MM-dd"
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
  />
</template>

<script>
import { parseTime } from '@/utils/formatDate'

export default {
  name: 'DateRange',
  props: {
    // 是否限制日期选择
    disabledDate: {
      type: Boolean,
      default: true,
    },
    // 是否禁止选择今天之前的日期
    disabledYestoday: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rangeDate: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          },
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          },
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          },
        }],
      },
    }
  },
  watch: {
    rangeDate(val) {
      this.$emit('dateRangeArr', [val === null ? null : val[0], val === null ? null : val[1]])
    },
  },
  methods: {
    setData(startTime, endTime) {
      const s = parseTime(startTime, '{y}-{m}-{d}')
      const e = parseTime(endTime, '{y}-{m}-{d}')
      this.rangeDate = [s, e]
    },
    reset() {
      this.rangeDate = null
    },
    getPickerOptions() {
      const disabledDate = (time) => time.getTime() > Date.now() - 8.64e6
      const disabledYestoday = (time) => time.getTime() < new Date(new Date(new Date().toLocaleDateString()).getTime())

      if (this.disabledDate) {
        if (this.disabledYestoday) {
          this.pickerOptions.disabledDate = disabledYestoday
        } else {
          this.pickerOptions.disabledDate = disabledDate
        }

        return this.pickerOptions
      }
      return {}
    },
  },
}
</script>
