/**
 * 存储地图点的信息
 */
export const markerData = {
  index: 0,
  currentCacheType: '',
  markerClusterers: { //  地图的点聚合图层
    all: null,
    disease: null, // 病害
    facility: null, // 设施
    risk: null, // 风险
    assets: null, // 资产
  },
  markers: { // marker点
    all: [],
    disease: [],
    facility: [],
    risk: [],
    assets: [],
  },
  caches: { // 缓存的infowindow信息
    all: {},
    disease: {},
    facility: {},
    risk: {},
    assets: {},
  },
  mapTypes: {
    all: 'all',
    1: 'disease',
    2: 'assets',
    3: 'risk',
    5: 'facility',
  },
}

/**
 * 给markerData的markers
 * @param {string} key markerData markers[key]
 * @param {number} currentIndex 在当前类型所在的位置
 * @param {object} tag 赋值的对象
 * @param {BMap.Marker} marker 百度地图的点
 * @param {number} allIndex 在全部的所在的位置
 */
export function setMarkerData({
  key, currentIndex, tag, marker, allIndex, allMarkers,
}) {
  tag.allIndex = allIndex
  tag.currentIndex = currentIndex
  marker.tag = tag
  allMarkers.tag = tag
  markerData.markers[key].push(marker)
  markerData.markers.all.push(allMarkers)
}

/**
 * 清空markerData的markers和caches
 */
export function resetMarkerData() {
  Object.keys(markerData.markers).forEach((key) => markerData.markers[key] = [])
  Object.keys(markerData.caches).forEach((key) => markerData.caches[key] = {})
  Object.keys(markerData.markerClusterers).forEach((key) => {
    const markerClusterers = markerData.markerClusterers[key]
    if (markerClusterers) {
      markerClusterers.clearMarkers()
    }
  })
  markerData.index = 0
}

/**
 * 设置地图infowindow的视野范围  必须得关闭infowindow的autoPan 属性
 * @param {BMapMap} map 百度地图实例
 * @param {BMapPoint} point 经纬度
 * @param {boolean} leftShow  左侧slide的显隐
 * @param {boolean} rightShow 右侧slide的显隐
 */
export function setInfoWindowViewport(map, point, leftShow, rightShow, option) {
  const mapContainer = map.getContainer()
  const mapWidth = mapContainer.clientWidth
  const mapHeight = mapContainer.clientHeight

  // 转换像素坐标
  const { x, y } = map.pointToPixel(point)

  // 定义范围，如果点的坐标超出这个范围，就需要偏移地图
  const left = leftShow ? 720 : 360
  const right = rightShow ? mapWidth - 820 : mapWidth - 460
  const top = 535
  const bottom = mapHeight - 15

  // 如果 x 和 y 都在范围内，直接返回
  if (left <= x && x <= right && top <= y && y <= bottom) return
  // 如果 点不在地图的可视区域内，将地图的中心设置为该点
  if (!map.getBounds().containsPoint(point)) {
    map.setCenter(point)
    setInfoWindowViewport(map, point, leftShow, rightShow)
    // console.log('弹出的点不在地图的可视区域内')
    return
  }

  let offsetX = 0; let
    offsetY = 0

  // 如果 x 小于左边界，偏移量为左边界减去 x
  if (x < left) offsetX = left - x
  // 如果 x 大于右边界，偏移量为右边界减去 x 的负数
  else if (x > right) offsetX = -(x - right)

  // 如果 y 小于上边界，偏移量为上边界减去 y
  if (y < top) offsetY = top - y
  // 如果 y 大于下边界，偏移量为下边界减去 y 的负数
  else if (y > bottom) offsetY = -(y - bottom)

  map.panBy(offsetX, offsetY) // https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html
}

/**
 * 设置跑马灯动画的执行顺序
 */
export function setMarqueeAnimation() {
  // function checkAnimationProgress(callback) {
  //   const animationDuration = 5 * 1000; // 动画执行事件 跟css相匹配
  //   let animationStartTime
  //   // 递归调用requestAnimationFrame，检查动画进度
  //   function checkProgress(timestamp) {
  //     // 如果animationStartTime为undefined，设置为当前时间戳
  //     if (!animationStartTime) {
  //       animationStartTime = timestamp;
  //     }
  //     // 计算动画已经经过的时间
  //     const elapsedTime = timestamp - animationStartTime;
  //     // 计算动画的进度（百分比）
  //     const progress = (elapsedTime / animationDuration) * 100;
  //     // 如果动画进度超过，触发回调函数
  //     if (progress >= 90) {
  //       callback();
  //     } else {
  //       // 否则，继续递归调用requestAnimationFrame
  //       requestAnimationFrame(checkProgress);
  //     }
  //   }
  //   // 初始调用
  //   requestAnimationFrame(checkProgress);
  // }

  const marqueeElement1 = document.getElementById('car1')
  const marqueeElement2 = document.getElementById('car2')
  const marqueeElement3 = document.getElementById('car3')
  const marqueeElement4 = document.getElementById('car4')
  marqueeElement1.classList.add('car_start')
  // marqueeElement1.addEventListener('animationstart', () => {
  //   // console.log('1111111111111111');
  //   checkAnimationProgress(() => {
  //     marqueeElement2.classList.add('car_start');
  //   });
  // });
  // marqueeElement2.addEventListener('animationstart', () => {
  //   // console.log('22222222222');

  //   checkAnimationProgress(() => {
  //     marqueeElement3.classList.add('car_end');
  //   });
  // });
  // marqueeElement3.addEventListener('animationstart', () => {
  //   // console.log('3333333333333');
  //   checkAnimationProgress(() => {
  //     marqueeElement4.classList.add('car_start');
  //   });
  // });
  // marqueeElement4.addEventListener('animationstart', () => {
  //   // console.log('44444444444444');
  //   checkAnimationProgress(() => {
  //     marqueeElement1.classList.add('car_start');
  //   });
  // });

  marqueeElement1.addEventListener('animationend', () => {
    marqueeElement1.classList.remove('car_start')
    marqueeElement2.classList.add('car_start')
  })
  marqueeElement2.addEventListener('animationend', () => {
    marqueeElement2.classList.remove('car_start')
    marqueeElement3.classList.add('car_end')
  })
  marqueeElement3.addEventListener('animationend', () => {
    marqueeElement3.classList.remove('car_end')
    marqueeElement4.classList.add('car_start')
  })
  marqueeElement4.addEventListener('animationend', () => {
    marqueeElement4.classList.remove('car_start')
    marqueeElement1.classList.add('car_start')
  })
}
