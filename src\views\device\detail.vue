<!--
 * @Author: guowy
 * @Date: 2020-07-14 18:30:32
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-14 22:11:47
-->
<template>
  <div class="app-container">
    <el-row>
      <el-col :span="12"><p>id：{{ device.id }}</p></el-col>
      <el-col :span="12"><p>用户id：{{ device.userId }}</p></el-col>
      <el-col :span="12"><p>设备关键：{{ device.deviceKey }}</p></el-col>
      <el-col :span="12"><p>设备名称：{{ device.deviceName }}</p></el-col>
      <el-col :span="12"><p>设备类型：{{ device.deviceType }}</p></el-col>
      <el-col :span="12"><p>创建时间：{{ device.createTime }}</p></el-col>
      <el-col :span="12"><p>更新时间：{{ device.updateTime }}</p></el-col>
      <el-col :span="12"><p>删除：{{ device.deleted }}</p></el-col>
    </el-row>
  </div>
</template>
<script>
import { getDevice } from '@/api/device'

export default {
  props: {
    id: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      device: {},
    }
  },
  methods: {
    getData() {
      const id = this.$route.params && this.$route.params.id
      getDevice(id).then((res) => {
        this.device = res.payload
      })
    },
  },
}
</script>
