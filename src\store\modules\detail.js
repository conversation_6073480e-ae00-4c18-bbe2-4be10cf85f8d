import Vue from 'vue'
import { getDataDetail } from '@/api/data'

const state = {
  taskDataObj: {},
}
const mutations = {
  SET_TASK_DATA_OBJ: (state, data) => {
    state.taskDataObj[data.id] = data
  },
  DEL_TASK_DATA_OBJ: (state, key) => {
    if (key === 'all') {
      // 全部置空
      state.taskDataObj = {}
      return
    }
    if (state.taskDataObj[key]) Vue.delete(state.taskDataObj, key)
  },
}
const actions = {
  setTaskData({ commit }, id) {
    return new Promise((resolve, reject) => {
      getDataDetail(id).then((response) => {
        const { payload } = response
        if (!payload || !payload.id) {
          reject(new Error('任务数据不完整或不存在'))
          return
        }
        commit('SET_TASK_DATA_OBJ', payload)
        resolve(payload)
      }).catch((error) => {
        reject(error)
      })
    })
  },

}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
