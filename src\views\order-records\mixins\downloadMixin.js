import { exportOrderRecordBatch } from '@/api/order-records'
import { exportFile, parseContentDisposition } from '@/utils/index'
import _ from 'lodash'

export default {
  methods: {
    /**
     * 下载单个或多个巡查记录
     * @param {Array|Number} ids 巡查记录ID或ID数组
     * @param {Number} downloadCount 下载数量，用于显示消息
     * @param {Object} options 下载选项
     * @param {Boolean} options.isSelectAll 是否全选
     * @param {Array} options.ignoredIds 忽略的ID数组（全选模式下使用）
     * @param {Object} options.cloneListQuery 查询条件（全选模式下使用）
     */
    downloadRecords(ids, downloadCount = 1, options = {}) {
      // 创建唯一的下载任务ID
      const taskId = `download-${Date.now()}-${Math.floor(
        Math.random() * 1000
      )}`

      // 创建下载通知
      const notificationMessage = `正在下载${downloadCount}条巡查记录，请耐心等待...`
      const notification = this.$notify({
        title: '下载进行中',
        message: notificationMessage,
        duration: 0, // 不自动关闭
        position: 'top-right',
        iconClass: 'el-icon-loading',
        id: taskId,
      })

      // 准备请求参数
      let params = {}
      
      if (options.isSelectAll) {
        // 全选模式
        params = {
          allSelected: true,
          ignoredIds: options.ignoredIds || [],
          ...options.cloneListQuery
        }
      } else {
        // 部分选择模式，确保ids始终是数组
        const recordIds = Array.isArray(ids) ? ids : [ids]
        params = {
          selectedIds: recordIds
        }
      }
      
      // 清理空值
      params = _.pickBy(params, (value) => {
        if (value === undefined || value === null || value === '') return false
        if (Array.isArray(value) && value.length === 0) return false
        return true
      })
      console.log('params', params)
      // 调用导出API
      exportOrderRecordBatch(params)
        .then(async (res) => {
          const url = URL.createObjectURL(res.payload)
          const contentDisposition = res.headers['content-disposition']
          const fileName =
            parseContentDisposition(contentDisposition) || '巡检记录.xlsx'

          await exportFile(url, fileName)

          // 关闭之前的通知
          notification.close()

          // 显示下载成功通知
          this.$notify({
            title: '下载成功',
            message: `文件：${fileName}已下载完成`,
            type: 'success',
            duration: 5000,
            position: 'top-right',
            id: taskId,
          })
        })
        .catch((error) => {
          console.error('下载失败', error)

          // 关闭之前的通知
          notification.close()

          // 显示下载失败通知
          this.$notify({
            title: '下载失败',
            message: error.statusText || '请稍后重试',
            type: 'error',
            duration: 5000,
            position: 'top-right',
            id: taskId,
          })
        })
    },
    
    /**
     * 处理单个记录的导出
     * @param {Number} id 记录ID
     */
    handleExport(id) {
      // 单个导出，使用批量导出接口
      this.downloadRecords(id, 1)
    }
  }
} 