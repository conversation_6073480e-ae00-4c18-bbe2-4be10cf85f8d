/*
 * @Author: wangyj
 * @Date: 2022-08-02 11:16:36
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-03 17:11:55
 */
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="12"><p>aiboxId：{{ detailData.aiboxId }}</p></el-col>
      <el-col :span="12"><p>boot_start_time：{{ detailData.bootStartTime }}</p></el-col>
      <el-col :span="12"><p>createTime：{{ detailData.createTime }}</p></el-col>
      <el-col :span="12"><p>damageCount：{{ detailData.damageCount }}</p></el-col>
      <el-col :span="12"><p>damageList：{{ detailData.damageList }}</p></el-col>
      <el-col :span="12"><p>deviceId：{{ detailData.deviceId }}</p></el-col>
      <el-col :span="12"><p>frameFileHasSent：{{ detailData.frameFileHasSent }}</p></el-col>
      <el-col :span="12"><p>frameMetaHasSent：{{ detailData.frameMetaHasSent }}</p></el-col>
      <el-col :span="12"><p>gpsLatitude：{{ detailData.gpsLatitude }}</p></el-col>
      <el-col :span="12"><p>gpsLatitudeTrans：{{ detailData.gpsLatitudeTrans }}</p></el-col>
      <el-col :span="12"><p>gpsLongitude：{{ detailData.gpsLongitude }}</p></el-col>
      <el-col :span="12"><p>gpsLongitudeTrans：{{ detailData.gpsLongitudeTrans }}</p></el-col>
      <el-col :span="12"><p>gpsSpeed：{{ detailData.gpsSpeed }}</p></el-col>
      <el-col :span="12"><p>hasSent：{{ detailData.hasSent }}</p></el-col>
      <el-col :span="12"><p>id：{{ detailData.id }}</p></el-col>
      <el-col :span="12"><p>imageFrame：{{ detailData.imageFrame }}</p></el-col>
      <el-col :span="12"><p>imageFrameName：{{ detailData.imageFrameName }}</p></el-col>
      <el-col :span="12"><p>imageSize：{{ detailData.imageSize }}</p></el-col>
      <el-col :span="12"><p>inferTimestamp：{{ detailData.inferTimestamp }}</p></el-col>
      <el-col :span="12"><p>jpeg：{{ detailData.jpeg }}</p></el-col>
      <el-col :span="12"><p>localImageDir：{{ detailData.localImageDir }}</p></el-col>
      <el-col :span="12"><p>objectStorageUrlPrefix：{{ detailData.objectStorageUrlPrefix }}</p></el-col>
      <el-col :span="12"><p>originalImagePath：{{ detailData.originalImagePath }}</p></el-col>
      <el-col :span="12"><p>photoTime：{{ detailData.photoTime }}</p></el-col>
      <el-col :span="12"><p>remoteImageDir：{{ detailData.remoteImageDir }}</p></el-col>
      <el-col :span="12"><p>resultImagePath：{{ detailData.resultImagePath }}</p></el-col>
      <el-col :span="12"><p>scheduler：{{ detailData.scheduler }}</p></el-col>
      <el-col :span="12"><p>timestrMinute：{{ detailData.timestrMinute }}</p></el-col>
      <el-col :span="12"><p>tryCount：{{ detailData.tryCount }}</p></el-col>
      <el-col :span="12"><p>tryFileCount：{{ detailData.tryFileCount }}</p></el-col>
      <el-col :span="12"><p>tryMetaCount：{{ detailData.tryMetaCount }}</p></el-col>
      <el-col :span="12"><p>zAcclspeed：{{ detailData.zAcclspeed }}</p></el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: 'SystemDetail',
  data() {
    return {
      detailData: {},
    }
  },
  activated() {
    this.detailData = JSON.parse(sessionStorage.getItem('detailData'))
    // this.detailData = JSON.parse(this.$route.params.detailData)
  },
  methods: {

  },
}
</script>
<style lang="scss" scode>
  p {
    word-break: break-all;
  }
</style>
