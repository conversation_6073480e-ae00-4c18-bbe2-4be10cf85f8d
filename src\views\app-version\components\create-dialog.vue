/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by: wangyj
 * @Last Modified time: 2023-03-03 10:06:15
 */

<template>
  <el-dialog :title="dialogType==='edit'?'修改app版本':'添加app版本'" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="是否为定制版" prop="isCustomized">
        <el-radio-group v-model="temp.isCustomized" @input="handleChange">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="temp.isCustomized" label="定制单位" prop="userId">
        <el-select v-model="temp.userId" placeholder="请选择" style="width: 100%">
          <el-option
            v-for="item in unitOptions"
            :key="item.id"
            :label="item.workUnit"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最新版本号" prop="latestVersionName">
        <el-input v-model="temp.latestVersionName" placeholder="请输入版本号格式,例如1.0.0" />
      </el-form-item>
      <el-form-item label="下载地址" prop="updateUrl">
        <el-input v-model="temp.updateUrl" placeholder="请输入下载地址" />
      </el-form-item>
      <el-form-item label="更新内容" prop="description">
        <el-input v-model="temp.description" placeholder="请输入更新内容" :rows="5" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createAppVersion, updateAppVersion, getCustomUnitList } from '@/api/system'
import { validateUrl } from '@/utils/validate'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        isCustomized: [{ required: true, message: '请选择是否为定制版', trigger: 'blur' }],
        userId: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        latestVersionName: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        updateUrl: [{ required: true, message: '请输入下载地址', trigger: 'blur' }, { validator: validateUrl, trigger: 'blur' }],
      },
      unitOptions: [],
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }
        if (this.formData.id) {
          this.dialogType = 'edit'
          this.rules.userId[0].required = this.temp.isCustomized
        } else {
          this.$set(this.temp, 'isCustomized', true)
          this.rules.userId[0].required = this.temp.isCustomized
          this.dialogType = 'add'
        }
        this.initUnitOptions()
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          createAppVersion(tempData).then((res) => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          updateAppVersion(tempData).then(() => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
    handleChange(value) {
      this.rules.userId[0].required = value
    },
    async initUnitOptions() {
      const { payload } = await getCustomUnitList()
      this.unitOptions = payload
    },
  },
}
</script>
