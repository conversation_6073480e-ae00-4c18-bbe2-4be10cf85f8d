{"aiRules": ["这是一个Vue 2.x项目，使用Element UI组件库", "遵循项目现有的代码风格：使用单引号，行尾无分号，缩进为2个空格", "所有Vue组件使用.vue文件扩展名，包含template、script和style标签", "组件命名使用PascalCase（首字母大写驼峰式）", "views目录用于页面组件，components目录用于可复用组件", "使用Vuex进行状态管理，store目录包含modules结构", "API请求通过src/api目录中的服务模块进行", "路由定义在router目录，遵循模块化组织", "使用ESLint规则：基于airbnb-base配置，最大行长度120字符", "样式使用SCSS预处理器，文件放在styles目录或组件内的style标签中", "使用@作为src目录的别名，例如@/components", "项目涉及地图应用，包含百度地图相关组件", "组件间通信优先使用props和events，复杂状态使用Vuex", "所有新建文件头部需添加作者和日期等注释信息", "路由权限控制通过permission.js实现", "项目中的所有icon使用SvgIcon组件来实现", "表单验证使用@robu/validator库"], "codeStyle": {"indentation": "spaces", "tabWidth": 2, "lineLength": 120, "quotes": "single", "semicolons": false, "trailingComma": "always-multiline"}, "projectStructure": {"viewsDir": "src/views", "componentsDir": "src/components", "apiDir": "src/api", "storeDir": "src/store", "routerDir": "src/router", "stylesDir": "src/styles", "layoutDir": "src/layout", "utilsDir": "src/utils"}}