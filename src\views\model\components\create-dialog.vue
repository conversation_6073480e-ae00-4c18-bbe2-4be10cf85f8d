/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:02:28
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-09 15:20:37
 */

<template>
  <el-dialog
    :title="dialogType==='edit'?'修改模型':'添加模型'"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="识别类型" prop="applyType">
        <el-select v-model="temp.applyType" placeholder="请选择识别模型" style="width: 100%">
          <el-option :value="1" label="图像" />
          <el-option :value="2" label="视频" />
        </el-select>
      </el-form-item>
      <el-form-item label="模型类型" prop="modelType">
        <el-select v-model.number="temp.modelType" placeholder="请选择模型类型" style="width: 100%">
          <el-option v-for="(value, key, index) in Model_Cn_Name" :key="key" :value="parseInt(key)" :label="value" />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="versionName">
        <el-input v-model="temp.versionName" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="API地址" prop="apiUrl">
        <el-input v-model="temp.apiUrl" placeholder="请输入算法API地址" />
      </el-form-item>
      <el-form-item label="更新说明" prop="description">
        <el-input
          v-model="temp.description"
          type="textarea"
          :rows="3"
          placeholder="请输入更新说明"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addModel, editModel } from '@/api/model'
import ConstantsMixin from '@/mixin/constants'

export default {
  mixins: [ConstantsMixin],
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        applyType: [{ required: true, message: '请选择识别类型', trigger: 'blur' }],
        modelType: [{ required: true, message: '请选择模型类型', trigger: 'blur' }],
        versionName: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        apiUrl: [{ required: true, message: '请输入API地址', trigger: 'blur' }],
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.formData }

        if (this.formData.id) {
          this.dialogType = 'edit'
        } else {
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
    },
  },
  methods: {
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          addModel(tempData).then((res) => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          delete tempData.confirmPassword
          editModel(tempData).then(() => {
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
  },
}
</script>
