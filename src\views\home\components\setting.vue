<template>
  <el-dialog title="显示项目设置" :visible.sync="dialogVisible" width="34%">
    <div class="main">
      <el-tabs v-model="activeName">
        <el-tab-pane label="沥青路面病害" name="pitchData" />
        <el-tab-pane label="水泥路面病害" name="cementData" />
        <el-tab-pane label="砂石路面病害" name="sandstoneData" />
        <el-tab-pane label="慢行病害" name="slowdownData" />
        <el-tab-pane label="路面风险" name="roadForeignMatterData" />
        <el-tab-pane label="沿线设施损坏" name="alongLineData" />
        <el-tab-pane label="道路资产" name="roadAssetsData" />
      </el-tabs>

      <div v-for="(checkbox, dataType) in checkboxList" :key="dataType">
        <div v-show="activeName === dataType">
          <el-row :gutter="10">
            <el-col :span="6" style="margin-bottom: 6px">
              <el-checkbox
                v-model="checkbox.checkAll"
                :indeterminate="checkbox.isIndeterminate"
                @change="handleCheckAllChange($event, checkbox.dataType)"
              >
                全选
              </el-checkbox>
            </el-col>
            <el-checkbox-group
              v-model="checkbox.checked"
              @change="handleCheckedChange($event, checkbox.dataType)"
            >
              <el-col v-for="item in checkbox.types" :key="item.id" :span="6" style="margin-bottom: 6px">
                <el-checkbox :label="item.engName">
                  {{
                    item.chineseName
                  }}
                </el-checkbox>
              </el-col>
            </el-checkbox-group>
          </el-row>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <span>
        <el-input v-if="activeName === 'pitchData'" v-model="checkboxList.pitchData.area" type="number" style="width: 200px">
          <template slot="prepend">面积 ≥</template>
          <template slot="append">㎡</template>
        </el-input>
        <el-input v-else-if="activeName === 'cementData'" v-model="checkboxList.cementData.area" type="number" style="width: 200px">
          <template slot="prepend">面积 ≥</template>
          <template slot="append">㎡</template>
        </el-input>
      </span>
      <span>
        <el-button @click="handelClose">取 消</el-button>
        <el-button type="primary" @click="handelSubmit">确 定</el-button>
      </span>
    </span>
  </el-dialog>
</template>

<script>
// import CanvasMixin from '@/mixin/canvas'
export default {
  // mixins: [CanvasMixin],
  props: {
    allIdentifyType: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      activeName: 'pitchData',
      concretePavementList: [],
      checked: [],
      area: 2,
      checkboxList: {
        pitchData: {
          // 沥青路面
          roadType: 1,
          classification: 1,
          dataType: 'pitchData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: 2,
        },
        cementData: {
          // 水泥路面
          roadType: 2,
          classification: 1,
          dataType: 'cementData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: 2,
        },
        sandstoneData: {
          // 砂石路面
          roadType: 13,
          classification: 1,
          dataType: 'sandstoneData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: 2,
        },
        slowdownData: {
          // 慢行路面
          roadType: 9,
          classification: 9,
          dataType: 'slowdownData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: null,
        },
        roadAssetsData: {
          // 道路资产
          roadType: null,
          classification: 2,
          dataType: 'roadAssetsData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: null,
        },
        roadForeignMatterData: {
          // 路面风险
          roadType: null,
          classification: 3,
          dataType: 'roadForeignMatterData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: null,
        },
        alongLineData: {
          // 沿线设施损坏
          roadType: null,
          classification: 5,
          dataType: 'alongLineData',
          types: [],
          checkAll: false,
          checkAllList: [],
          checked: [], // 选中的
          isIndeterminate: true,
          area: null,
        },
      },
    }
  },
  watch: {
    allIdentifyType() {
      this.initCheckboxList()
    },
  },
  methods: {
    initCheckboxList() {
      const that = this
      Object.keys(this.checkboxList).forEach((key) => {
        // 根据roadType与classification筛选
        const { roadType, classification } = this.checkboxList[key]
        const data = that.allIdentifyType.filter((item) => (item.classification === classification && (!roadType || item.roadType === roadType)))
        this.checkboxList[key].types = data
        this.checkboxList[key].checkAllList = data.map((item) => item.engName)
      })
    },
    handelSubmit() {
      const params = {
        types: [],
        area: null,
      }
      for (const checkbox in this.checkboxList) {
        const tempObj = this.checkboxList[checkbox]
        if (tempObj.checked.length) {
          params.types = tempObj.checked
          params.area = tempObj.area
          break
        }
      }
      if (params.types.length === 0 && (this.activeName === 'pitchData' || this.activeName === 'cementData')) {
        params.area = this.checkboxList[this.activeName].area
      }

      this.$emit('settingParams', params)
      this.dialogVisible = false
    },
    handelClose() {
      this.dialogVisible = false
      this.activeName = 'pitchData'
    },
    handleCheckAllChange(val, typeData) {
      for (const checkbox in this.checkboxList) {
        const tempObj = this.checkboxList[checkbox]
        if (typeData !== checkbox && tempObj.checked.length) {
          tempObj.checked = []
          tempObj.checkAll = false
          if (tempObj?.area) tempObj.area = 0
        }
      }

      this.checkboxList[typeData].checked = val
        ? this.checkboxList[typeData].checkAllList
        : []
      this.checkboxList[typeData].isIndeterminate = false
    },
    handleCheckedChange(value, typeData) {
      for (const checkbox in this.checkboxList) {
        const tempObj = this.checkboxList[checkbox]
        if (typeData !== checkbox && tempObj.checked.length) {
          tempObj.checked = []
          tempObj.checkAll = false
          if (tempObj?.area) tempObj.area = 0
        }
      }

      if (this.oldActiveName) this.checkboxList[this.oldActiveName].checked = []

      const checkedCount = value.length
      this.checkboxList[typeData].checkAll = checkedCount === this.checkboxList[typeData].checkAllList.length
      this.checkboxList[typeData].isIndeterminate = checkedCount > 0
        && checkedCount < this.checkboxList[typeData].checkAllList.length
    },
  },
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  height: 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .el-tabs__item {
  padding: 0 8px !important;
}
::v-deep .el-dialog__body {
  padding: 5px 20px 10px!important;
}
::v-deep .el-checkbox__inner::before {
  content: "";
  display: none;
}
::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border: 1px solid rgba(51, 51, 51, .6)
}
::v-deep .el-input__inner {
  padding-left: 4px;
  padding-right: 0px;
}
::v-deep .el-checkbox__inner {
  border: 1px solid rgba(51, 51, 51, .6)
}
</style>
