node {
    stage('Preparation') {
        dir('source') {
            git credentialsId: 'robu-git', url: 'https://git.robusoft.cn/scm/road-disease.git', branch: '${BRANCH}'
        }
    }
    stage('Build') {
        dir('source') {
            nodejs('NodeJs') {
                sh 'yarn install'
                sh 'yarn run build:prod'
            }
        }
    }
    stage('Deploy') {
        dir('source') {
            ansiblePlaybook inventory: 'deploy/ansible_hosts.txt', playbook: 'deploy/ansible_playbook.yml', sudoUser: null
        }
    }
}
