import request from '@/utils/request'

/**
 * 获取平台巡检记录列表
 * @param {Object} params 请求参数
 * @param {string} [params.startTime] 起始时间，格式：2025-05-13T10:00:00
 * @param {string} [params.endTime] 截止时间，格式：2025-05-15T19:00:00
 * @param {number} [params.unitId] 单位ID
 * @returns {Promise<Object>} 响应结果
 */
export function getOrderRecordList(params) {
  return request({
    url: '/order-records/platform-list',
    method: 'get',
    params,
  })
}

/**
 * 删除手动巡检记录
 * @param {string} id 记录ID
 */
export function deleteOrderRecord(id) {
  return request({
    url: `/order-records/${id}`,
    method: 'delete',
  })
}

/**
 * 获取手动巡检记录病害列表
 * @param {*} params
 * @param {string} [params.startTime] 起始时间，格式：2025-05-13T10:00:00
 * @param {string} [params.endTime] 截止时间，格式：2025-05-15T19:00:00
 * @param {number} [params.unitId] 单位ID
 * @returns
 */
export function getOrderDamagesList(params) {
  return request({
    url: `/order-damages/record-damages`,
    method: 'get',
    params,
  })
}

/**
 * 批量导出手动巡检记录
 * @param {Object} data 请求数据
 * @param {Array<number>} data.ids 记录ID数组
 * @returns {Promise<Object>} 响应结果，包含blob数据
 */
export function exportOrderRecordBatch(data) {
  return request({
    url: '/order-records/batch-export',
    method: 'post',
    data,
    timeout: 1200 * 1000, // 20分钟超时 
    responseType: 'blob'
  })
}
