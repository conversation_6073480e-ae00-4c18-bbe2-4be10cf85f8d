/*
 * @Author: wangyj
 * @Date: 2022-06-16 17:05:47
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-07-01 16:55:27
 */
<template>
  <el-dialog :title="dialogType==='edit'?'修改运行策略':'添加运行策略'" :visible.sync="dialogFormVisible">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="top"
      label-width="120px"
    >
      <el-form-item label="策略名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入策略名称" />
      </el-form-item>
      <el-form-item label="设备工作时间" prop="time">
        <el-select v-model="temp.deviceWorkTimeOption" style="width: 120px; margin-right: 10px">
          <el-option
            v-for="item in workTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select v-show="temp.deviceWorkTimeOption == 3" v-model="temp.deviceWorkMonthDay" style="width: 120px; margin-right: 10px">
          <el-option
            v-for="item in monthDayOptions"
            :key="item"
            :label="item + '号'"
            :value="item"
          />
        </el-select>
        <el-select v-show="temp.deviceWorkTimeOption == 2" v-model="temp.deviceWorkWeekDay" style="width: 120px; margin-right: 10px">
          <el-option
            v-for="item in weekDayOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-time-picker
          v-show="temp.deviceWorkTimeOption != 0"
          v-model="timeRange"
          is-range
          format="HH:mm"
          value-format="HH:mm"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
        />
      </el-form-item>
      <el-form-item label="病害去重有效距离（m）" prop="diseaseRemoveDupDistance">
        <el-input v-model="temp.diseaseRemoveDupDistance" placeholder="有效距离" />
      </el-form-item>
      <el-form-item label="判断车辆是否偏移路线有效距离（m）" prop="routeDepartJudgeDistance">
        <el-input v-model="temp.routeDepartJudgeDistance" placeholder="有效距离" />
      </el-form-item>
      <el-form-item label="判断巡检任务是否结束设备持续未响应时长（min）" prop="workEndJudgeDuration">
        <el-input v-model="temp.workEndJudgeDuration" placeholder="响应时长" />
      </el-form-item>
      <el-form-item label="数据存储周期（天）" prop="dataClearDay">
        <el-input v-model="temp.dataClearDay" placeholder="数据存储周期" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createTactic, updateTactic } from '@/api/tactics'
import { validateNumber } from '@/utils/validate'

export default {
  props: {
    formData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      defaultTemp: {
        deviceWorkTimeOption: 0,
        deviceWorkMonthDay: 1,
        deviceWorkWeekDay: '星期一',
      },
      temp: {},
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
        diseaseRemoveDupDistance: [{
          required: true, message: '请输入病害去重有效距离', trigger: 'blur',
        }, {
          validator: validateNumber, trigger: 'blur',
        }],
        routeDepartJudgeDistance: [{
          required: true, message: '请输入判断车辆是否偏移路线有效距离', trigger: 'blur',
        }, {
          validator: validateNumber, trigger: 'blur',
        }],
        workEndJudgeDuration: [{
          required: true, message: '请输入判断巡检任务是否结束设备持续未响应时长', trigger: 'blur',
        }, {
          validator: validateNumber, trigger: 'blur',
        }],
        dataClearDay: [{
          required: true, message: '请输入数据存储周期', trigger: 'blur',
        }, {
          validator: validateNumber, trigger: 'blur',
        }],
      },
      workTypeOptions: [{
        value: 0,
        label: '开机工作',
      }, {
        value: 1,
        label: '每天',
      }, {
        value: 2,
        label: '每周',
      }, {
        value: 3,
        label: '每月',
      }],
      monthDay: '',
      weekDay: '',
      weekDayOptions: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      timeRange: null,
    }
  },
  computed: {
    monthDayOptions() {
      const arr = new Array(31)
      for (let i = 0; i < arr.length; i += 1) {
        arr[i] = i + 1
      }
      return arr
    },
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.temp = { ...this.defaultTemp, ...this.formData }
        if (this.formData.id) {
          this.dialogType = 'edit'

          if (this.temp.deviceWorkTimeOption === 1) {
            this.timeRange = this.temp.deviceWorkDaliyTime.split('-')
          } else if (this.temp.deviceWorkTimeOption === 2) {
            this.timeRange = this.temp.deviceWorkWeekDaytime.split('-')
          } else if (this.temp.deviceWorkTimeOption === 3) {
            this.timeRange = this.temp.deviceWorkMonthDaytime.split('-')
          }
        } else {
          this.dialogType = 'add'
        }
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      } else {
        this.timeRange = null
      }
    },
  },
  methods: {
    fetchPostData() {
      const {
        name,
        deviceWorkTimeOption,
        diseaseRemoveDupDistance,
        routeDepartJudgeDistance,
        workEndJudgeDuration,
        dataClearDay,
      } = this.temp
      const postData = {
        name,
        deviceWorkTimeOption,
        diseaseRemoveDupDistance,
        routeDepartJudgeDistance,
        workEndJudgeDuration,
        dataClearDay,
      }

      if (this.temp.deviceWorkTimeOption === 1) {
        if (this.timeRange) {
          postData.deviceWorkDaliyTime = this.timeRange.join('-')
        }
      } else if (this.temp.deviceWorkTimeOption === 2) {
        if (this.timeRange) {
          postData.deviceWorkWeekDaytime = this.timeRange.join('-')
        }
        postData.deviceWorkWeekDay = this.temp.deviceWorkWeekDay
      } else if (this.temp.deviceWorkTimeOption === 3) {
        if (this.timeRange) {
          postData.deviceWorkMonthDaytime = this.timeRange.join('-')
        }
        postData.deviceWorkMonthDay = this.temp.deviceWorkMonthDay
      }

      if (this.temp.id) {
        postData.id = this.temp.id
      }

      return postData
    },
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const postData = this.fetchPostData()

          createTactic(postData).then((res) => {
            this.$message({
              message: '添加成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const postData = this.fetchPostData()

          updateTactic(postData).then((res) => {
            this.$message({
              message: '修改成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    show() {
      this.dialogFormVisible = true
    },
  },
}
</script>
