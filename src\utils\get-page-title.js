/*
 * @Author: guowy
 * @Date: 2020-02-19 16:51:54
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-08-03 16:02:00
 */
import defaultSettings from '@/settings'

let title = defaultSettings.title || ''
const { hostname, host } = window.location
if (hostname === 'qinyi.yiluyun.robusoft.cn') {
  title = '翌路巡'
} else if (hostname === 'fengxun.robusoft.cn' || host === '***************:10001') {
  title = '峰巡'
} else if (hostname === 'yangluyun.robusoft.cn') {
  title = '养路云'
} else if (hostname === 'ai.guojiaomap.com') {
  // ai.guojiaomap.com
  title = 'AI巡检云平台'
} else if (hostname === 'xunluyun.robusoft.cn') {
  // xunluyun.robusoft.cn
  title = '巡路云'
} else if (hostname === 'xunjian.robusoft.cn') {
  // 盛世博业
  title = '路面智能巡检仪'
} else if (hostname === 'unicom.robusoft.cn' || host === '***************:10002') {
  // 联通数科
  title = '数字道路一体化平台'
} else if (host === '***************:10003') {
  // 北京绿道巡检管护平台
  title = '北京绿道巡检管护平台'
} else if (host === '***************:10005') {
  // 承德市政
  title = '承德市政'
} else {
  title = '智能巡检'
}
export default function getPageTitle(pageTitle) {
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return `${title}`
}
