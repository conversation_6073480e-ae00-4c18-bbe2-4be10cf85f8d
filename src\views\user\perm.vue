<template>
  <div class="app-container">
    <div class="filter-container" style="margin-bottom:30px">
      <el-button type="primary" icon="el-icon-plus" @click="openCreateRoleDialog">创建角色</el-button>
      <el-button type="primary" icon="el-icon-copy-document" @click="openCopyRoleDialog">复制角色</el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="roleList"
      border
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column type="index" align="center" label="序号" width="50" />
      <el-table-column align="center" label="角色名称" prop="name" width="400" />
      <el-table-column align="center" label="已分配权限" prop="permissions">
        <template slot-scope="{ row, $index }">
          <template v-if="row.permissions && row.permissions.length > 0">
            <div v-for="p in row.permissions" :key="p.id">"{{ p.title }}"</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="250">
        <template slot-scope="{ row, $index }">
          <el-button type="text" icon="el-icon-sort" @click="openAssignDialog(row)">分配权限</el-button>
          <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color: #f56c6c"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      :page.sync="listQuery.currentPage"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <AssignDialog ref="assignDialogRef" @refresh="onRefresh" />
    <CreateRoleDialog ref="createRoleDialogRef" @refresh="onRefresh" />
    <CopyRoleDialog ref="copyRoleDialogRef" @refresh="onRefresh" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getPermissions, getRoles, deleteRole } from '@/api/auth'
import AssignDialog from './components/assign-dialog.vue'
import CreateRoleDialog from './components/create-role-dialog.vue'
import CopyRoleDialog from './components/copy-role-dialog.vue'

export default {
  name: 'PermView',
  components: {
    Pagination, AssignDialog, CreateRoleDialog, CopyRoleDialog,
  },
  data() {
    return {
      roleList: [],
      permissionsList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        currentPage: 1,
        pageSize: 10,
      },
      dialogVisible: false,
      selectedRole: null,
    }
  },
  async created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.listLoading = true
      const rolesRes = await getRoles()
      const permRes = await getPermissions()
      this.roleList = rolesRes.payload
      this.permissionsList = permRes.payload
      // this.total = res.data.data.total
      this.listLoading = false
    },
    openAssignDialog(row) {
      const assignedPermissions = row.permissions || []
      const permissions = this.permissionsList.filter(
        (p) => !assignedPermissions.find((ap) => ap.id === p.id),
      )

      this.$refs.assignDialogRef.show({
        role: row,
        assignedPermissions,
        permissions,
      })
    },
    onRefresh() {
      this.getList()
    },
    openCreateRoleDialog() {
      this.$refs.createRoleDialogRef.show()
    },
    openCopyRoleDialog() {
      this.$refs.copyRoleDialogRef.show(this.roleList)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该角色?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        await deleteRole(row.id)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        console.error(error)
      }
    },

    handleEdit(row) {
      this.$refs.createRoleDialogRef.show(row)
    },
  },
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-table__row {
    &:hover {
      td {
        background: rgb(246, 247, 251)!important;
      }
    }
  }
</style>
