/*
 * @Author: wangyj
 * @Date: 2022-10-25 18:01:27
 * @Last Modified by: wangyj
 * @Last Modified time: 2022-11-17 10:33:06
 */
<template>
  <el-dialog :title="dialogType==='edit'?'修改设备':'添加设备'" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
    >
      <div class="form-title">设备信息</div>
      <el-form-item label="设备单位" prop="workUnitId" class="form-item-unit">
        <UnitSelect v-model="temp.workUnitId" useNodeId style="width: 100%" />
      </el-form-item>
      <el-form-item label="设备ID" prop="deviceKey">
        <el-input v-model="temp.deviceKey" placeholder="请输入设备ID" />
      </el-form-item>
      <el-form-item label="设备BSSID" prop="deviceBssid">
        <el-input v-model="temp.deviceBssid" placeholder="请输入设备BSSID" />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="temp.deviceName" placeholder="请输入设备名称" />
      </el-form-item>
      <el-form-item label="标签" prop="deviceLabel">
        <el-input v-model="temp.deviceLabel" placeholder="请输入标签" />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNum">
        <el-input v-model="temp.plateNum" placeholder="请输入车牌号" />
      </el-form-item>
      <el-form-item label="设备型号" prop="deviceType">
        <el-select v-model="temp.deviceType" clearable placeholder="请选择设备型号" filterable style="width: 100%">
          <el-option v-for="item in deviceTypes" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- 设备类型：0车载 1慢行，默认为车载设备 -->
      <el-form-item label="设备类型" prop="deviceModel">
        <el-radio v-model="temp.deviceModel" label="0">车载设备</el-radio>
        <el-radio v-model="temp.deviceModel" label="1">慢行设备</el-radio>
      </el-form-item>
      <el-form-item label="视频播放地址" prop="videoUrl">
        <el-input v-model="temp.videoUrl" placeholder="请输入视频播放地址" />
      </el-form-item>

      <div class="form-title" style="padding-top: 20px">设备关联道路信息</div>
      <el-form-item label="行政区划编码" prop="adCode">
        <el-input v-model="temp.adCode" placeholder="请输入行政区划编码" />
      </el-form-item>
      <el-form-item label="路线编码" prop="roadCode">
        <el-input v-model="temp.roadCode" placeholder="请输入路线编码" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="dialogType==='edit'?updateData():createData()">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { createDevice, updateDevice, getUsersWorkUnits } from '@/api/device'

export default {
  data() {
    return {
      temp: {
        workUnitId: null,
      },
      dialogType: 'add',
      dialogFormVisible: false,
      rules: {
        workUnitId: [{ required: true, message: '请选择单位', trigger: 'change' }],
        deviceKey: [{ required: true, message: '请输入设备ID', trigger: 'blur' }],
        deviceBssid: [{ required: true, message: '请输入设备BSSID', trigger: 'blur' }],
        deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        adCode: [{ pattern: /^[1-8][0-7]\d{4}$/, message: '行政区划编码格式不正确' }],
      },
      workUnits: [],
      deviceTypes: [
        { label: 'T506S', value: 'T506S' },
        { label: 'T805', value: 'T805' },
        { label: 'T808', value: 'T808' },
        { label: 'T906', value: 'T906' },
      ],
    }
  },
  computed: {
    ...mapState({
      admin: (state) => state.account.admin,
    }),
  },
  methods: {
    async getWorkUnits() {
      const { payload } = await getUsersWorkUnits()
      this.workUnits = payload
    },
    createData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          createDevice(this.temp).then((res) => {
            this.$message({
              message: '添加设备成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    updateData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const tempData = { ...this.temp }
          updateDevice(tempData).then(() => {
            this.$message({
              message: '修改设备成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.$emit('refreshTable')
          })
        }
      })
    },
    async show(formData = {}) {
      this.temp = { ...formData }
      if (formData.id) {
        this.dialogType = 'edit'
        if (!this.temp.deviceModel) {
          // 如果 deviceModel 为 null，则将其设置为 '0'（车载设备）
          this.$set(this.temp, 'deviceModel', '0')
        }
      } else {
        this.dialogType = 'add'
        this.$set(this.temp, 'deviceModel', '0')
      }
      
      if (this.admin) {
        await this.getWorkUnits()
        const unit = this.workUnits.find((item) => item.workUnit === this.temp.workUnit)
        if (unit) {  // 添加安全检查
          this.$set(this.temp, 'userId', unit.id)
        }
      }
      
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
  },
}
</script>
<style lang="scss" scoped>
  .form-title {
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 20px;
  }

  ::v-deep .el-dialog {
    margin-top: 8vh !important;
  }
  ::v-deep .form-item-unit {
    .el-form-item__content {
      line-height: 0px !important;
    }
  }
</style>
