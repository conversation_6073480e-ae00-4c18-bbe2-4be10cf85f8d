<!--  -->
<template>
  <div class="container">
    <baidu-map class="bm-view" :center="center" :zoom="zoom" :scroll-wheel-zoom="true" @ready="handler">
      <!-- 所有绿点       '#7EBE2A', '#28CAE3', '#705FFF', '#F4D539', '#FF1C1C'  -->
      <bm-point-collection
        :points="greenPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorA"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 所有蓝点 -->
      <bm-point-collection
        :points="bluePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorB"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 所有紫点 -->
      <bm-point-collection
        :points="purplePointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorC"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 所有黄点 -->
      <bm-point-collection
        :points="yellowPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorD"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 所有红点 -->
      <bm-point-collection
        :points="redPointData"
        shape="BMAP_POINT_SHAPE_CIRCLE"
        :color="GLOBAL_VARIATE.colorE"
        size="BMAP_POINT_SIZE_NORMAL"
      />
      <!-- 无数条线段集合，每一节颜色都不一样 -->
      <bm-polyline
        v-for="(item,id) in mapLineData"
        :key="id+item.color"
        :path="item.line"
        :stroke-color="item.color"
        :stroke-opacity="1"
        :stroke-weight="5"
      />
    </baidu-map>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { simpleStyleJson } from '@/utils/map-style'
import { getAnalysis } from '@/api/analysis'

export default {
  data() {
    return {
      map: null,
      BMap: null,
      center: { lng: 0, lat: 0 },
      zoom: 3,
      mapLineData: [],
      greenPointData: [],
      bluePointData: [],
      purplePointData: [],
      yellowPointData: [],
      redPointData: [],
    }
  },
  async created() {
    this.getAnalysisAjax()
  },

  methods: {
    handler({ BMap, map }) {
      // console.log(BMap, map)
      this.map = map
      this.BMap = BMap
      this.center.lng = 116.404
      this.center.lat = 39.915
      this.zoom = 6

      // map.setMapStyle({
      //   style: 'grayscale',
      // })
      // map.setMapStyleV2({
      //   styleId: '84641241ef00321945d05b9cb13fab3b',
      // })
      map.setMapStyleV2({
        styleJson: simpleStyleJson,
      })
    },
    async getAnalysisAjax() {
      const that = this
      that.greenPointData = []
      that.yellowPointData = []
      that.redPointData = []
      that.purplePointData = []
      that.bluePointData = []
      that.mapLineData = []

      const mapGLineData = []
      const mapBLineData = []
      const mapPLineData = []
      const mapYLineData = []
      const mapRLineData = []
      const params = {
        workUnit: '中科软',
        deviceKey: '48:b0:2d:76:2a:91',
      }
      const { payload } = await getAnalysis(params)
      console.log(payload)
      const allPoints = []
      payload.routes.forEach((route) => {
        route.forEach((point) => {
          if (point.pqi >= 90) {
            that.greenPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            mapBLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBLE_COLORS.colorA,
            })

            allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
          } else if (point.pqi >= 80 && point.pqi < 90) {
            // 蓝
            that.bluePointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            mapBLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBLE_COLORS.colorB,
            })

            allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
          } else if (point.pqi >= 70 && point.pqi < 80) {
            // 紫
            that.purplePointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            mapPLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBLE_COLORS.colorC,
            })

            allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
          } else if (point.pqi >= 60 && point.pqi < 70) {
            // 黄
            that.yellowPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            mapYLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBLE_COLORS.colorD,
            })

            allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
          } else {
            // 红
            that.redPointData.push({
              lng: point.lngStart,
              lat: point.latStart,
            }, {
              lng: point.lngEnd,
              lat: point.latEnd,
            })

            mapRLineData.push({
              line: [{
                lng: point.lngStart,
                lat: point.latStart,
              }, {
                lng: point.lngEnd,
                lat: point.latEnd,
              }],
              color: this.GLOBLE_COLORS.colorE,
            })

            allPoints.push(new this.BMap.Point(point.lngStart, point.latStart), new this.BMap.Point(point.lngEnd, point.latEnd))
          }
        })
      })

      that.mapLineData = [...mapGLineData, ...mapBLineData, ...mapPLineData, ...mapYLineData, ...mapRLineData]
      // 地图视野包含所有点
      that.$nextTick(() => {
        that.map.setViewport(allPoints)
        that.zoom = that.map.getZoom()
      })
    },
  },
}

</script>
<style lang="scss" scoped>
  .container {
    width: 100%;
    height: calc(100vh - 94px);
    position: relative;

    .bm-view {
      width: 100%;
      height: 100%;
    }
  }
</style>
