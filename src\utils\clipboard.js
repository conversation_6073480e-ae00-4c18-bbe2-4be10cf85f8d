import Message from 'element-ui/packages/message'

/**
 * 复制文本到剪贴板
 * @param {string} text 需要复制的文本
 * @param {Function} successCallback 复制成功的回调函数
 * @param {Function} errorCallback 复制失败的回调函数
 */
export function copyText(text, successCallback, errorCallback) {
  if (navigator.clipboard && window.isSecureContext) {
    // 现代浏览器API
    navigator.clipboard
      .writeText(text)
      .then(() => {
        console.log('复制成功');
        if (successCallback) {
          successCallback(text)
        } else {
          Message({
            message: '复制成功',
            type: 'success',
            duration: 2 * 1000,
          })
        }

      })
      .catch((err) => {
        if (errorCallback) {
          errorCallback(err)
        } else {
          Message({
            message: '复制失败',
            type: 'error',
            duration: 2 * 1000,
          })
        }
        console.error('复制失败: ', err)
      })
  } else {
    // 兼容旧浏览器
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      const successful = document.execCommand('copy')
      if (successful) {
        if (successCallback) {
          successCallback(text)
        } else {
          Message({
            message: '复制成功',
            type: 'success',
            duration: 2 * 1000,
          })
        }
      } else {
        if (errorCallback) {
          errorCallback(new Error('execCommand返回失败'))
        } else {
          Message({
            message: '复制失败',
            type: 'error',
            duration: 2 * 1000,
          })
        }
      }
    } catch (err) {
      if (errorCallback) {
        errorCallback(err)
      } else {
        Message.error('复制失败')
      }
      console.error('复制失败: ', err)
    }

    document.body.removeChild(textArea)
  }
} 