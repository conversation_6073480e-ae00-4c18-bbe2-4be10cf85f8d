/*
 * @Author: guowy
 * @Date: 2020-07-07 09:42:23
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-10 16:17:01
 */
const state = {
  device: 'desktop',
}
const mutations = {
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
}
const actions = {
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
