/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-03-24 16:03:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-08-14 14:34:50
 */

import request from '@/utils/request'

export function getModels(params) {
  return request({
    url: `/models`,
    method: 'get',
    params,
  })
}

export function addModel(data) {
  return request({
    url: `/models`,
    method: 'post',
    data,
  })
}

export function editModel(data) {
  return request({
    url: `/models/${data.id}`,
    method: 'put',
    data,
  })
}

export function deleteModel(id) {
  return request({
    url: `/models/${id}`,
    method: 'delete',
  })
}

// 开启模型调用
export function startModelCall(data) {
  return request({
    url: `/task-photo-remove-dups/applyModel`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

// 暂停模型调用
export function pauseModelCall(data) {
  return request({
    url: `/model-use-records/pause`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

// 查询模型启用状态
export function getModelCallStatus(params) {
  return request({
    url: `/model-use-records/queryModelUseStatus`,
    method: 'get',
    params,
    timeout: undefined,
  })
}

// 模型识别
export function modelIdentify(data) {
  return request({
    url: `/task-photo-remove-dups/${data.id}/modelIdentify`,
    method: 'post',
    data,
    timeout: undefined,
  })
}

export function downloadModelXml(data) {
  return request({
    url: `task-photo-remove-dups/downloadModelXml`,
    method: 'post',
    data,
  })
}

// 切换模型的状态
export function modelChange(params) {
  return request({
    url: `models/change`,
    method: 'get',
    params,
  })
}

/**
 * 查询配置信息
 * @param {*} params {modelId}
 * @returns
 */
export function getModelDetail(params) {
  return request({
    url: `model-infer-settings/detail`,
    method: 'get',
    params,
  })
}

/**
 * 添加/修改  置信度
 * @param {*} params
 * @returns
 */
export function modelSettings(data) {
  return request({
    url: `model-infer-settings`,
    method: 'post',
    data,
  })
}
