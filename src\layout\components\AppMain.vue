<!--
 * @Author: guowy
 * @Date: 2020-05-26 09:42:38
 * @LastEditors: guowy
 * @LastEditTime: 2020-07-03 21:35:19
-->
<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>
<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
  },
}
</script>
<style lang="scss" scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 94px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.fixed-header+.app-main {
  padding-top: 64px;
}
.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 64 + 44 */
    min-height: calc(100vh - 110px);
  }
  .fixed-header+.app-main {
    padding-top: 110px;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
