import request from '@/utils/request'

/**
 * 查询地图病害列表
 * @param {*} params
 * @param {number} params.startTime 开始日期
 * @param {number} params.endTime 结束日期
 * @param {number} params.diseaseType 病害类型
 * @param {number} params.type 病害类型
 * @returns
 */
export function getDiseaseList(params) {
  return request({
    url: 'order-damages/map-list',
    method: 'get',
    params,
  })
}

/**
 * 获取道路列表
 * @param {*} params 返回归属对应的道路集合
 * @param {number} params.belongTo 归属
 */
export function getRoadList(params) {
  return request({
    url: 'order-damages/road-list',
    method: 'get',
    params,
  })
}