/*
 * @Author: guowy
 * @Date: 2020-05-27 11:03:57
 * @LastEditors: Wangyj
 * @LastEditTime: 2023-04-06 16:56:51
 */

import request from '@/utils/request'

export function getImageInfers(query) {
  return request({
    url: '/image-infers',
    method: 'get',
    params: query,
    timeout: undefined,
  })
}

export function exportImageInfers(query) {
  return request({
    url: '/image-infers/export',
    method: 'get',
    params: query,
    timeout: undefined,
  })
}

export function getImageInfersProblem(query) {
  return request({
    url: '/image-infers/queryProblematicData',
    method: 'get',
    params: query,
    timeout: undefined,
  })
}

export function getDiseaseAccuracyStatistics(query) {
  return request({
    url: '/inspect-tasks/queryDamageStatistics',
    method: 'get',
    params: query,
    timeout: undefined,
  })
}

export function getVersionList(query) {
  return request({
    url: '/version-infoes',
    method: 'get',
    params: query,
  })
}
export function getAppDownloadUrl(query) {
  return request({
    url: '/version-infoes/queryDownloadUrl',
    method: 'get',
    params: query,
  })
}

// 查询定制单位列表
export function getCustomUnitList(query) {
  return request({
    url: '/users/queryWorkUnits',
    method: 'get',
    params: query,
  })
}

export function createAppVersion(data) {
  return request({
    url: `/version-infoes`,
    method: 'post',
    data,
  })
}
export function updateAppVersion(data) {
  return request({
    url: `/version-infoes/${data.id}`,
    method: 'put',
    data,
  })
}
export function deleteAppVersion(id) {
  return request({
    url: `/version-infoes/${id}`,
    method: 'delete',
  })
}
