<template>
  <el-dialog
    title="实时视频"
    :visible.sync="dialogVisible"
    width="45%"
    :destroy-on-close="true"
  >
    <Jessibuca v-if="dialogVisible" style="height: 460px;" :play-url="playUrl" :auto-play="true" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import Jessibuca from '@/components/Jessibuca.vue'

export default {
  components: {
    Jessibuca,
  },
  data() {
    return {
      dialogVisible: false,
      playUrl: '',
    }
  },
  methods: {
    show(playUrl) {
      this.playUrl = playUrl
      this.dialogVisible = true
    },
  },
}
</script>
